/**
 * Unit tests for NextMDXRemoteProvider
 * 
 * Tests the core functionality of the next-mdx-remote content provider
 * including content retrieval, filtering, and language support.
 */

import { NextMDXRemoteProvider } from '@/services/content/providers/next-mdx-remote'
import type { ContentItem } from '@/services/content/types'

// Mock the content bundle
jest.mock('./.mdx-compiled/content-bundle.json', () => ({
  'blog-test-post-en': {
    slug: 'test-post',
    type: 'blog',
    lang: 'en',
    title: 'Test Post',
    description: 'Test description',
    body: { html: '<p>Test content</p>', raw: 'Test content' },
    url: '/blogs/test-post',
    createdAt: '2024-01-01T00:00:00Z',
    featured: true,
    tags: ['test', 'sample'],
    author: 'Test Author'
  },
  'blog-test-post-zh': {
    slug: 'test-post',
    type: 'blog',
    lang: 'zh',
    title: '测试文章',
    description: '测试描述',
    body: { html: '<p>测试内容</p>', raw: '测试内容' },
    url: '/zh/blogs/test-post',
    createdAt: '2024-01-01T00:00:00Z',
    featured: false,
    tags: ['测试'],
    author: 'Test Author'
  },
  'product-test-product-en': {
    slug: 'test-product',
    type: 'product',
    lang: 'en',
    title: 'Test Product',
    description: 'Product description',
    body: { html: '<p>Product content</p>', raw: 'Product content' },
    url: '/products/test-product',
    createdAt: '2024-01-02T00:00:00Z',
    featured: true,
    tags: ['product'],
    author: 'Product Team'
  }
}), { virtual: true })

describe('NextMDXRemoteProvider', () => {
  let provider: NextMDXRemoteProvider

  beforeEach(() => {
    provider = new NextMDXRemoteProvider()
  })

  describe('getContent', () => {
    it('should retrieve content by type, slug, and locale', async () => {
      const content = await provider.getContent('blog', 'test-post', 'en')
      
      expect(content).toBeDefined()
      expect(content?.slug).toBe('test-post')
      expect(content?.title).toBe('Test Post')
      expect(content?.lang).toBe('en')
    })

    it('should return null for non-existent content', async () => {
      const content = await provider.getContent('blog', 'non-existent', 'en')
      
      expect(content).toBeNull()
    })

    it('should retrieve content in different languages', async () => {
      const enContent = await provider.getContent('blog', 'test-post', 'en')
      const zhContent = await provider.getContent('blog', 'test-post', 'zh')
      
      expect(enContent?.title).toBe('Test Post')
      expect(zhContent?.title).toBe('测试文章')
    })
  })

  describe('getContentList', () => {
    it('should retrieve all content of a specific type and locale', async () => {
      const blogs = await provider.getContentList('blog', 'en')
      
      expect(blogs).toHaveLength(1)
      expect(blogs[0].type).toBe('blog')
      expect(blogs[0].lang).toBe('en')
    })

    it('should filter by featured status', async () => {
      const featured = await provider.getContentList('blog', 'en', { featured: true })
      const notFeatured = await provider.getContentList('blog', 'zh', { featured: false })
      
      expect(featured).toHaveLength(1)
      expect(featured[0].featured).toBe(true)
      expect(notFeatured).toHaveLength(1)
      expect(notFeatured[0].featured).toBe(false)
    })

    it('should filter by tags', async () => {
      const withTag = await provider.getContentList('blog', 'en', { tags: ['test'] })
      const withoutTag = await provider.getContentList('blog', 'en', { tags: ['nonexistent'] })
      
      expect(withTag).toHaveLength(1)
      expect(withoutTag).toHaveLength(0)
    })

    it('should sort content', async () => {
      const sorted = await provider.getContentList('blog', 'en', {
        sortBy: 'title',
        order: 'asc'
      })
      
      expect(sorted[0].title).toBe('Test Post')
    })

    it('should limit results', async () => {
      const limited = await provider.getContentList('blog', 'en', { limit: 1 })
      
      expect(limited).toHaveLength(1)
    })
  })

  describe('getAllContentSlugs', () => {
    it('should return all slugs for a content type', async () => {
      const slugs = await provider.getAllContentSlugs('blog')
      
      expect(slugs).toHaveLength(2)
      expect(slugs).toContainEqual({ slug: 'test-post', locale: 'en' })
      expect(slugs).toContainEqual({ slug: 'test-post', locale: 'zh' })
    })
  })

  describe('contentExists', () => {
    it('should return true for existing content', async () => {
      const exists = await provider.contentExists('blog', 'test-post', 'en')
      
      expect(exists).toBe(true)
    })

    it('should return false for non-existent content', async () => {
      const exists = await provider.contentExists('blog', 'non-existent', 'en')
      
      expect(exists).toBe(false)
    })
  })

  describe('getContentTitle', () => {
    it('should return title for existing content', async () => {
      const title = await provider.getContentTitle('blog', 'test-post', 'en')
      
      expect(title).toBe('Test Post')
    })

    it('should return null for non-existent content', async () => {
      const title = await provider.getContentTitle('blog', 'non-existent', 'en')
      
      expect(title).toBeNull()
    })
  })

  describe('getAvailableLanguages', () => {
    it('should return available languages for content', async () => {
      const languages = await provider.getAvailableLanguages('blog', 'test-post')
      
      expect(languages).toHaveLength(2)
      expect(languages[0]).toMatchObject({
        lang: 'en',
        title: 'Test Post',
        available: true
      })
      expect(languages[1]).toMatchObject({
        lang: 'zh',
        title: '测试文章',
        available: true
      })
    })

    it('should mark unavailable languages', async () => {
      const languages = await provider.getAvailableLanguages('product', 'test-product')
      
      expect(languages).toHaveLength(2)
      expect(languages[0].available).toBe(true) // en
      expect(languages[1].available).toBe(false) // zh not available
    })
  })
})