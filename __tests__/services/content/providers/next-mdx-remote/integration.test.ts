/**
 * Integration tests for NextMDXRemoteProvider with ContentService
 * 
 * Tests the integration between the next-mdx-remote provider and the 
 * content management service including content queries, language switching,
 * and static generation features.
 */

import { ContentService } from '@/services/content/core/content-service'
import { NextMDXRemoteProvider } from '@/services/content/providers/next-mdx-remote'
import type { ContentServiceConfig, ContentItem } from '@/services/content/types'

// Mock the content bundle with comprehensive test data
jest.mock('./.mdx-compiled/content-bundle.json', () => ({
  'blog-getting-started-en': {
    slug: 'getting-started',
    type: 'blog',
    lang: 'en',
    title: 'Getting Started with ShipAny',
    description: 'Learn how to build and deploy your first application',
    body: { 
      html: '<h1>Getting Started</h1><p>Welcome to ShipAny!</p>', 
      raw: '# Getting Started\n\nWelcome to ShipAny!' 
    },
    url: '/blogs/getting-started',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
    featured: true,
    tags: ['tutorial', 'getting-started'],
    author: 'ShipAny Team',
    category: 'Tutorial'
  },
  'blog-getting-started-zh': {
    slug: 'getting-started',
    type: 'blog',
    lang: 'zh',
    title: 'ShipAny 快速入门',
    description: '学习如何构建和部署您的第一个应用',
    body: { 
      html: '<h1>快速入门</h1><p>欢迎使用 ShipAny！</p>', 
      raw: '# 快速入门\n\n欢迎使用 ShipAny！' 
    },
    url: '/zh/blogs/getting-started',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
    featured: true,
    tags: ['教程', '入门'],
    author: 'ShipAny 团队',
    category: '教程'
  },
  'blog-advanced-features-en': {
    slug: 'advanced-features',
    type: 'blog',
    lang: 'en',
    title: 'Advanced Features',
    description: 'Explore advanced features and capabilities',
    body: { 
      html: '<h1>Advanced Features</h1><p>Deep dive into advanced features.</p>', 
      raw: '# Advanced Features\n\nDeep dive into advanced features.' 
    },
    url: '/blogs/advanced-features',
    createdAt: '2024-02-01T00:00:00Z',
    featured: false,
    tags: ['advanced', 'features'],
    author: 'Tech Team'
  },
  'product-ai-assistant-en': {
    slug: 'ai-assistant',
    type: 'product',
    lang: 'en',
    title: 'AI Assistant',
    description: 'Powerful AI assistant for your workflow',
    body: { 
      html: '<h1>AI Assistant</h1><p>Enhance productivity with AI.</p>', 
      raw: '# AI Assistant\n\nEnhance productivity with AI.' 
    },
    url: '/products/ai-assistant',
    createdAt: '2024-01-10T00:00:00Z',
    featured: true,
    tags: ['ai', 'productivity'],
    author: 'Product Team'
  },
  'case-study-techcorp-en': {
    slug: 'techcorp-transformation',
    type: 'case-study',
    lang: 'en',
    title: 'TechCorp Digital Transformation',
    description: 'How TechCorp transformed their business with AI',
    body: { 
      html: '<h1>TechCorp Case Study</h1><p>A success story.</p>', 
      raw: '# TechCorp Case Study\n\nA success story.' 
    },
    url: '/case-studies/techcorp-transformation',
    createdAt: '2024-01-20T00:00:00Z',
    featured: true,
    tags: ['success-story', 'enterprise'],
    author: 'Case Study Team'
  }
}), { virtual: true })

describe('NextMDXRemoteProvider Integration', () => {
  let contentService: ContentService
  let config: ContentServiceConfig

  beforeEach(() => {
    // Create a configuration for testing
    config = {
      provider: 'next-mdx-remote',
      contentTypes: ['blog', 'product', 'case-study'],
      defaultLocale: 'en',
      supportedLocales: ['en', 'zh'],
      features: {
        seo: true,
        relatedContent: true,
        languageSwitching: true,
        staticGeneration: true
      },
      baseUrl: 'https://example.com'
    }

    // Initialize content service with NextMDXRemoteProvider
    contentService = new ContentService(config)
  })

  describe('Content Queries', () => {
    it('should query content with filters', async () => {
      const content = await contentService.queryContent({
        type: 'blog',
        locale: 'en',
        featured: true
      })

      expect(content).toHaveLength(1)
      expect(content[0].slug).toBe('getting-started')
      expect(content[0].featured).toBe(true)
    })

    it('should support tag filtering', async () => {
      const content = await contentService.queryContent({
        type: 'blog',
        locale: 'en',
        tags: ['tutorial']
      })

      expect(content).toHaveLength(1)
      expect(content[0].tags).toContain('tutorial')
    })

    it('should handle sorting', async () => {
      const content = await contentService.queryContent({
        type: 'blog',
        locale: 'en',
        sortBy: 'createdAt',
        order: 'desc'
      })

      expect(content).toHaveLength(2)
      expect(content[0].slug).toBe('advanced-features')
      expect(content[1].slug).toBe('getting-started')
    })

    it('should support pagination', async () => {
      const content = await contentService.queryContent({
        type: 'blog',
        locale: 'en',
        limit: 1,
        offset: 1
      })

      expect(content).toHaveLength(1)
      expect(content[0].slug).toBe('advanced-features')
    })
  })

  describe('Language Features', () => {
    it('should get available language versions', async () => {
      const versions = await contentService.getAvailableLanguageVersions(
        'blog',
        'getting-started',
        'en'
      )

      expect(versions).toHaveLength(2)
      expect(versions[0]).toMatchObject({
        lang: 'en',
        title: 'Getting Started with ShipAny',
        available: true
      })
      expect(versions[1]).toMatchObject({
        lang: 'zh',
        title: 'ShipAny 快速入门',
        available: true
      })
    })

    it('should detect content language availability', async () => {
      const hasEnglish = await contentService.hasLanguageVersion(
        'blog',
        'getting-started',
        'en'
      )
      const hasChinese = await contentService.hasLanguageVersion(
        'blog',
        'getting-started',
        'zh'
      )
      const hasFrench = await contentService.hasLanguageVersion(
        'blog',
        'getting-started',
        'fr'
      )

      expect(hasEnglish).toBe(true)
      expect(hasChinese).toBe(true)
      expect(hasFrench).toBe(false)
    })

    it('should switch language context', async () => {
      const englishContent = await contentService.getContent('blog', 'getting-started', 'en')
      const chineseContent = await contentService.getContent('blog', 'getting-started', 'zh')

      expect(englishContent?.title).toBe('Getting Started with ShipAny')
      expect(chineseContent?.title).toBe('ShipAny 快速入门')
    })
  })

  describe('Related Content', () => {
    it('should find related content by tags', async () => {
      const content = await contentService.getContent('blog', 'getting-started', 'en')
      const related = await contentService.getRelatedContent(content!)

      expect(related).toHaveLength(0) // No other content shares 'tutorial' tag
    })

    it('should find content recommendations', async () => {
      const content = await contentService.getContent('blog', 'advanced-features', 'en')
      const recommendations = await contentService.getContentRecommendations(content!)

      expect(recommendations.length).toBeGreaterThan(0)
      expect(recommendations[0].type).toBe('blog')
    })
  })

  describe('Static Generation', () => {
    it('should generate static params for all content', async () => {
      const params = await contentService.generateStaticParams('blog')

      expect(params).toHaveLength(3) // 2 en + 1 zh
      expect(params).toContainEqual({ 
        slug: 'getting-started', 
        locale: 'en' 
      })
      expect(params).toContainEqual({ 
        slug: 'getting-started', 
        locale: 'zh' 
      })
      expect(params).toContainEqual({ 
        slug: 'advanced-features', 
        locale: 'en' 
      })
    })

    it('should get content by URL path', async () => {
      const content = await contentService.getContentByPath(
        '/blogs/getting-started',
        'en'
      )

      expect(content).toBeDefined()
      expect(content?.slug).toBe('getting-started')
      expect(content?.type).toBe('blog')
    })
  })

  describe('SEO Features', () => {
    it('should generate SEO metadata', async () => {
      const content = await contentService.getContent('blog', 'getting-started', 'en')
      const metadata = await contentService.generateSEOMetadata(content!)

      expect(metadata.title).toBe('Getting Started with ShipAny')
      expect(metadata.description).toBe('Learn how to build and deploy your first application')
      expect(metadata.openGraph?.title).toBe('Getting Started with ShipAny')
      expect(metadata.twitter?.card).toBe('summary_large_image')
    })

    it('should generate sitemap entries', async () => {
      const entries = await contentService.generateSitemapEntries()

      expect(entries.length).toBeGreaterThan(0)
      expect(entries).toContainEqual(
        expect.objectContaining({
          url: 'https://example.com/blogs/getting-started',
          lastModified: new Date('2024-01-15T00:00:00Z')
        })
      )
    })

    it('should generate RSS feed data', async () => {
      const feedData = await contentService.generateRSSFeed('en')

      expect(feedData.items).toHaveLength(2) // 2 English blogs
      expect(feedData.items[0]).toMatchObject({
        title: 'Advanced Features',
        link: 'https://example.com/blogs/advanced-features',
        pubDate: new Date('2024-02-01T00:00:00Z')
      })
    })
  })

  describe('Content Types', () => {
    it('should handle different content types', async () => {
      const blog = await contentService.getContent('blog', 'getting-started', 'en')
      const product = await contentService.getContent('product', 'ai-assistant', 'en')
      const caseStudy = await contentService.getContent('case-study', 'techcorp-transformation', 'en')

      expect(blog?.type).toBe('blog')
      expect(product?.type).toBe('product')
      expect(caseStudy?.type).toBe('case-study')
    })

    it('should query across content types', async () => {
      const allFeatured = await contentService.queryContent({
        featured: true,
        locale: 'en'
      })

      expect(allFeatured).toHaveLength(3) // 1 blog + 1 product + 1 case-study
      expect(allFeatured.map(c => c.type)).toContain('blog')
      expect(allFeatured.map(c => c.type)).toContain('product')
      expect(allFeatured.map(c => c.type)).toContain('case-study')
    })
  })

  describe('Error Handling', () => {
    it('should handle non-existent content gracefully', async () => {
      const content = await contentService.getContent('blog', 'non-existent', 'en')
      expect(content).toBeNull()
    })

    it('should handle invalid content types', async () => {
      const content = await contentService.queryContent({
        type: 'invalid' as any,
        locale: 'en'
      })
      expect(content).toEqual([])
    })

    it('should validate content exists before operations', async () => {
      const exists = await contentService.contentExists('blog', 'getting-started', 'en')
      const notExists = await contentService.contentExists('blog', 'non-existent', 'en')

      expect(exists).toBe(true)
      expect(notExists).toBe(false)
    })
  })
})