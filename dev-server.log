
> shipany-template-one@2.6.0 dev /home/<USER>/workspace/shipany-stwd
> run-s build:content dev:parallel


> shipany-template-one@2.6.0 build:content /home/<USER>/workspace/shipany-stwd
> tsx scripts/build-content.ts

[Content Build] Provider: next-mdx-remote
[Content Build] Mode: build
[Content Build] Running MDX build...
[MDX Build] Starting build process...
[MDX Build] Found 13 MDX files
[MDX Build] Invalid file structure: content/docs/index.mdx
[MDX Build] Invalid file structure: content/docs/quickstart.zh.mdx
[MDX Build] Compiled: blog-getting-started-with-shipany-en
[MDX Build] Compiled: blog-english-only-test-en
[MDX Build] Compiled: blog-shipany-kuai-su-ru-men-zh
[MDX Build] Invalid file structure: content/docs/index.zh.mdx
[MDX Build] Compiled: case-study-techcorp-ai-transformation-en
[MDX Build] Compiled: case-study-keji-gongsi-ai-zhuanxing-zh
[MDX Build] Invalid file structure: content/docs/quickstart.mdx
[MDX Build] Compiled: product-ai-content-generator-en
[MDX Build] Compiled: product-video-demo-example-en
[MDX Build] Compiled: product-ai-nei-rong-sheng-cheng-qi-zh
[MDX Build] Compiled: product-video-demo-example-zh
[MDX Build] Written full bundle to .mdx-compiled/content-bundle.json
[MDX Build] Written minified bundle to .mdx-compiled/content-bundle.min.json
[MDX Build] ✓ Build completed in 16ms

> shipany-template-one@2.6.0 dev:parallel /home/<USER>/workspace/shipany-stwd
> run-p dev:next dev:watch


> shipany-template-one@2.6.0 dev:next /home/<USER>/workspace/shipany-stwd
> cross-env NODE_NO_WARNINGS=1 next dev --turbopack


> shipany-template-one@2.6.0 dev:watch /home/<USER>/workspace/shipany-stwd
> tsx scripts/build-content.ts --watch

[Content Build] Provider: next-mdx-remote
[Content Build] Mode: watch
[Content Build] Running MDX build...
[MDX Build] Starting build process...
[MDX Build] Found 13 MDX files
[MDX Build] Invalid file structure: content/docs/index.mdx
[MDX Build] Invalid file structure: content/docs/index.zh.mdx
[MDX Build] Invalid file structure: content/docs/quickstart.mdx
[MDX Build] Invalid file structure: content/docs/quickstart.zh.mdx
[MDX Build] Compiled: blog-english-only-test-en
[MDX Build] Compiled: blog-getting-started-with-shipany-en
[MDX Build] Compiled: blog-shipany-kuai-su-ru-men-zh
[MDX Build] Compiled: case-study-techcorp-ai-transformation-en
[MDX Build] Compiled: case-study-keji-gongsi-ai-zhuanxing-zh
[MDX Build] Compiled: product-ai-content-generator-en
[MDX Build] Compiled: product-video-demo-example-en
[MDX Build] Compiled: product-ai-nei-rong-sheng-cheng-qi-zh
[MDX Build] Compiled: product-video-demo-example-zh
[MDX Build] Written full bundle to .mdx-compiled/content-bundle.json
[MDX Build] Written minified bundle to .mdx-compiled/content-bundle.min.json
[MDX Build] ✓ Build completed in 15ms
[MDX Build] Starting watch mode...
[MDX] updated map file in 5.402629000000047ms
[MDX] started dev server
   ▲ Next.js 15.2.3 (Turbopack)
   - Local:        http://localhost:3000
   - Network:      http://**************:3000
   - Environments: .env.development
   - Experiments (use with caution):
     · turbo

 ✓ Starting...
 ⚠ Invalid next.config.mjs options detected: 
 ⚠     Unrecognized key(s) in object: 'turbopack'
 ⚠ See more info here: https://nextjs.org/docs/messages/invalid-next-config
 ✓ Compiled in 266ms
 ✓ Ready in 2.1s
 ○ Compiling /[locale]/blogs ...
 ✓ Compiled /[locale]/blogs in 4.6s
 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


[NextMDXRemoteProvider] Loading content bundle...
[NextMDXRemoteProvider] NODE_ENV: development
[NextMDXRemoteProvider] isWorkerEnv: false
[NextMDXRemoteProvider] Loading development bundle...
[NextMDXRemoteProvider] Failed to load content bundle: Error: Cannot find module '../../../../.mdx-compiled/content-bundle.json'
    at <unknown> (.next/server/chunks/ssr/_9877f192._.js:438:31)
    at NextMDXRemoteProvider.loadCompiledContent (.next/server/chunks/ssr/_9877f192._.js:441:19)
    at new NextMDXRemoteProvider (src/services/content/providers/next-mdx-remote/provider.ts:41:9)
    at ProviderFactory.create (src/services/content/core/provider-factory.ts:39:15)
    at [project]/src/services/content/index.ts [app-rsc] (ecmascript) <locals> (src/services/content/index.ts:20:33)
    at [project]/src/services/content/index.ts [app-rsc] (ecmascript) <module evaluation> (.next/server/chunks/ssr/_9877f192._.js:2381:177)
    at [project]/src/app/[locale]/(default)/blogs/page.tsx [app-rsc] (ecmascript) (src/app/[locale]/(default)/blogs/page.tsx:15:0)
    at [project]/src/app/[locale]/(default)/blogs/page.tsx [app-rsc] (ecmascript, Next.js server component) (.next/server/chunks/ssr/_9877f192._.js:2648:47)
    at Object.<anonymous> (.next/server/app/[locale]/(default)/blogs/page.js:17:9)
  39 |     
  40 |     // Load compiled content
> 41 |     this.loadCompiledContent()
     |         ^
  42 |   }
  43 |   
  44 |   /** {
  code: 'MODULE_NOT_FOUND'
}
[NextMDXRemoteProvider] Run "pnpm build:content" to generate content bundle
[NextMDXRemoteProvider] Loading content bundle...
[NextMDXRemoteProvider] NODE_ENV: development
[NextMDXRemoteProvider] isWorkerEnv: false
[NextMDXRemoteProvider] Loading development bundle...
[NextMDXRemoteProvider] Failed to load content bundle: Error: Cannot find module '../../../../.mdx-compiled/content-bundle.json'
    at <unknown> (.next/server/chunks/ssr/_9877f192._.js:438:31)
    at NextMDXRemoteProvider.loadCompiledContent (.next/server/chunks/ssr/_9877f192._.js:441:19)
    at new NextMDXRemoteProvider (src/services/content/providers/next-mdx-remote/provider.ts:41:9)
    at ProviderFactory.create (src/services/content/core/provider-factory.ts:39:15)
    at [project]/src/services/content/index.ts [app-rsc] (ecmascript) <locals> (src/services/content/index.ts:20:33)
    at instantiateModule (.next/server/chunks/ssr/[turbopack]_runtime.js:593:23)
    at getOrInstantiateModuleFromParent (.next/server/chunks/ssr/[turbopack]_runtime.js:652:12)
    at esmImport (.next/server/chunks/ssr/[turbopack]_runtime.js:132:20)
    at [project]/src/services/content/index.ts [app-rsc] (ecmascript) <module evaluation> (.next/server/chunks/ssr/_9877f192._.js:2381:177)
    at instantiateModule (.next/server/chunks/ssr/[turbopack]_runtime.js:593:23)
  39 |     
  40 |     // Load compiled content
> 41 |     this.loadCompiledContent()
     |         ^
  42 |   }
  43 |   
  44 |   /** {
  code: 'MODULE_NOT_FOUND'
}
[NextMDXRemoteProvider] Run "pnpm build:content" to generate content bundle
[BlogsPage] Fetching blogs for locale: en
[NextMDXRemoteProvider] getContentList called with: {
  type: 'blog',
  locale: 'en',
  options: { sortBy: 'publishedAt', order: 'desc' }
}
[NextMDXRemoteProvider] Total items in contentMap: 0
[NextMDXRemoteProvider] Items after type/locale filter: 0
[BlogsPage] Found 0 blogs
[NextMDXRemoteProvider] Loading content bundle...
[NextMDXRemoteProvider] NODE_ENV: development
[NextMDXRemoteProvider] isWorkerEnv: false
[NextMDXRemoteProvider] Loading development bundle...
[NextMDXRemoteProvider] Failed to load content bundle: Error: Cannot find module '../../../../.mdx-compiled/content-bundle.json'
    at eval (.next/server/chunks/ssr/_7405ebe0._.js:1017:31)
    at NextMDXRemoteProvider.loadCompiledContent (.next/server/chunks/ssr/_7405ebe0._.js:1020:19)
    at new NextMDXRemoteProvider (src/services/content/providers/next-mdx-remote/provider.ts:41:9)
    at ProviderFactory.create (src/services/content/core/provider-factory.ts:39:15)
    at [project]/src/services/content/index.ts [app-ssr] (ecmascript) <locals> (src/services/content/index.ts:20:33)
    at [project]/src/services/content/index.ts [app-ssr] (ecmascript) <module evaluation> (.next/server/chunks/ssr/_7405ebe0._.js:2960:177)
    at [project]/src/components/locale/toggle.tsx [app-ssr] (ecmascript) (src/components/locale/toggle.tsx:14:0)
    at [project]/src/components/blocks/header/index.tsx [app-ssr] (ecmascript) (src/components/blocks/header/index.tsx:30:0)
  39 |     
  40 |     // Load compiled content
> 41 |     this.loadCompiledContent()
     |         ^
  42 |   }
  43 |   
  44 |   /** {
  code: 'MODULE_NOT_FOUND'
}
[NextMDXRemoteProvider] Run "pnpm build:content" to generate content bundle
 GET /blogs 200 in 6155ms
 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 ○ Compiling /api/auth/[...nextauth] ...
 ✓ Compiled /api/auth/[...nextauth] in 936ms
 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 GET /api/auth/session 200 in 1485ms
 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 GET /api/auth/session 200 in 67ms
 ○ Compiling /api/get-user-info ...
 ✓ Compiled /api/get-user-info in 629ms
 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


[BlogsPage] Fetching blogs for locale: en
[NextMDXRemoteProvider] getContentList called with: {
  type: 'blog',
  locale: 'en',
  options: { sortBy: 'publishedAt', order: 'desc' }
}
[NextMDXRemoteProvider] Total items in contentMap: 0
[NextMDXRemoteProvider] Items after type/locale filter: 0
[BlogsPage] Found 0 blogs
 POST /api/get-user-info 200 in 981ms
 POST /api/get-user-info 200 in 905ms
 GET /blogs 200 in 139ms
[NextMDXRemoteProvider] Loading content bundle...
[NextMDXRemoteProvider] NODE_ENV: development
[NextMDXRemoteProvider] isWorkerEnv: false
[NextMDXRemoteProvider] Loading development bundle...
[NextMDXRemoteProvider] Failed to load content bundle: Error: Cannot find module '../../../../.mdx-compiled/content-bundle.json'
    at <unknown> (.next/server/chunks/ssr/_9877f192._.js:438:31)
    at NextMDXRemoteProvider.loadCompiledContent (.next/server/chunks/ssr/_9877f192._.js:441:19)
    at new NextMDXRemoteProvider (src/services/content/providers/next-mdx-remote/provider.ts:41:9)
    at ProviderFactory.create (src/services/content/core/provider-factory.ts:39:15)
    at [project]/src/services/content/index.ts [app-rsc] (ecmascript) <locals> (src/services/content/index.ts:20:33)
    at instantiateModule (.next/server/chunks/ssr/[turbopack]_runtime.js:593:23)
    at getOrInstantiateModuleFromParent (.next/server/chunks/ssr/[turbopack]_runtime.js:652:12)
    at esmImport (.next/server/chunks/ssr/[turbopack]_runtime.js:132:20)
    at [project]/src/services/content/index.ts [app-rsc] (ecmascript) <module evaluation> (.next/server/chunks/ssr/_9877f192._.js:2381:177)
    at instantiateModule (.next/server/chunks/ssr/[turbopack]_runtime.js:593:23)
  39 |     
  40 |     // Load compiled content
> 41 |     this.loadCompiledContent()
     |         ^
  42 |   }
  43 |   
  44 |   /** {
  code: 'MODULE_NOT_FOUND'
}
[NextMDXRemoteProvider] Run "pnpm build:content" to generate content bundle
 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 GET /api/auth/session 200 in 92ms
 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 GET /api/auth/session 200 in 56ms
 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


[BlogsPage] Fetching blogs for locale: en
[NextMDXRemoteProvider] getContentList called with: {
  type: 'blog',
  locale: 'en',
  options: { sortBy: 'publishedAt', order: 'desc' }
}
[NextMDXRemoteProvider] Total items in contentMap: 0
[NextMDXRemoteProvider] Items after type/locale filter: 0
[BlogsPage] Found 0 blogs
 POST /api/get-user-info 200 in 1641ms
 POST /api/get-user-info 200 in 1722ms
 GET /blogs 200 in 125ms
[NextMDXRemoteProvider] Loading content bundle...
[NextMDXRemoteProvider] NODE_ENV: development
[NextMDXRemoteProvider] isWorkerEnv: false
[NextMDXRemoteProvider] Loading development bundle...
[NextMDXRemoteProvider] Failed to load content bundle: Error: Cannot find module '../../../../.mdx-compiled/content-bundle.json'
    at <unknown> (.next/server/chunks/ssr/_9877f192._.js:438:31)
    at NextMDXRemoteProvider.loadCompiledContent (.next/server/chunks/ssr/_9877f192._.js:441:19)
    at new NextMDXRemoteProvider (src/services/content/providers/next-mdx-remote/provider.ts:41:9)
    at ProviderFactory.create (src/services/content/core/provider-factory.ts:39:15)
    at [project]/src/services/content/index.ts [app-rsc] (ecmascript) <locals> (src/services/content/index.ts:20:33)
    at instantiateModule (.next/server/chunks/ssr/[turbopack]_runtime.js:593:23)
    at getOrInstantiateModuleFromParent (.next/server/chunks/ssr/[turbopack]_runtime.js:652:12)
    at esmImport (.next/server/chunks/ssr/[turbopack]_runtime.js:132:20)
    at [project]/src/services/content/index.ts [app-rsc] (ecmascript) <module evaluation> (.next/server/chunks/ssr/_9877f192._.js:2381:177)
    at instantiateModule (.next/server/chunks/ssr/[turbopack]_runtime.js:593:23)
  39 |     
  40 |     // Load compiled content
> 41 |     this.loadCompiledContent()
     |         ^
  42 |   }
  43 |   
  44 |   /** {
  code: 'MODULE_NOT_FOUND'
}
[NextMDXRemoteProvider] Run "pnpm build:content" to generate content bundle
 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 GET /api/auth/session 200 in 89ms
 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 GET /api/auth/session 200 in 54ms
 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 POST /api/get-user-info 200 in 1274ms
 POST /api/get-user-info 200 in 1711ms
 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 GET /api/auth/session 200 in 58ms
 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 GET /api/auth/session 200 in 43ms
 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 POST /api/get-user-info 200 in 1275ms
 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 POST /api/get-user-info 200 in 1279ms
[BlogsPage] Fetching blogs for locale: en
[NextMDXRemoteProvider] getContentList called with: {
  type: 'blog',
  locale: 'en',
  options: { sortBy: 'publishedAt', order: 'desc' }
}
[NextMDXRemoteProvider] Total items in contentMap: 0
[NextMDXRemoteProvider] Items after type/locale filter: 0
[BlogsPage] Found 0 blogs
 GET /blogs 200 in 138ms
[NextMDXRemoteProvider] Loading content bundle...
[NextMDXRemoteProvider] NODE_ENV: development
[NextMDXRemoteProvider] isWorkerEnv: false
[NextMDXRemoteProvider] Loading development bundle...
[NextMDXRemoteProvider] Failed to load content bundle: Error: Cannot find module '../../../../.mdx-compiled/content-bundle.json'
    at <unknown> (.next/server/chunks/ssr/_9877f192._.js:438:31)
    at NextMDXRemoteProvider.loadCompiledContent (.next/server/chunks/ssr/_9877f192._.js:441:19)
    at new NextMDXRemoteProvider (src/services/content/providers/next-mdx-remote/provider.ts:41:9)
    at ProviderFactory.create (src/services/content/core/provider-factory.ts:39:15)
    at [project]/src/services/content/index.ts [app-rsc] (ecmascript) <locals> (src/services/content/index.ts:20:33)
    at instantiateModule (.next/server/chunks/ssr/[turbopack]_runtime.js:593:23)
    at getOrInstantiateModuleFromParent (.next/server/chunks/ssr/[turbopack]_runtime.js:652:12)
    at esmImport (.next/server/chunks/ssr/[turbopack]_runtime.js:132:20)
    at [project]/src/services/content/index.ts [app-rsc] (ecmascript) <module evaluation> (.next/server/chunks/ssr/_9877f192._.js:2381:177)
    at instantiateModule (.next/server/chunks/ssr/[turbopack]_runtime.js:593:23)
  39 |     
  40 |     // Load compiled content
> 41 |     this.loadCompiledContent()
     |         ^
  42 |   }
  43 |   
  44 |   /** {
  code: 'MODULE_NOT_FOUND'
}
[NextMDXRemoteProvider] Run "pnpm build:content" to generate content bundle
 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 GET /api/auth/session 200 in 78ms
 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 GET /api/auth/session 200 in 58ms
 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 POST /api/get-user-info 200 in 1266ms
 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 GET /api/auth/session 200 in 62ms
 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 GET /api/auth/session 200 in 46ms
 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:70:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 70 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 71 |[39m       }[0m
[0m [90m 72 |[39m       [0m
[0m [90m 73 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:66:18
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 64 |[39m         [90m// In production/worker env, use minified bundle[39m[0m
[0m [90m 65 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m[31m[1m>[22m[39m[90m 66 |[39m         bundle [33m=[39m require([32m'../../../../.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 67 |[39m       } [36melse[39m {[0m
[0m [90m 68 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 69 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 POST /api/get-user-info 200 in 1677ms
 POST /api/get-user-info 200 in 1297ms
 POST /api/get-user-info 200 in 1246ms
 ⨯ ./src/services/content/providers/next-mdx-remote
Module not found: Can't resolve './ROOT/.mdx-compiled/content-bundle.json'
server relative imports are not implemented yet. Please try an import relative to the file you are importing from.


https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote
Module not found: Can't resolve './ROOT/.mdx-compiled/content-bundle.json'
server relative imports are not implemented yet. Please try an import relative to the file you are importing from.


https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote
Module not found: Can't resolve './ROOT/.mdx-compiled/content-bundle.min.json'
server relative imports are not implemented yet. Please try an import relative to the file you are importing from.


https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote
Module not found: Can't resolve './ROOT/.mdx-compiled/content-bundle.min.json'
server relative imports are not implemented yet. Please try an import relative to the file you are importing from.


https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote
Module not found: Can't resolve './ROOT/.mdx-compiled/content-bundle.json'
server relative imports are not implemented yet. Please try an import relative to the file you are importing from.


https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote
Module not found: Can't resolve './ROOT/.mdx-compiled/content-bundle.json'
server relative imports are not implemented yet. Please try an import relative to the file you are importing from.


https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote
Module not found: Can't resolve './ROOT/.mdx-compiled/content-bundle.min.json'
server relative imports are not implemented yet. Please try an import relative to the file you are importing from.


https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote
Module not found: Can't resolve './ROOT/.mdx-compiled/content-bundle.min.json'
server relative imports are not implemented yet. Please try an import relative to the file you are importing from.


https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote
Module not found: Can't resolve './ROOT/.mdx-compiled/content-bundle.json'
server relative imports are not implemented yet. Please try an import relative to the file you are importing from.


https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote
Module not found: Can't resolve './ROOT/.mdx-compiled/content-bundle.json'
server relative imports are not implemented yet. Please try an import relative to the file you are importing from.


https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote
Module not found: Can't resolve './ROOT/.mdx-compiled/content-bundle.min.json'
server relative imports are not implemented yet. Please try an import relative to the file you are importing from.


https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote
Module not found: Can't resolve './ROOT/.mdx-compiled/content-bundle.min.json'
server relative imports are not implemented yet. Please try an import relative to the file you are importing from.


https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote
Module not found: Can't resolve './ROOT/.mdx-compiled/content-bundle.json'
server relative imports are not implemented yet. Please try an import relative to the file you are importing from.


https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote
Module not found: Can't resolve './ROOT/.mdx-compiled/content-bundle.json'
server relative imports are not implemented yet. Please try an import relative to the file you are importing from.


https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote
Module not found: Can't resolve './ROOT/.mdx-compiled/content-bundle.min.json'
server relative imports are not implemented yet. Please try an import relative to the file you are importing from.


https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote
Module not found: Can't resolve './ROOT/.mdx-compiled/content-bundle.min.json'
server relative imports are not implemented yet. Please try an import relative to the file you are importing from.


https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote
Module not found: Can't resolve './ROOT/.mdx-compiled/content-bundle.json'
server relative imports are not implemented yet. Please try an import relative to the file you are importing from.


https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote
Module not found: Can't resolve './ROOT/.mdx-compiled/content-bundle.json'
server relative imports are not implemented yet. Please try an import relative to the file you are importing from.


https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote
Module not found: Can't resolve './ROOT/.mdx-compiled/content-bundle.min.json'
server relative imports are not implemented yet. Please try an import relative to the file you are importing from.


https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote
Module not found: Can't resolve './ROOT/.mdx-compiled/content-bundle.min.json'
server relative imports are not implemented yet. Please try an import relative to the file you are importing from.


https://nextjs.org/docs/messages/module-not-found


 ✓ Compiled in 326ms
 ⨯ ./src/services/content/providers/next-mdx-remote
Module not found: Can't resolve './ROOT/.mdx-compiled/content-bundle.json'
server relative imports are not implemented yet. Please try an import relative to the file you are importing from.


https://nextjs.org/docs/messages/module-not-found



./src/services/content/providers/next-mdx-remote
Module not found: Can't resolve './ROOT/.mdx-compiled/content-bundle.min.json'
server relative imports are not implemented yet. Please try an import relative to the file you are importing from.


https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:74:18
Module not found: Can't resolve '/ROOT/.mdx-compiled/content-bundle.json'
[0m [90m 72 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m [90m 73 |[39m         [36mconst[39m bundlePath [33m=[39m path[33m.[39mjoin(projectRoot[33m,[39m [32m'.mdx-compiled/content-bundle.json'[39m)[0m
[0m[31m[1m>[22m[39m[90m 74 |[39m         bundle [33m=[39m require(bundlePath)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 75 |[39m       }[0m
[0m [90m 76 |[39m       [0m
[0m [90m 77 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:69:18
Module not found: Can't resolve '/ROOT/.mdx-compiled/content-bundle.min.json'
[0m [90m 67 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m [90m 68 |[39m         [36mconst[39m bundlePath [33m=[39m path[33m.[39mjoin(projectRoot[33m,[39m [32m'.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m[31m[1m>[22m[39m[90m 69 |[39m         bundle [33m=[39m require(bundlePath)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 70 |[39m       } [36melse[39m {[0m
[0m [90m 71 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 72 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:74:18
Module not found: Can't resolve <dynamic>
[0m [90m 72 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m [90m 73 |[39m         [36mconst[39m bundlePath [33m=[39m path[33m.[39mjoin(projectRoot[33m,[39m [32m'.mdx-compiled/content-bundle.json'[39m)[0m
[0m[31m[1m>[22m[39m[90m 74 |[39m         bundle [33m=[39m require(bundlePath)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 75 |[39m       }[0m
[0m [90m 76 |[39m       [0m
[0m [90m 77 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ○ Compiling /_error ...
 ✓ Compiled /_error in 710ms
 GET /blogs 500 in 841ms
 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:74:18
Module not found: Can't resolve '/ROOT/.mdx-compiled/content-bundle.json'
[0m [90m 72 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m [90m 73 |[39m         [36mconst[39m bundlePath [33m=[39m path[33m.[39mjoin(projectRoot[33m,[39m [32m'.mdx-compiled/content-bundle.json'[39m)[0m
[0m[31m[1m>[22m[39m[90m 74 |[39m         bundle [33m=[39m require(bundlePath)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 75 |[39m       }[0m
[0m [90m 76 |[39m       [0m
[0m [90m 77 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:69:18
Module not found: Can't resolve '/ROOT/.mdx-compiled/content-bundle.min.json'
[0m [90m 67 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading minified bundle...`[39m)[0m
[0m [90m 68 |[39m         [36mconst[39m bundlePath [33m=[39m path[33m.[39mjoin(projectRoot[33m,[39m [32m'.mdx-compiled/content-bundle.min.json'[39m)[0m
[0m[31m[1m>[22m[39m[90m 69 |[39m         bundle [33m=[39m require(bundlePath)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 70 |[39m       } [36melse[39m {[0m
[0m [90m 71 |[39m         [90m// In development, use full bundle[39m[0m
[0m [90m 72 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m



https://nextjs.org/docs/messages/module-not-found


 ⚠ ./src/services/content/providers/next-mdx-remote/provider.ts:74:18
Module not found: Can't resolve <dynamic>
[0m [90m 72 |[39m         console[33m.[39mlog([32m`[NextMDXRemoteProvider] Loading development bundle...`[39m)[0m
[0m [90m 73 |[39m         [36mconst[39m bundlePath [33m=[39m path[33m.[39mjoin(projectRoot[33m,[39m [32m'.mdx-compiled/content-bundle.json'[39m)[0m
[0m[31m[1m>[22m[39m[90m 74 |[39m         bundle [33m=[39m require(bundlePath)[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 75 |[39m       }[0m
[0m [90m 76 |[39m       [0m
[0m [90m 77 |[39m       console[33m.[39mlog([32m`[NextMDXRemoteProvider] Bundle loaded, keys:`[39m[33m,[39m [33mObject[39m[33m.[39mkeys(bundle)[33m.[39mlength)[0m



https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote
Module not found: Can't resolve './ROOT/.mdx-compiled/content-bundle.json'
server relative imports are not implemented yet. Please try an import relative to the file you are importing from.


https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote
Module not found: Can't resolve './ROOT/.mdx-compiled/content-bundle.json'
server relative imports are not implemented yet. Please try an import relative to the file you are importing from.


https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote
Module not found: Can't resolve './ROOT/.mdx-compiled/content-bundle.min.json'
server relative imports are not implemented yet. Please try an import relative to the file you are importing from.


https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote
Module not found: Can't resolve './ROOT/.mdx-compiled/content-bundle.min.json'
server relative imports are not implemented yet. Please try an import relative to the file you are importing from.


https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote/provider.ts:22:1
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 20 |[39m [90m// Import content bundles - these are generated by build-mdx-content.ts[39m[0m
[0m [90m 21 |[39m [90m// @ts-ignore - JSON imports[39m[0m
[0m[31m[1m>[22m[39m[90m 22 |[39m [36mimport[39m contentBundle [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.json'[39m[0m
[0m [90m    |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 23 |[39m [90m// @ts-ignore - JSON imports[39m[0m
[0m [90m 24 |[39m [36mimport[39m contentBundleMin [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.min.json'[39m[0m
[0m [90m 25 |[39m[0m



https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote/provider.ts:22:1
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 20 |[39m [90m// Import content bundles - these are generated by build-mdx-content.ts[39m[0m
[0m [90m 21 |[39m [90m// @ts-ignore - JSON imports[39m[0m
[0m[31m[1m>[22m[39m[90m 22 |[39m [36mimport[39m contentBundle [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.json'[39m[0m
[0m [90m    |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 23 |[39m [90m// @ts-ignore - JSON imports[39m[0m
[0m [90m 24 |[39m [36mimport[39m contentBundleMin [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.min.json'[39m[0m
[0m [90m 25 |[39m[0m



https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote/provider.ts:22:1
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 20 |[39m [90m// Import content bundles - these are generated by build-mdx-content.ts[39m[0m
[0m [90m 21 |[39m [90m// @ts-ignore - JSON imports[39m[0m
[0m[31m[1m>[22m[39m[90m 22 |[39m [36mimport[39m contentBundle [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.json'[39m[0m
[0m [90m    |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 23 |[39m [90m// @ts-ignore - JSON imports[39m[0m
[0m [90m 24 |[39m [36mimport[39m contentBundleMin [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.min.json'[39m[0m
[0m [90m 25 |[39m[0m



https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote/provider.ts:22:1
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 20 |[39m [90m// Import content bundles - these are generated by build-mdx-content.ts[39m[0m
[0m [90m 21 |[39m [90m// @ts-ignore - JSON imports[39m[0m
[0m[31m[1m>[22m[39m[90m 22 |[39m [36mimport[39m contentBundle [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.json'[39m[0m
[0m [90m    |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 23 |[39m [90m// @ts-ignore - JSON imports[39m[0m
[0m [90m 24 |[39m [36mimport[39m contentBundleMin [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.min.json'[39m[0m
[0m [90m 25 |[39m[0m



https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote/provider.ts:22:1
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 20 |[39m [90m// Import content bundles - these are generated by build-mdx-content.ts[39m[0m
[0m [90m 21 |[39m [90m// @ts-ignore - JSON imports[39m[0m
[0m[31m[1m>[22m[39m[90m 22 |[39m [36mimport[39m contentBundle [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.json'[39m[0m
[0m [90m    |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 23 |[39m [90m// @ts-ignore - JSON imports[39m[0m
[0m [90m 24 |[39m [36mimport[39m contentBundleMin [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.min.json'[39m[0m
[0m [90m 25 |[39m[0m



https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote/provider.ts:22:1
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 20 |[39m [90m// Import content bundles - these are generated by build-mdx-content.ts[39m[0m
[0m [90m 21 |[39m [90m// @ts-ignore - JSON imports[39m[0m
[0m[31m[1m>[22m[39m[90m 22 |[39m [36mimport[39m contentBundle [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.json'[39m[0m
[0m [90m    |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 23 |[39m [90m// @ts-ignore - JSON imports[39m[0m
[0m [90m 24 |[39m [36mimport[39m contentBundleMin [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.min.json'[39m[0m
[0m [90m 25 |[39m[0m



https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote/provider.ts:24:1
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 22 |[39m [36mimport[39m contentBundle [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.json'[39m[0m
[0m [90m 23 |[39m [90m// @ts-ignore - JSON imports[39m[0m
[0m[31m[1m>[22m[39m[90m 24 |[39m [36mimport[39m contentBundleMin [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.min.json'[39m[0m
[0m [90m    |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 25 |[39m[0m
[0m [90m 26 |[39m [90m/**[39m[0m
[0m [90m 27 |[39m [90m * NextMDXRemoteProvider Class[39m[0m



https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote/provider.ts:24:1
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 22 |[39m [36mimport[39m contentBundle [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.json'[39m[0m
[0m [90m 23 |[39m [90m// @ts-ignore - JSON imports[39m[0m
[0m[31m[1m>[22m[39m[90m 24 |[39m [36mimport[39m contentBundleMin [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.min.json'[39m[0m
[0m [90m    |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 25 |[39m[0m
[0m [90m 26 |[39m [90m/**[39m[0m
[0m [90m 27 |[39m [90m * NextMDXRemoteProvider Class[39m[0m



https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote/provider.ts:24:1
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 22 |[39m [36mimport[39m contentBundle [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.json'[39m[0m
[0m [90m 23 |[39m [90m// @ts-ignore - JSON imports[39m[0m
[0m[31m[1m>[22m[39m[90m 24 |[39m [36mimport[39m contentBundleMin [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.min.json'[39m[0m
[0m [90m    |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 25 |[39m[0m
[0m [90m 26 |[39m [90m/**[39m[0m
[0m [90m 27 |[39m [90m * NextMDXRemoteProvider Class[39m[0m



https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote/provider.ts:24:1
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 22 |[39m [36mimport[39m contentBundle [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.json'[39m[0m
[0m [90m 23 |[39m [90m// @ts-ignore - JSON imports[39m[0m
[0m[31m[1m>[22m[39m[90m 24 |[39m [36mimport[39m contentBundleMin [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.min.json'[39m[0m
[0m [90m    |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 25 |[39m[0m
[0m [90m 26 |[39m [90m/**[39m[0m
[0m [90m 27 |[39m [90m * NextMDXRemoteProvider Class[39m[0m



https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote/provider.ts:24:1
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 22 |[39m [36mimport[39m contentBundle [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.json'[39m[0m
[0m [90m 23 |[39m [90m// @ts-ignore - JSON imports[39m[0m
[0m[31m[1m>[22m[39m[90m 24 |[39m [36mimport[39m contentBundleMin [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.min.json'[39m[0m
[0m [90m    |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 25 |[39m[0m
[0m [90m 26 |[39m [90m/**[39m[0m
[0m [90m 27 |[39m [90m * NextMDXRemoteProvider Class[39m[0m



https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote/provider.ts:24:1
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 22 |[39m [36mimport[39m contentBundle [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.json'[39m[0m
[0m [90m 23 |[39m [90m// @ts-ignore - JSON imports[39m[0m
[0m[31m[1m>[22m[39m[90m 24 |[39m [36mimport[39m contentBundleMin [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.min.json'[39m[0m
[0m [90m    |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 25 |[39m[0m
[0m [90m 26 |[39m [90m/**[39m[0m
[0m [90m 27 |[39m [90m * NextMDXRemoteProvider Class[39m[0m



https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/services/content/providers/next-mdx-remote/provider.ts:22:1
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.json'
[0m [90m 20 |[39m [90m// Import content bundles - these are generated by build-mdx-content.ts[39m[0m
[0m [90m 21 |[39m [90m// @ts-ignore - JSON imports[39m[0m
[0m[31m[1m>[22m[39m[90m 22 |[39m [36mimport[39m contentBundle [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.json'[39m[0m
[0m [90m    |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 23 |[39m [90m// @ts-ignore - JSON imports[39m[0m
[0m [90m 24 |[39m [36mimport[39m contentBundleMin [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.min.json'[39m[0m
[0m [90m 25 |[39m[0m



https://nextjs.org/docs/messages/module-not-found



./src/services/content/providers/next-mdx-remote/provider.ts:24:1
Module not found: Can't resolve '../../../../.mdx-compiled/content-bundle.min.json'
[0m [90m 22 |[39m [36mimport[39m contentBundle [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.json'[39m[0m
[0m [90m 23 |[39m [90m// @ts-ignore - JSON imports[39m[0m
[0m[31m[1m>[22m[39m[90m 24 |[39m [36mimport[39m contentBundleMin [36mfrom[39m [32m'../../../../.mdx-compiled/content-bundle.min.json'[39m[0m
[0m [90m    |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[0m
[0m [90m 25 |[39m[0m
[0m [90m 26 |[39m [90m/**[39m[0m
[0m [90m 27 |[39m [90m * NextMDXRemoteProvider Class[39m[0m



https://nextjs.org/docs/messages/module-not-found


 GET /blogs 500 in 85ms
 ELIFECYCLE  Command failed.
