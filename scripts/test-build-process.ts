#!/usr/bin/env tsx

/**
 * Build Process Test Script
 * 
 * This script tests the complete build process to ensure
 * the Contentlayer2 migration is working correctly.
 */

import { execSync } from 'child_process'
import fs from 'fs'
import path from 'path'

interface TestResult {
  name: string
  passed: boolean
  error?: string
  duration: number
}

class BuildProcessTester {
  private results: TestResult[] = []

  private async runTest(name: string, testFn: () => Promise<void> | void): Promise<void> {
    const startTime = Date.now()
    
    try {
      await testFn()
      this.results.push({
        name,
        passed: true,
        duration: Date.now() - startTime
      })
      console.log(`✅ ${name}`)
    } catch (error) {
      this.results.push({
        name,
        passed: false,
        error: error instanceof Error ? error.message : String(error),
        duration: Date.now() - startTime
      })
      console.log(`❌ ${name}: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  async runAllTests(): Promise<void> {
    console.log('🚀 Testing Contentlayer2 Build Process...\n')

    await this.testContentlayerBuild()
    await this.testGeneratedFiles()
    await this.testContentImports()
    await this.testSEOGeneration()
    await this.testFullBuild()
    await this.testDevServer()

    this.printSummary()
  }

  private async testContentlayerBuild(): Promise<void> {
    await this.runTest('Contentlayer2 Build', () => {
      try {
        execSync('pnpm contentlayer2 build', { 
          stdio: 'pipe',
          cwd: process.cwd()
        })
      } catch (error: any) {
        // Check if it's just warnings (exit code 0 but with warnings)
        if (error.status !== 0) {
          throw new Error(`Contentlayer2 build failed: ${error.message}`)
        }
      }
    })
  }

  private async testGeneratedFiles(): Promise<void> {
    await this.runTest('Generated Files Check', () => {
      const requiredFiles = [
        '.contentlayer/generated/index.mjs',
        '.contentlayer/generated/index.d.ts',
        '.contentlayer/generated/types.d.ts'
      ]

      for (const file of requiredFiles) {
        if (!fs.existsSync(file)) {
          throw new Error(`Required file not found: ${file}`)
        }
      }

      // Check if content was generated
      const indexContent = fs.readFileSync('.contentlayer/generated/index.mjs', 'utf-8')
      if (!indexContent.includes('allBlogs') || !indexContent.includes('allProducts')) {
        throw new Error('Generated content is missing expected exports')
      }

      console.log('  All required files generated successfully')
    })
  }

  private async testContentImports(): Promise<void> {
    await this.runTest('Content Imports Test', async () => {
      // Create a temporary test file to verify imports work
      const testFile = 'temp-import-test.mjs'
      const testCode = `
        import { allBlogs, allProducts, allCaseStudies } from './.contentlayer/generated/index.mjs'
        
        console.log('Blogs:', allBlogs.length)
        console.log('Products:', allProducts.length)
        console.log('Case Studies:', allCaseStudies.length)
        
        if (allBlogs.length === 0 && allProducts.length === 0 && allCaseStudies.length === 0) {
          throw new Error('No content found')
        }
        
        // Test content structure
        if (allBlogs.length > 0) {
          const blog = allBlogs[0]
          if (!blog.title || !blog.slug || !blog.lang) {
            throw new Error('Blog content missing required fields')
          }
        }
        
        console.log('Content imports working correctly')
      `
      
      fs.writeFileSync(testFile, testCode)
      
      try {
        execSync(`node ${testFile}`, { 
          stdio: 'pipe',
          cwd: process.cwd()
        })
      } finally {
        // Clean up
        if (fs.existsSync(testFile)) {
          fs.unlinkSync(testFile)
        }
      }
    })
  }

  private async testSEOGeneration(): Promise<void> {
    await this.runTest('SEO Files Generation', () => {
      try {
        execSync('pnpm generate:content', { 
          stdio: 'pipe',
          cwd: process.cwd()
        })

        // Check if SEO files were generated
        const seoFiles = [
          'public/sitemap.xml',
          'public/rss.xml',
          'public/rss-zh.xml'
        ]

        for (const file of seoFiles) {
          if (!fs.existsSync(file)) {
            console.log(`  Warning: ${file} not found (may be expected)`)
          } else {
            console.log(`  ✓ Generated: ${file}`)
          }
        }
      } catch (error: any) {
        // SEO generation might fail if scripts don't exist yet
        console.log('  SEO generation skipped (scripts may not be implemented yet)')
      }
    })
  }

  private async testFullBuild(): Promise<void> {
    await this.runTest('Full Application Build', () => {
      try {
        execSync('pnpm build', { 
          stdio: 'pipe',
          cwd: process.cwd(),
          timeout: 120000 // 2 minutes timeout
        })
        console.log('  Full build completed successfully')
      } catch (error: any) {
        throw new Error(`Build failed: ${error.message}`)
      }
    })
  }

  private async testDevServer(): Promise<void> {
    await this.runTest('Development Server Start', () => {
      // We'll just test that the dev command doesn't immediately fail
      // We won't actually start the server to avoid hanging the test
      try {
        execSync('pnpm next --help', { 
          stdio: 'pipe',
          cwd: process.cwd()
        })
        console.log('  Next.js CLI available and working')
      } catch (error: any) {
        throw new Error(`Next.js CLI not working: ${error.message}`)
      }
    })
  }

  private printSummary(): void {
    const passed = this.results.filter(r => r.passed).length
    const failed = this.results.filter(r => !r.passed).length
    const totalTime = this.results.reduce((sum, r) => sum + r.duration, 0)

    console.log('\n📊 Build Process Test Summary:')
    console.log(`  Total tests: ${this.results.length}`)
    console.log(`  Passed: ${passed}`)
    console.log(`  Failed: ${failed}`)
    console.log(`  Total time: ${totalTime}ms`)
    console.log(`  Success rate: ${((passed / this.results.length) * 100).toFixed(1)}%`)

    if (failed > 0) {
      console.log('\n❌ Failed tests:')
      this.results
        .filter(r => !r.passed)
        .forEach(r => console.log(`  - ${r.name}: ${r.error}`))
    }

    if (failed === 0) {
      console.log('\n🎉 All build process tests passed! Contentlayer2 migration is successful.')
      console.log('\n📋 Next Steps:')
      console.log('  1. Run: pnpm dev (to start development server)')
      console.log('  2. Test: http://localhost:3000/blogs')
      console.log('  3. Test: http://localhost:3000/products')
      console.log('  4. Test: http://localhost:3000/case-studies')
      console.log('  5. Verify: Language switching works correctly')
    } else {
      console.log('\n⚠️  Some tests failed. Please review the errors above.')
      process.exit(1)
    }
  }
}

// Run the tests
async function main() {
  const tester = new BuildProcessTester()
  await tester.runAllTests()
}

main().catch(error => {
  console.error('❌ Test script failed:', error)
  process.exit(1)
})
