#!/usr/bin/env node
/**
 * Simple test to check if content bundle exists
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const bundlePath = path.join(__dirname, '../.mdx-compiled/content-bundle.json');

console.log('\n=== Content Bundle Test ===\n');
console.log('Checking bundle at:', bundlePath);

if (fs.existsSync(bundlePath)) {
  const bundle = JSON.parse(fs.readFileSync(bundlePath, 'utf8'));
  const keys = Object.keys(bundle);
  
  console.log(`✓ Bundle exists with ${keys.length} items`);
  console.log('\nContent types found:');
  
  const types = new Set();
  const locales = new Set();
  
  keys.forEach(key => {
    const item = bundle[key];
    types.add(item.type);
    locales.add(item.lang);
  });
  
  console.log('- Types:', Array.from(types).join(', '));
  console.log('- Locales:', Array.from(locales).join(', '));
  
  // Count by type and locale
  const counts = {};
  keys.forEach(key => {
    const item = bundle[key];
    const countKey = `${item.type}-${item.lang}`;
    counts[countKey] = (counts[countKey] || 0) + 1;
  });
  
  console.log('\nContent distribution:');
  Object.entries(counts).sort().forEach(([key, count]) => {
    console.log(`- ${key}: ${count} items`);
  });
  
  // Show sample blog items
  console.log('\nSample blog items:');
  keys.filter(key => bundle[key].type === 'blog' && bundle[key].lang === 'en')
    .slice(0, 3)
    .forEach(key => {
      const item = bundle[key];
      console.log(`- ${item.slug}: "${item.title}"`);
    });
    
} else {
  console.log('✗ Bundle not found!');
  console.log('Run "pnpm build:content" to generate the bundle.');
}