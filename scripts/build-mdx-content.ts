#!/usr/bin/env tsx

/**
 * MDX Content Build Script
 * 
 * This script compiles MDX files into a JSON bundle for use with
 * the NextMDXRemoteProvider. It supports both one-time builds
 * and watch mode for development.
 */

import matter from 'gray-matter'
import glob from 'fast-glob'
import fs from 'fs-extra'
import path from 'path'
import { watch } from 'chokidar'
import type { ContentBundle } from '../src/services/content/providers/next-mdx-remote/types'

/**
 * Build options
 */
interface BuildOptions {
  /** Enable watch mode for development */
  watch?: boolean
  /** Content directory path */
  contentDir?: string
  /** Output directory path */
  outputDir?: string
}

/**
 * Main build function
 * 
 * Compiles all MDX files into a JSON bundle
 */
async function buildMDXContent(options: BuildOptions = {}) {
  const {
    watch: watchMode = false,
    contentDir = './content',
    outputDir = './.mdx-compiled'
  } = options
  
  console.log('[MDX Build] Starting build process...')
  const startTime = Date.now()
  
  try {
    // Find all MDX files
    const pattern = path.join(contentDir, '**/*.mdx')
    const files = await glob(pattern, {
      ignore: ['**/node_modules/**', '**/_*/**', '**/_*']
    })
    
    if (files.length === 0) {
      console.warn('[MDX Build] No MDX files found in', contentDir)
      return
    }
    
    console.log(`[MDX Build] Found ${files.length} MDX files`)
    
    // Compile all files in parallel
    const contentMap: ContentBundle = {}
    const compilationResults = await Promise.all(
      files.map(file => compileFile(file, contentDir))
    )
    
    // Build content map
    compilationResults.forEach(result => {
      if (result) {
        contentMap[result.key] = result.content
      }
    })
    
    // Ensure output directory exists
    await fs.ensureDir(outputDir)
    
    // Write full bundle
    const fullBundlePath = path.join(outputDir, 'content-bundle.json')
    await fs.writeJSON(fullBundlePath, contentMap, { spaces: 2 })
    console.log(`[MDX Build] Written full bundle to ${fullBundlePath}`)
    
    // Write minified bundle for Workers
    const minifiedMap = minifyContentBundle(contentMap)
    const minBundlePath = path.join(outputDir, 'content-bundle.min.json')
    await fs.writeJSON(minBundlePath, minifiedMap)
    console.log(`[MDX Build] Written minified bundle to ${minBundlePath}`)
    
    // Generate chunked static TypeScript files for optimal Workers performance
    await generateChunkedStaticContent(contentMap)
    console.log(`[MDX Build] Generated chunked static content files`)
    
    const duration = Date.now() - startTime
    console.log(`[MDX Build] ✓ Build completed in ${duration}ms`)
    
    // Start watch mode if requested
    if (watchMode) {
      startWatcher(contentDir, options)
    }
  } catch (error) {
    console.error('[MDX Build] Build failed:', error)
    process.exit(1)
  }
}

/**
 * Compile a single MDX file
 * 
 * @param filePath - Path to the MDX file
 * @param contentDir - Base content directory
 * @returns Compiled content with key
 */
async function compileFile(
  filePath: string,
  contentDir: string
): Promise<{ key: string; content: any } | null> {
  try {
    // Read file content
    const source = await fs.readFile(filePath, 'utf8')
    
    // Parse frontmatter with gray-matter
    // WARNING: gray-matter automatically converts certain value types:
    // - Time formats (e.g., 5:30) → seconds (330)
    // - Dates → Date objects
    // - Numbers → number type
    // - Booleans → boolean type
    // Always use quotes in YAML for values that should remain strings!
    // Example: videoDuration: "5:30" (not videoDuration: 5:30)
    const { content, data } = matter(source)
    
    // Extract metadata from file path
    const relativePath = path.relative(contentDir, filePath)
    const pathParts = relativePath.split(path.sep)
    
    // Expected structure: content/[type]/[locale]/[slug].mdx
    if (pathParts.length < 3) {
      console.warn(`[MDX Build] Invalid file structure: ${filePath}`)
      return null
    }
    
    const type = normalizeContentType(pathParts[0])
    const locale = pathParts[1]
    const fileSlug = path.basename(pathParts[pathParts.length - 1], '.mdx')
    
    // Use slug from frontmatter if available, otherwise use filename
    const slug = data.slug || fileSlug
    
    // Build content object for NextMDXRemote provider
    // The body structure should only contain raw content for this provider
    
    // Build content object
    const compiledContent = {
      slug,
      type,
      lang: locale,
      url: buildContentUrl(type, slug, locale),
      title: data.title || slug,
      description: data.description || '',
      body: {
        // Don't include 'code' field for NextMDXRemote provider
        raw: content
      },
      createdAt: data.date || data.createdAt || new Date().toISOString(),
      publishedAt: data.publishedAt,
      featured: data.featured || false,
      tags: data.tags || [],
      author: data.author,
      coverImage: data.coverImage || data.image,
      ...data // Include any additional frontmatter
    }
    
    const key = `${type}-${slug}-${locale}`
    console.log(`[MDX Build] Compiled: ${key}`)
    
    return { key, content: compiledContent }
  } catch (error) {
    console.error(`[MDX Build] Failed to compile ${filePath}:`, error)
    return null
  }
}

/**
 * Normalize content type from directory name
 * 
 * @param dirName - Directory name (e.g., 'blogs', 'case-studies')
 * @returns Normalized type (e.g., 'blog', 'case-study')
 */
function normalizeContentType(dirName: string): string {
  if (dirName === 'case-studies') {
    return 'case-study'
  }
  // Remove trailing 's' for other types
  return dirName.endsWith('s') ? dirName.slice(0, -1) : dirName
}

/**
 * Build content URL
 * 
 * @param type - Content type
 * @param slug - Content slug
 * @param locale - Language locale
 * @returns Full URL path
 */
function buildContentUrl(type: string, slug: string, locale: string): string {
  const typeSegment = type === 'case-study' ? 'case-studies' : `${type}s`
  const localePrefix = locale === 'en' ? '' : `/${locale}`
  return `${localePrefix}/${typeSegment}/${slug}`
}

/**
 * Minify content bundle for production/Workers
 * 
 * @param bundle - Full content bundle
 * @returns Minified bundle
 */
function minifyContentBundle(bundle: ContentBundle): ContentBundle {
  const minified: ContentBundle = {}
  
  Object.entries(bundle).forEach(([key, content]) => {
    minified[key] = {
      ...content,
      body: {
        html: content.body.html
        // Remove raw content to save space
      }
    } as any
  })
  
  return minified
}

/**
 * Start file watcher for development
 * 
 * @param contentDir - Content directory to watch
 * @param options - Build options
 */
function startWatcher(contentDir: string, options: BuildOptions) {
  console.log('[MDX Build] Starting watch mode...')
  
  const watcher = watch(path.join(contentDir, '**/*.mdx'), {
    ignoreInitial: true,
    ignored: ['**/node_modules/**', '**/_*/**', '**/_*']
  })
  
  let rebuildTimeout: NodeJS.Timeout | null = null
  
  const scheduleRebuild = () => {
    // Debounce rebuilds
    if (rebuildTimeout) {
      clearTimeout(rebuildTimeout)
    }
    
    rebuildTimeout = setTimeout(async () => {
      console.log('[MDX Build] Changes detected, rebuilding...')
      await buildMDXContent({ ...options, watch: false })
    }, 300)
  }
  
  watcher
    .on('add', path => {
      console.log(`[MDX Build] File added: ${path}`)
      scheduleRebuild()
    })
    .on('change', path => {
      console.log(`[MDX Build] File changed: ${path}`)
      scheduleRebuild()
    })
    .on('unlink', path => {
      console.log(`[MDX Build] File removed: ${path}`)
      scheduleRebuild()
    })
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n[MDX Build] Shutting down watcher...')
    watcher.close()
    process.exit(0)
  })
}

/**
 * Generate chunked static TypeScript files for optimal Workers performance
 *
 * @param contentMap - Full content bundle
 */
async function generateChunkedStaticContent(contentMap: ContentBundle): Promise<void> {
  const staticDir = path.join(
    process.cwd(),
    'src/services/content/providers/next-mdx-remote'
  )

  // Group content by type
  const contentChunks: Record<string, ContentBundle> = {
    blogs: {},
    products: {},
    'case-studies': {},
    docs: {}
  }

  // Categorize content into chunks
  Object.entries(contentMap).forEach(([key, content]) => {
    const type = content.type
    const chunkKey = type === 'case-study' ? 'case-studies' :
                     type === 'blog' ? 'blogs' :
                     type === 'product' ? 'products' :
                     'docs' // fallback for any other content

    if (contentChunks[chunkKey]) {
      contentChunks[chunkKey][key] = content
    }
  })

  const timestamp = new Date().toISOString()

  // Generate individual chunk files
  for (const [chunkName, chunkContent] of Object.entries(contentChunks)) {
    if (Object.keys(chunkContent).length === 0) continue

    const chunkFileName = `static-content-${chunkName}.ts`
    const chunkFilePath = path.join(staticDir, chunkFileName)

    const chunkFileContent = `/**
 * Static Content Chunk: ${chunkName}
 *
 * This file is auto-generated by build-mdx-content.ts
 * DO NOT EDIT MANUALLY
 *
 * Last generated: ${timestamp}
 */

import type { ContentItem } from '../../types'

// Pre-compiled ${chunkName} content
export const ${chunkName}Content: Record<string, ContentItem> = ${JSON.stringify(chunkContent, null, 2)}

// Export default for dynamic imports
export default ${chunkName}Content
`

    await fs.writeFile(chunkFilePath, chunkFileContent, 'utf-8')
    console.log(`[MDX Build] Generated chunk: ${chunkFileName} (${Object.keys(chunkContent).length} items)`)
  }

  // Generate main index file with lazy loading
  const indexFilePath = path.join(staticDir, 'static-content.ts')
  const indexFileContent = `/**
 * Static Content Index for Cloudflare Workers
 *
 * This file is auto-generated by build-mdx-content.ts
 * DO NOT EDIT MANUALLY
 *
 * Last generated: ${timestamp}
 */

import type { ContentItem } from '../../types'

// Lazy-loaded content chunks
let contentCache: Record<string, ContentItem> | null = null

/**
 * Get all static content with lazy loading and caching
 *
 * @returns Complete content bundle
 */
export async function getStaticContent(): Promise<Record<string, ContentItem>> {
  // Return cached content if available
  if (contentCache) {
    return contentCache
  }

  console.log('[StaticContent] Loading content chunks...')

  try {
    // Dynamically import all content chunks
    const [blogs, products, caseStudies, docs] = await Promise.all([
      import('./static-content-blogs').catch(() => ({ default: {} })),
      import('./static-content-products').catch(() => ({ default: {} })),
      import('./static-content-case-studies').catch(() => ({ default: {} })),
      import('./static-content-docs').catch(() => ({ default: {} }))
    ])

    // Combine all chunks
    contentCache = {
      ...blogs.default,
      ...products.default,
      ...caseStudies.default,
      ...docs.default
    }

    console.log(\`[StaticContent] Loaded \${Object.keys(contentCache).length} content items\`)
    return contentCache
  } catch (error) {
    console.error('[StaticContent] Failed to load content chunks:', error)
    return {}
  }
}

/**
 * Clear content cache (useful for development)
 */
export function clearContentCache(): void {
  contentCache = null
}

/**
 * Get content synchronously (requires content to be pre-loaded)
 *
 * @returns Cached content or empty object
 */
export function getStaticContentSync(): Record<string, ContentItem> {
  return contentCache || {}
}
`

  await fs.writeFile(indexFilePath, indexFileContent, 'utf-8')
  console.log(`[MDX Build] Generated main index: static-content.ts`)
}

// Export for programmatic use
export default buildMDXContent

// CLI entry point
if (require.main === module) {
  const args = process.argv.slice(2)
  const watchMode = args.includes('--watch') || args.includes('-w')
  
  buildMDXContent({ watch: watchMode }).catch(error => {
    console.error('[MDX Build] Fatal error:', error)
    process.exit(1)
  })
}