#!/usr/bin/env tsx

/**
 * Pre-deployment Check Script
 * 
 * This script validates the deployment configuration and ensures
 * the correct content provider is selected for the target platform.
 * 
 * Usage:
 *   tsx scripts/pre-deploy-check.ts [platform]
 *   
 * Examples:
 *   tsx scripts/pre-deploy-check.ts vercel
 *   tsx scripts/pre-deploy-check.ts cloudflare
 *   tsx scripts/pre-deploy-check.ts docker
 */

import { existsSync } from 'fs'
import { join } from 'path'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.production' })
dotenv.config({ path: '.env.local' })
dotenv.config({ path: '.env' })

// Platform configuration recommendations
const PLATFORM_CONFIGS = {
  vercel: {
    recommendedProvider: 'contentlayer2',
    requiredEnvVars: ['NEXT_PUBLIC_WEB_URL', 'DATABASE_URL'],
    optionalEnvVars: ['AUTH_SECRET', 'AUTH_URL'],
    notes: [
      'Vercel supports both providers, but contentlayer2 offers better DX',
      'Type generation and hot reload work best with contentlayer2',
      'Make sure to set Node.js version to 18+ in Vercel settings'
    ]
  },
  cloudflare: {
    recommendedProvider: 'next-mdx-remote',
    requiredEnvVars: ['NEXT_PUBLIC_WEB_URL'],
    optionalEnvVars: ['DATABASE_URL', 'AUTH_SECRET'],
    notes: [
      'Cloudflare Workers have file system limitations',
      'next-mdx-remote pre-compiles content for edge runtime',
      'Database connections should use compatible drivers (e.g., Hyperdrive)'
    ]
  },
  docker: {
    recommendedProvider: 'contentlayer2',
    requiredEnvVars: ['NEXT_PUBLIC_WEB_URL', 'DATABASE_URL'],
    optionalEnvVars: ['AUTH_SECRET', 'AUTH_URL'],
    notes: [
      'Docker deployments have full Node.js environment',
      'Both providers work well, contentlayer2 recommended for consistency',
      'Ensure Docker image includes all dependencies'
    ]
  }
} as const

type Platform = keyof typeof PLATFORM_CONFIGS

// Color codes for terminal output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
}

function log(message: string, color: keyof typeof colors = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function checkEnvVar(name: string, required: boolean = true): boolean {
  const value = process.env[name]
  
  if (!value) {
    log(`  ❌ ${name}: Not set${required ? ' (REQUIRED)' : ' (optional)'}`, 'red')
    return !required
  }
  
  // Mask sensitive values
  const maskedValue = name.includes('SECRET') || name.includes('KEY') 
    ? '***' 
    : value.substring(0, 20) + (value.length > 20 ? '...' : '')
    
  log(`  ✅ ${name}: ${maskedValue}`, 'green')
  return true
}

function checkContentProvider(platform: Platform): boolean {
  const currentProvider = process.env.CONTENT_PROVIDER || 'contentlayer2'
  const recommended = PLATFORM_CONFIGS[platform].recommendedProvider
  
  log('\n📦 Content Provider Check:', 'cyan')
  log(`  Current: ${currentProvider}`)
  log(`  Recommended for ${platform}: ${recommended}`)
  
  if (currentProvider !== recommended) {
    log(`  ⚠️  Consider using '${recommended}' for optimal ${platform} performance`, 'yellow')
    log(`     Set CONTENT_PROVIDER=${recommended} in your environment`, 'yellow')
    return true // Not a blocking issue
  }
  
  log('  ✅ Provider matches platform recommendation', 'green')
  return true
}

function checkBuildArtifacts(): boolean {
  log('\n🏗️  Build Artifacts Check:', 'cyan')
  
  const artifacts = [
    { path: '.next', name: 'Next.js build' },
    { path: '.contentlayer', name: 'Contentlayer cache', onlyFor: 'contentlayer2' },
    { path: '.mdx-compiled', name: 'MDX compiled cache', onlyFor: 'next-mdx-remote' },
    { path: 'public/sitemap.xml', name: 'Sitemap' },
    { path: 'public/rss.xml', name: 'RSS feed' }
  ]
  
  const currentProvider = process.env.CONTENT_PROVIDER || 'contentlayer2'
  let allGood = true
  
  for (const artifact of artifacts) {
    // Skip provider-specific artifacts if not using that provider
    if (artifact.onlyFor && artifact.onlyFor !== currentProvider) {
      continue
    }
    
    if (existsSync(join(process.cwd(), artifact.path))) {
      log(`  ✅ ${artifact.name} found`, 'green')
    } else {
      log(`  ❌ ${artifact.name} missing - run build first`, 'red')
      allGood = false
    }
  }
  
  return allGood
}

function checkDatabaseConfig(): boolean {
  log('\n🗄️  Database Configuration:', 'cyan')
  
  const dbUrl = process.env.DATABASE_URL
  
  if (!dbUrl) {
    log('  ⚠️  No database configured (some features may be limited)', 'yellow')
    return true
  }
  
  try {
    const url = new URL(dbUrl)
    log(`  ✅ Database type: ${url.protocol.replace(':', '')}`, 'green')
    log(`  ✅ Host: ${url.hostname}`, 'green')
    
    // Platform-specific warnings
    const platform = process.argv[2] as Platform
    if (platform === 'cloudflare' && !dbUrl.includes('hyperdrive')) {
      log('  ⚠️  Consider using Cloudflare Hyperdrive for database connections', 'yellow')
    }
    
    return true
  } catch (error) {
    log('  ❌ Invalid DATABASE_URL format', 'red')
    return false
  }
}

async function main() {
  const platform = process.argv[2] as Platform
  
  if (!platform || !PLATFORM_CONFIGS[platform]) {
    log('Usage: tsx scripts/pre-deploy-check.ts [platform]', 'red')
    log('Available platforms: vercel, cloudflare, docker')
    process.exit(1)
  }
  
  log(`\n🚀 Pre-deployment Check for ${platform.toUpperCase()}\n`, 'blue')
  
  const config = PLATFORM_CONFIGS[platform]
  let hasErrors = false
  
  // Check environment variables
  log('🔐 Environment Variables:', 'cyan')
  for (const envVar of config.requiredEnvVars) {
    if (!checkEnvVar(envVar, true)) {
      hasErrors = true
    }
  }
  
  for (const envVar of config.optionalEnvVars) {
    checkEnvVar(envVar, false)
  }
  
  // Check content provider
  if (!checkContentProvider(platform)) {
    hasErrors = true
  }
  
  // Check build artifacts
  if (!checkBuildArtifacts()) {
    hasErrors = true
  }
  
  // Check database config
  if (!checkDatabaseConfig()) {
    hasErrors = true
  }
  
  // Platform-specific notes
  log('\n📝 Platform Notes:', 'cyan')
  for (const note of config.notes) {
    log(`  • ${note}`, 'blue')
  }
  
  // Summary
  log('\n' + '='.repeat(50), 'cyan')
  if (hasErrors) {
    log('❌ Pre-deployment check failed! Fix the issues above.', 'red')
    process.exit(1)
  } else {
    log('✅ Pre-deployment check passed! Ready to deploy.', 'green')
    
    // Deployment command suggestions
    log('\n🚢 Deployment Commands:', 'cyan')
    switch (platform) {
      case 'vercel':
        log('  vercel --prod', 'blue')
        break
      case 'cloudflare':
        log('  npm run cf:deploy', 'blue')
        break
      case 'docker':
        log('  docker build -t your-app . && docker run -p 3000:3000 your-app', 'blue')
        break
    }
  }
}

main().catch(error => {
  log(`\n❌ Error: ${error.message}`, 'red')
  process.exit(1)
})