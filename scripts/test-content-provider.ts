#!/usr/bin/env tsx
/**
 * Test script to verify NextMDXRemoteProvider is loading content correctly
 */

// Set the environment variable before importing anything
process.env.CONTENT_PROVIDER = 'next-mdx-remote'

import { contentService } from '../src/services/content'

async function testContentProvider() {
  console.log('\n=== Testing Content Provider ===\n')
  
  console.log('Environment Variable CONTENT_PROVIDER:', process.env.CONTENT_PROVIDER)
  
  // Test getting the provider info
  console.log('Provider:', contentService.getProviderName())
  console.log('Version:', contentService.getProviderVersion())
  console.log('Supported Locales:', contentService.getSupportedLocales())
  console.log('Supported Content Types:', contentService.getSupportedContentTypes())
  
  // Test getting blog list
  console.log('\n--- Testing Blog List ---')
  const blogs = await contentService.getContentList('blog', 'en')
  console.log(`Found ${blogs.length} English blog posts`)
  
  if (blogs.length > 0) {
    console.log('\nFirst blog:', {
      slug: blogs[0].slug,
      title: blogs[0].title,
      type: blogs[0].type,
      lang: blogs[0].lang
    })
  }
  
  // Test getting Chinese blogs
  const zhBlogs = await contentService.getContentList('blog', 'zh')
  console.log(`\nFound ${zhBlogs.length} Chinese blog posts`)
  
  // Test getting single blog
  console.log('\n--- Testing Single Blog ---')
  const singleBlog = await contentService.getContent('blog', 'getting-started-with-shipany', 'en')
  console.log('Single blog found:', singleBlog ? 'Yes' : 'No')
  if (singleBlog) {
    console.log('Title:', singleBlog.title)
  }
  
  // Test getting all slugs
  console.log('\n--- Testing Get All Slugs ---')
  const allSlugs = await contentService.getAllContentSlugs('blog')
  console.log(`Found ${allSlugs.length} total blog slugs`)
  console.log('Sample slugs:', allSlugs.slice(0, 3))
}

testContentProvider().catch(console.error)