import { ModelInfo } from "@/aisdk/types/common";

// Available models mapping
export const Models: Record<string, ModelInfo> = {
  "gpt4o": {
    id: "gpt4o",
    name: "GPT-4o",
    provider: "kie",
    maxOutputs: 1, // Currently only supports one image per generation
  },
};

/**
 * Task status enum for KIE image generation
 */
enum TaskStatus {
  GENERATING = "GENERATING", // Task is in process
  SUCCESS = "SUCCESS",        // Task completed successfully
  GENERATE_FAILED = "GENERATE_FAILED", // Image generation failed
}

/**
 * Interface for KIE task record response
 */
interface TaskRecord {
  taskId: string;
  paramJson: string;
  completeTime: number | string;
  response: {
    resultUrls: string[] | null;
  };
  successFlag: number;
  status: string;
  errorCode: number;
  errorMessage: string;
  createTime: number | string;
  progress: string; // "0.00" ~ "1.00"
}

/**
 * KIE Image Generator Service
 * 
 * This service implements image generation functionality using the KIE API.
 * It supports generating images using GPT-4o through KIE's API.
 * 
 * Features:
 * - Handles authentication with KIE API
 * - Provides task creation and status polling
 * - Error handling and logging
 * - Supports GPT-4o image model
 * - Webhook support for asynchronous processing
 */
export class ImageGenerator {
  private baseUrl: string = "https://kieai.erweima.ai";
  private gpt4oImageEndpoint: string = "/api/v1/gpt4o-image/generate";
  private gpt4oImageRecordEndpoint: string = "/api/v1/gpt4o-image/record-info";
  private apiKey: string;
  private maxPollingAttempts: number = 30;
  private initialPollingDelay: number = 3000; // 最初指定为1000
  
  /**
   * Initialize the KIE image generator with API token
   * 
   * @param apiToken Optional API token (otherwise reads from environment)
   */
  constructor(apiToken?: string) {
    // Use provided token or fetch from environment
    const token = apiToken || process.env.KIE_API_KEY;
    
    if (!token) {
      throw new Error('KIE API token not configured');
    }
    
    this.apiKey = token;
  }
  
  /**
   * Generate a single image and return its URL as string
   * 
   * @param modelId The KIE model ID (e.g. "gpt4o")
   * @param parameters Parameters for image generation (already formatted for KIE API)
   * @returns Promise with a single image URL string
   */
  public async generateImage(
    modelId: string, 
    parameters: Record<string, any>
  ): Promise<string> {
    // Validate the model
    if (modelId !== "gpt4o") {
      throw new Error(`Unsupported model ID: ${modelId}. Currently only "gpt4o" is supported.`);
    }
    
    try {
      console.log(`Creating image generation task with KIE GPT-4o model`);
      console.log(`Parameters: ${JSON.stringify(parameters, null, 2)}`);
      
      // Make the API request to create a task
      const response = 
        await this.makeApiRequest(this.gpt4oImageEndpoint, parameters);
      
      if (response.code !== 200 || !response.data?.taskId) {
        throw new Error(`Failed to create image generation task: ${response.msg || 'Unknown error'}`);
      }
      
      const taskId = response.data.taskId;
      console.log(`Created KIE image generation task: ${taskId}`);
      
      // Poll for task completion
      const imageUrl = await this.pollForTaskCompletion(taskId);
      return imageUrl;
    } catch (error) {
      console.error('Error generating image with KIE:', error);
      throw error instanceof Error 
        ? error 
        : new Error('Unknown error during KIE image generation');
    }
  }
  
  /**
   * Generate multiple images (Currently KIE only supports one image per request)
   * 
   * @param modelId The KIE model ID
   * @param parameters Parameters for image generation (already formatted for KIE API)
   * @param count Number of images to generate
   * @returns Promise with array of generated image URLs
   */
  public async generateImages(
    modelId: string,
    parameters: Record<string, any>,
    count: number = 1
  ): Promise<string[]> {
    if (count <= 0) {
      return [];
    }
    
    // Since KIE currently only supports generating one image at a time,
    // we'll make multiple sequential requests if more than one image is needed
    const results: string[] = [];
    
    for (let i = 0; i < count; i++) {
      console.log(`Generating image ${i + 1}/${count}`);
      const imageUrl = await this.generateImage(modelId, parameters);
      results.push(imageUrl);
    }
    
    return results;
  }

  /**
   * Submit a task for image generation with webhook support
   * This method creates a task and returns immediately with the task ID,
   * rather than waiting for completion like generateImage does.
   * The results will be sent to the provided webhook URL.
   * 
   * @param modelId The KIE model ID (e.g. "gpt4o")
   * @param parameters Parameters with webhook URL
   * @returns Promise with the provider's task ID
   */
  public async submitImageGenerationTask(
    modelId: string, 
    parameters: Record<string, any>
  ): Promise<{ taskId: string }> {
    // Validate the model
    if (modelId !== Models.gpt4o.id) {
      throw new Error(`Unsupported model ID: ${modelId}. Currently only "gpt4o" is supported.`);
    }
    
    try {
      console.log(`Creating image generation task with KIE GPT-4o model`);
      console.log(`Parameters: ${JSON.stringify(parameters, null, 2)}`);
      
      // Ensure webhookUrl is included in parameters
      if (!parameters.callBackUrl) {
        throw new Error("Webhook URL (callBackUrl) is required");
      }
      
      // Make the API request to create a task
      const response = 
        await this.makeApiRequest(this.gpt4oImageEndpoint, parameters);
      
      if (response.code !== 200 || !response.data?.taskId) {
        throw new Error(`Failed to create image generation task: ${response.msg || 'Unknown error'}`);
      }
      
      const taskId = response.data.taskId;
      console.log(`Created KIE image generation task: ${taskId}`);
      
      return { taskId };
    } catch (error) {
      console.error('Error submitting image generation task to KIE:', error);
      throw error instanceof Error 
        ? error 
        : new Error('Unknown error during KIE task submission');
    }
  }
  
  /**
   * Make an API request to the KIE endpoint
   * 
   * @param endpoint API endpoint path
   * @param data Request data for POST requests
   * @param method HTTP method (default: POST)
   * @returns Promise with API response
   */
  private async makeApiRequest(
    endpoint: string, 
    data?: any, 
    method: 'GET' | 'POST' = 'POST'
  ): Promise<any> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const headers: HeadersInit = {
      'Accept': 'application/json',
      'Authorization': `Bearer ${this.apiKey}`
    };
    
    if (method === 'POST') {
      headers['Content-Type'] = 'application/json';
    }
    
    const options: RequestInit = {
      method,
      headers,
      redirect: 'follow'
    };
    
    if (data && method === 'POST') {
      options.body = JSON.stringify(data);
    }
    
    try {
      const response = await fetch(url, options);
      
      // Handle API errors
      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        
        if (response.status === 401) {
          throw new Error(`Authentication failed: Invalid API key or expired token`);
        }
        
        throw new Error(
          `API request failed with status ${response.status}: ${
            errorData?.msg || response.statusText || 'Unknown error'
          }`
        );
      }
      
      return await response.json();
    } catch (error) {
      console.error(`Error making API request to ${endpoint}:`, error);
      throw error;
    }
  }
  
  /**
   * Poll for task completion with exponential backoff
   * 
   * @param taskId The ID of the task to poll for
   * @returns Promise with image URL after completion
   */
  private async pollForTaskCompletion(taskId: string): Promise<string> {
    let attempts = 0;
    let delay = this.initialPollingDelay;
    
    // Tracking variables for detailed logging
    const startTime = Date.now();
    const pollingStats = {
      totalAttempts: 0,
      totalWaitTime: 0
    };
    
    console.log(`Starting polling for KIE task: ${taskId}. Max attempts: ${this.maxPollingAttempts}`);
    
    // Add taskId to querystring for GET request
    const endpoint = `${this.gpt4oImageRecordEndpoint}?taskId=${taskId}`;
    
    // First request
    let taskRecord = await this.makeApiRequest(endpoint, null, 'GET');
    
    if (!taskRecord.data) {
      throw new Error(`Failed to get task record: ${taskRecord.msg || 'Unknown error'}`);
    }
    
    let record: TaskRecord = taskRecord.data;
    
    while (
      attempts < this.maxPollingAttempts && 
      record.status !== TaskStatus.SUCCESS &&
      record.status !== TaskStatus.GENERATE_FAILED
    ) {
      // Log polling attempt details
      console.log(`Polling attempt ${attempts + 1}/${this.maxPollingAttempts} for task ${taskId}, current status: ${record.status}, progress: ${record.progress}, delay: ${delay}ms`);
      
      // Wait before next poll with exponential backoff
      await new Promise(resolve => setTimeout(resolve, delay));
      pollingStats.totalWaitTime += delay;
      
      // Increase delay with exponential backoff strategy, capped at 3 seconds
      // KIE documentation mentions maximum polling frequency of 3 requests per second
      delay = Math.min(delay * 1.5, 10000); // 最初指定为3000
      
      // Get updated task status
      taskRecord = await this.makeApiRequest(endpoint, null, 'GET');
      
      if (!taskRecord.data) {
        throw new Error(`Failed to get task record: ${taskRecord.msg || 'Unknown error'}`);
      }
      
      record = taskRecord.data;
      attempts++;
      pollingStats.totalAttempts = attempts;
    }
    
    // Calculate total polling time
    const totalTimeElapsed = Date.now() - startTime;
    
    // Log polling completion summary
    console.log(`Completed polling for task ${taskId} after ${attempts} attempts. ` +
      `Total time: ${totalTimeElapsed}ms, total wait time: ${pollingStats.totalWaitTime}ms, ` +
      `final status: ${record.status}, progress: ${record.progress}`);
    
    // Process task result based on final status
    if (record.status === TaskStatus.SUCCESS) {
      // Successfully completed task - return results
      const resultUrls = record.response?.resultUrls;
      
      if (!resultUrls || resultUrls.length === 0) {
        throw new Error('Task completed successfully but no image URLs were returned');
      }
      
      console.log(`Image URL: ${resultUrls[0]}`);
      return resultUrls[0];
    } else if (record.status === TaskStatus.GENERATE_FAILED) {
      // Task failed with an error
      console.error(`KIE task failed: ${record.errorMessage}`, {
        taskId,
        errorCode: record.errorCode,
        pollingAttempts: attempts,
        totalTime: totalTimeElapsed
      });
      throw new Error(`Image generation failed: ${record.errorMessage || `Error code: ${record.errorCode}`}`);
    } else {
      // We reached the maximum number of polling attempts without success
      console.error(`KIE task timed out after ${this.maxPollingAttempts} polling attempts`, {
        taskId,
        status: record.status,
        progress: record.progress,
        pollingAttempts: attempts,
        totalTime: totalTimeElapsed,
        pollingStats
      });
      throw new Error(`Image generation timed out after ${this.maxPollingAttempts} attempts (${Math.round(totalTimeElapsed/1000)} seconds). Last status: ${record.status}, progress: ${record.progress}`);
    }
  }
}

/**
 * Default export for easier importing
 */
export default ImageGenerator; 