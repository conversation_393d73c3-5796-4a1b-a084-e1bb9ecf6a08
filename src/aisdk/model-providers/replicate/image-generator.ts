import Replicate from "replicate";
import { ModelInfo } from "@/aisdk/types/common";


// Available models mapping
export const Models: Record<string, ModelInfo> = {
  "flux-schnell": {
    id: "black-forest-labs/flux-schnell",
    name: "Flux Schnell",
    provider: "replicate",
    maxOutputs: 4, // Supports multiple outputs (up to 4)
  },
  "flux-1.1-pro": {
    id: "black-forest-labs/flux-1.1-pro",
    name: "Flux 1.1 Pro",
    provider: "replicate",
    maxOutputs: 1, // Default one image per generation
  },
  "stable-diffusion-3.5-large": {
    id: "stability-ai/stable-diffusion-3.5-large",
    name: "Stable Diffusion 3.5 Large",
    provider: "replicate",
    maxOutputs: 1, // Default one image per generation
  },
};

/**
 * Replicate Image Generator Service
 * 
 * This service implements image generation functionality using the Replicate API.
 * It supports multiple image generation models from Replicate platform.
 * 
 * Features:
 * - Handles authentication with Replicate API
 * - Provides prediction creation and status polling
 * - Error handling and logging
 * - Supports multiple image models
 * - Options for single or multiple image generation
 * - Batch generation through multiple requests when needed
 */
export class ImageGenerator {
  private client: Replicate;
  private maxPollingAttempts: number = 20;
  private initialPollingDelay: number = 1000;
  
  /**
   * Initialize the Replicate image generator with API token
   * 
   * @param apiToken Optional API token (otherwise reads from environment)
   */
  constructor(apiToken?: string) {
    // Use provided token or fetch from environment
    const token = apiToken || process.env.REPLICATE_API_TOKEN;
    
    if (!token) {
      throw new Error('Replicate API token not configured');
    }
    
    this.client = new Replicate({
      auth: token,
    });
  }
  
  /**
   * Generate a single image and return its URL as string
   * 
   * @param modelId The Replicate model ID (e.g. "stability-ai/stable-diffusion-3.5-large")
   * @param parameters Model-specific parameters for the prediction
   * @returns Promise with a single image URL string
   */
  public async generateImage(
    modelId: string, 
    parameters: Record<string, any>
  ): Promise<string> {
    // Remove any num_outputs parameter to ensure single image generation
    const singleImageParams = { ...parameters };
    if ('num_outputs' in singleImageParams) {
      delete singleImageParams.num_outputs;
    }
    
    // Generate images and return the first one
    const imageUrls = await this.generateImages(modelId, singleImageParams, 1);
    return imageUrls[0];
  }
  
  /**
   * Generate images of exact specified count, making multiple requests if necessary
   * 
   * @param modelId The Replicate model ID
   * @param parameters Model-specific parameters for the prediction
   * @param count Total number of images to generate (will make multiple calls if needed)
   * @param maxConcurrent Maximum number of concurrent API requests (default: 3)
   * @returns Promise with array of generated image URLs of the exact requested count
   */
  public async generateImages(
    modelId: string,
    parameters: Record<string, any>,
    count: number = 1,
    maxConcurrent: number = 3
  ): Promise<string[]> {
    if (count <= 0) {
      return [];
    }
    
    // Find model information to determine batch strategy
    const modelKey = Object.keys(Models).find(key => Models[key].id === modelId);
    if (!modelKey) {
      console.warn(`Model information for ${modelId} not found in Models registry. Using default single output.`);
    }
    
    const model = modelKey ? Models[modelKey] : { maxOutputs: 1 };
    const maxOutputs = model.maxOutputs || 1;
    
    console.log(`Generating batch of ${count} images with model ${modelId} (max outputs per call: ${maxOutputs})`);
    
    // Calculate how many API calls are needed
    const batchCount = Math.ceil(count / maxOutputs);
    
    if (batchCount === 1) {
      // If we can get all images in one call, use a single API request
      return this.createPredictionAndProcess(modelId, { 
        ...parameters, 
        ...(maxOutputs > 1 ? { num_outputs: count } : {})
      });
    }
    
    // Prepare batch parameters
    const batches: Array<{ params: Record<string, any>, count: number }> = [];
    
    // Create full batches
    let remainingCount = count;
    while (remainingCount > 0) {
      const batchSize = Math.min(remainingCount, maxOutputs);
      
      // Clone parameters for this batch
      const batchParams = { ...parameters };
      
      // Add num_outputs for models supporting multiple outputs
      if (maxOutputs > 1) {
        batchParams.num_outputs = batchSize;
      }
      
      batches.push({ params: batchParams, count: batchSize });
      remainingCount -= batchSize;
    }
    
    console.log(`Splitting into ${batches.length} batches for generation`);
    
    // Process batches in concurrent chunks to avoid overwhelming the API
    const allResults: string[] = [];
    
    // Process batches in chunks based on maxConcurrent
    for (let i = 0; i < batches.length; i += maxConcurrent) {
      const chunk = batches.slice(i, i + maxConcurrent);
      
      console.log(`Processing batch chunk ${i / maxConcurrent + 1}, size: ${chunk.length}`);
      
      // Execute all batches in this chunk concurrently
      const chunkResults = await Promise.all(
        chunk.map(batch => this.createPredictionAndProcess(modelId, batch.params))
      );
      
      // Collect all results from this chunk
      for (const urls of chunkResults) {
        allResults.push(...urls);
      }
      
      console.log(`Completed chunk ${i / maxConcurrent + 1}, total images so far: ${allResults.length}/${count}`);
    }
    
    // Ensure we return exactly the requested count (trim if we got more)
    return allResults.slice(0, count);
  }
  
  /**
   * Create a prediction with the given model and parameters and process the results
   * 
   * @param modelId The Replicate model ID
   * @param parameters Model-specific parameters
   * @returns Promise with array of generated image URLs
   */
  private async createPredictionAndProcess(
    modelId: string,
    parameters: Record<string, any>
  ): Promise<string[]> {
    try {
      console.log(`Creating prediction with Replicate model: ${modelId}`);
      console.log(`Parameters: ${JSON.stringify(parameters, null, 2)}`);
      
      // Create prediction with model and parameters
      const prediction = await this.client.predictions.create({
        version: modelId,
        input: parameters,
      });
      
      // Get prediction ID for polling
      const predictionId = prediction.id;
      console.log(`Created Replicate prediction: ${predictionId}`);
      
      // If prediction completed immediately (rare case)
      if (prediction.status === 'succeeded') {
        const imageUrls = prediction.output;
        console.log(`Image URLs: ${JSON.stringify(imageUrls, null, 2)}`);
        return Array.isArray(imageUrls) ? imageUrls : [imageUrls];
      }
      
      // Poll for prediction completion
      return await this.pollForPredictionResults(predictionId);
    } catch (error) {
      console.error('Error generating images with Replicate:', error);
      throw error instanceof Error 
        ? error 
        : new Error('Unknown error during Replicate image generation');
    }
  }
  
  /**
   * Poll for prediction results with exponential backoff
   * 
   * @param predictionId The ID of the prediction to poll for
   * @returns Promise with array of image URLs after completion
   */
  private async pollForPredictionResults(predictionId: string): Promise<string[]> {
    let attempts = 0;
    let delay = this.initialPollingDelay;
    
    // Tracking variables for detailed logging
    const startTime = Date.now();
    const pollingStats = {
      totalAttempts: 0,
      totalWaitTime: 0
    };
    
    console.log(`Starting polling for Replicate prediction: ${predictionId}. Max attempts: ${this.maxPollingAttempts}`);
    
    // Initialize prediction state
    let prediction = await this.client.predictions.get(predictionId);
    
    while (
      attempts < this.maxPollingAttempts && 
      (prediction.status === 'starting' || prediction.status === 'processing')
    ) {
      // Log polling attempt details
      console.log(`Polling attempt ${attempts + 1}/${this.maxPollingAttempts} for prediction ${predictionId}, current status: ${prediction.status}, delay: ${delay}ms`);
      
      // Wait before next poll with exponential backoff
      await new Promise(resolve => setTimeout(resolve, delay));
      pollingStats.totalWaitTime += delay;
      
      // Increase delay with exponential backoff strategy, capped at 10 seconds
      delay = Math.min(delay * 1.5, 10000);
      
      // Get updated prediction status
      prediction = await this.client.predictions.get(predictionId);
      attempts++;
      pollingStats.totalAttempts = attempts;
    }
    
    // Calculate total polling time
    const totalTimeElapsed = Date.now() - startTime;
    
    // Log polling completion summary
    console.log(`Completed polling for prediction ${predictionId} after ${attempts} attempts. ` +
      `Total time: ${totalTimeElapsed}ms, total wait time: ${pollingStats.totalWaitTime}ms, ` +
      `final status: ${prediction.status}`);
    
    // Process prediction result based on final status
    if (prediction.status === 'succeeded') {
      // Successfully completed prediction - return results
      const imageUrls = prediction.output;

      console.log(`Image URLs: ${JSON.stringify(imageUrls, null, 2)}`);

      return Array.isArray(imageUrls) ? imageUrls : [imageUrls];
    } else if (prediction.status === 'failed') {
      // Prediction failed with an error from Replicate
      console.error(`Replicate prediction failed: ${prediction.error}`, {
        predictionId,
        pollingAttempts: attempts,
        totalTime: totalTimeElapsed
      });
      throw new Error(`Generation failed: ${prediction.error || 'Unknown error'}`);
    } else {
      // We reached the maximum number of polling attempts without success
      console.error(`Replicate prediction timed out after ${this.maxPollingAttempts} polling attempts`, {
        predictionId,
        status: prediction.status,
        pollingAttempts: attempts,
        totalTime: totalTimeElapsed,
        pollingStats
      });
      throw new Error(`Image generation timed out after ${this.maxPollingAttempts} attempts (${Math.round(totalTimeElapsed/1000)} seconds). Last status: ${prediction.status}`);
    }
  }
}

/**
 * Default export for easier importing
 */
export default ImageGenerator;
