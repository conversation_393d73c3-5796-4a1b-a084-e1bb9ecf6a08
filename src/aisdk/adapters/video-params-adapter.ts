import { 
  ModelInfo 
} from '@/aisdk/types/common';
import { 
  VideoGenerationMode, 
  VideoGenerationRequest, 
  VideoStyle,
  VideoAspectRatio,
  VideoDuration,
} from '@/types/ai/video-gen-types';
import { JSONValue } from '@ai-sdk/provider';

/**
 * Enhances the prompt with style keywords
 */
function enhancePromptForKie(
  request: VideoGenerationRequest,
  model: ModelInfo
): string {
  let enhancedPrompt = request.prompt.trim();
  
  switch(request.mode) {
    case VideoGenerationMode.TextToVideo:
      enhancedPrompt = `Generate a video based on the given prompt and style. ${enhancedPrompt}`;
      break;
    case VideoGenerationMode.ImageToVideo:
      enhancedPrompt = `Animate the given image based on the prompt. ${enhancedPrompt}`;
      break;
  }

  // Add style to prompt if specified
  if (request.videoStyle && request.videoStyle !== VideoStyle.NoStyle) {
    enhancedPrompt += `, ${request.videoStyle}`;
  }

  return enhancedPrompt;
}

/**
 * Enhances the prompt specifically for Kling video generation
 */
function enhancePromptForKling(
  request: VideoGenerationRequest,
  model: ModelInfo
): string {
  let enhancedPrompt = request.prompt.trim();

  switch(request.mode) {
    case VideoGenerationMode.TextToVideo:
      enhancedPrompt = `Generate a video based on the given prompt and style. \n${enhancedPrompt}`;
      break;
    case VideoGenerationMode.ImageToVideo:
      enhancedPrompt = `Animate the given image based on the prompt. \n${enhancedPrompt}`;
      break;
  }
  
  // Add style information if specified
  if (request.videoStyle && request.videoStyle !== VideoStyle.NoStyle) {
    enhancedPrompt += `\nStyle: ${request.videoStyle}`;
  }
  
  return enhancedPrompt;
}
