/**
 * Utility functions for sanitizing and normalizing strings to be safely used
 * in file paths, directory names, or filenames. 
 * 
 * Converts user input into clean, valid file system segments while preventing
 * security vulnerabilities like path traversal and filename injection attacks.
 *
 * Features:
 * - Path traversal protection (../, ..\)
 * - Control character filtering
 * - Reserved Windows filename handling
 * - Unicode support with configurable security levels
 * - Multi-part extension handling (.tar.gz, etc.)
 */

// =============================================================================
// Types and Constants
// =============================================================================

/**
 * Security levels for filename sanitization
 * - basic: Minimal sanitization, preserves most characters
 * - standard: Balanced approach for general use  
 * - strict: Maximum security for HTTP headers and sensitive contexts
 */
export type SecurityLevel = 'basic' | 'standard' | 'strict';

/**
 * Configuration options for filename sanitization
 */
export interface SanitizeOptions {
  /** Convert to lowercase (default: true) */
  toLower?: boolean;
  /** Security level (default: 'standard') */
  securityLevel?: SecurityLevel;
  /** Allow Unicode characters (default: true) */
  allowUnicode?: boolean;
  /** Maximum filename length (default: 200) */
  maxLength?: number;
}

/**
 * Windows reserved filenames that need special handling
 * These names cannot be used as filenames on Windows systems
 */
const RESERVED_WINDOWS_NAMES = [
  'CON', 'PRN', 'AUX', 'NUL', 
  'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
  'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
] as const;

// =============================================================================
// Input Validation and Basic Security
// =============================================================================

/**
 * Validates input string and returns sanitized version or fallback
 * Simple validation without confusing null returns
 * 
 * @param input - String to validate
 * @param fallback - Default value if input is invalid
 * @returns Valid string or fallback
 */
function validateAndNormalize(input: string, fallback: string): string {
  if (!input || typeof input !== 'string' || input.trim().length === 0) {
    return fallback;
  }
  return input.trim();
}

/**
 * Handle Windows reserved filenames by prefixing with 'file_'
 * Prevents issues when files are downloaded on Windows systems
 * 
 * @param filename - Filename to check
 * @returns Safe filename
 */
function handleReservedNames(filename: string): string {
  const nameWithoutExt = filename.split('.')[0].toUpperCase();
  if (RESERVED_WINDOWS_NAMES.includes(nameWithoutExt as any)) {
    return `file_${filename}`;
  }
  return filename;
}

/**
 * Limit filename length while preserving extension
 * Prevents filesystem errors due to overly long filenames
 * 
 * @param filename - Filename to limit
 * @param maxLength - Maximum allowed length
 * @returns Truncated filename with preserved extension
 */
function limitFilenameLength(filename: string, maxLength: number): string {
  if (filename.length <= maxLength) {
    return filename;
  }

  const parts = filename.split('.');
  if (parts.length === 1) {
    // No extension, simple truncation
    return filename.substring(0, maxLength);
  }

  // Preserve extension while truncating name
  const ext = parts.pop()!;
  const name = parts.join('.');
  const availableLength = maxLength - ext.length - 1; // -1 for the dot
  
  if (availableLength > 0) {
    return `${name.substring(0, availableLength)}.${ext}`;
  }
  
  // Edge case: extension longer than maxLength (very rare)
  return filename.substring(0, maxLength);
}

/**
 * Apply comprehensive security measures based on security level
 * Centralizes all security-related string processing to prevent duplication
 * 
 * @param input - String to secure
 * @param securityLevel - Level of security to apply
 * @param allowUnicode - Whether to preserve Unicode characters
 * @returns Secured string with appropriate character filtering
 */
function applySecurity(
  input: string, 
  securityLevel: SecurityLevel = 'standard',
  allowUnicode: boolean = true
): string {
  // Always apply basic security measures first
  let secured = input
    .replace(/\.\.[\/\\]/g, '') // Remove path traversal sequences
    .replace(/[\x00-\x1f\x80-\x9f]/g, '') // Remove control characters
    .replace(/^\.+/, '') // Remove leading dots (hidden file protection)
    .trim();

  // Apply security level specific processing
  switch (securityLevel) {
    case 'basic':
      // Minimal processing - only remove filesystem unsafe characters
      secured = secured.replace(/[\/\\:*?"<>|]/g, "");
      break;

    case 'standard':
      // Balanced approach - normalize spaces, remove unsafe chars
      secured = secured
        .replace(/[\/\\:*?"<>|]/g, "") // Remove filesystem unsafe chars
        .replace(/\s+/g, "-") // Convert spaces to hyphens
        .replace(/-+/g, "-") // Collapse multiple hyphens
        .replace(/^-+|-+$/g, ""); // Remove leading/trailing hyphens
      break;

    case 'strict':
      // Maximum security - for HTTP headers and sensitive contexts
      if (allowUnicode) {
        secured = secured
          .replace(/[\/\\:*?"<>|]/g, '_') // Replace unsafe chars with underscores
          .replace(/[\r\n\t]/g, '') // Remove line breaks and tabs
          .replace(/\s+/g, '_') // Convert spaces to underscores
          .replace(/[^\w\s.\-_]/g, '_'); // Replace special chars (keep Unicode word chars)
      } else {
        secured = secured
          .replace(/[\/\\:*?"<>|]/g, '_') // Replace unsafe chars
          .replace(/[^a-zA-Z0-9.\-_]/g, '_'); // ASCII only
      }
      break;
  }

  return secured;
}

// =============================================================================
// Core Sanitization Functions
// =============================================================================

/**
 * Sanitizes a single path segment (folder name, URL slug, etc.)
 * 
 * Security levels:
 * - basic: Only removes filesystem-unsafe characters
 * - standard: Normalizes spaces to hyphens, good for URLs
 * - strict: More aggressive character filtering for sensitive contexts
 * 
 * @param segment - Path segment to sanitize
 * @param options - Sanitization options
 * @returns Clean path segment
 * 
 * @example
 * ```typescript
 * // Standard usage (URL-friendly)
 * sanitizePathSegment("My Folder Name!")
 * // => "my-folder-name"
 * 
 * // Preserve case and Unicode
 * sanitizePathSegment("用户文档", { toLower: false })
 * // => "用户文档"
 * 
 * // Strict security
 * sanitizePathSegment("Folder/Name", { securityLevel: 'strict' })
 * // => "folder_name"
 * ```
 */
export function sanitizePathSegment(
  segment: string,
  options: SanitizeOptions = {}
): string {
  const {
    toLower = true,
    securityLevel = 'standard',
    allowUnicode = true
  } = options;

  const normalized = validateAndNormalize(segment, '');
  if (!normalized) return '';

  // Apply all security processing in one centralized function
  const secured = applySecurity(normalized, securityLevel, allowUnicode);

  return toLower ? secured.toLowerCase() : secured;
}

/**
 * Sanitizes a complete filename while preserving its extension
 * Automatically detects and preserves file extensions (including multi-part like .tar.gz)
 * 
 * @param filename - Full filename to sanitize
 * @param options - Sanitization options  
 * @returns Clean filename with preserved extension
 * 
 * @example
 * ```typescript
 * // Standard usage
 * sanitizeFileName("My Document.pdf")
 * // => "my-document.pdf"
 * 
 * // Preserve Unicode characters
 * sanitizeFileName("用户报告.docx", { allowUnicode: true })
 * // => "用户报告.docx"
 * 
 * // Strict security for HTTP headers
 * sanitizeFileName("Report (Final).pdf", { 
 *   securityLevel: 'strict',
 *   allowUnicode: false 
 * })
 * // => "report__final_.pdf"
 * 
 * // Multi-part extensions
 * sanitizeFileName("archive.tar.gz")
 * // => "archive.tar.gz"
 * ```
 */
export function sanitizeFileName(
  filename: string,
  options: SanitizeOptions = {}
): string {
  const {
    toLower = true,
    securityLevel = 'standard',
    allowUnicode = true,
    maxLength = 200
  } = options;

  const normalized = validateAndNormalize(filename, 'download');
  if (normalized === 'download') return 'download';

  const parts = normalized.split(".");

  if (parts.length === 1) {
    // No extension present
    const sanitized = sanitizePathSegment(parts[0], { toLower, securityLevel, allowUnicode });
    return sanitized || 'download';
  }

  // Handle filename with extension(s)
  const ext = parts.pop()!;
  const base = parts.join("."); // Support multi-part extensions like .tar.gz

  const sanitizedBase = sanitizePathSegment(base, { toLower, securityLevel, allowUnicode });
  const sanitizedExt = toLower ? ext.toLowerCase() : ext;

  const result = `${sanitizedBase}.${sanitizedExt}`;
  const withReservedCheck = handleReservedNames(result);
  const finalResult = limitFilenameLength(withReservedCheck, maxLength);

  return finalResult || 'download';
}

/**
 * Sanitizes a complete file path (multiple segments + optional filename)
 * Automatically detects whether the last segment is a filename and processes accordingly
 * 
 * @param path - Full file path to sanitize
 * @param options - Sanitization options
 * @returns Clean file path
 *
 * @example
 * ```typescript
 * // Path with filename
 * sanitizeFilePath("user uploads/reports/final report.pdf")
 * // => "user-uploads/reports/final-report.pdf"
 * 
 * // Directory path only
 * sanitizeFilePath("user uploads/documents")
 * // => "user-uploads/documents"
 * 
 * // International characters
 * sanitizeFilePath("用户上传/最终报告/report.docx")
 * // => "用户上传/最终报告/report.docx"
 * ```
 */
export function sanitizeFilePath(
  path: string,
  options: SanitizeOptions = {}
): string {
  const normalized = validateAndNormalize(path, '');
  if (!normalized) return '';

  const segments = normalized
    .split("/")
    .map(s => s.trim())
    .filter(Boolean);

  if (segments.length === 0) return "";

  const lastSegment = segments[segments.length - 1];
  const isFileName = /\.\w{1,10}$/.test(lastSegment);

  const dirSegments = isFileName
    ? segments.slice(0, -1).map(s => sanitizePathSegment(s, options))
    : segments.map(s => sanitizePathSegment(s, options));

  const filePart = isFileName
    ? sanitizeFileName(lastSegment, options)
    : null;

  return filePart
    ? [...dirSegments, filePart].join("/")
    : dirSegments.join("/");
}



// =============================================================================
// Usage Examples and Tests
// =============================================================================

/**
 * Example usage patterns for different scenarios:
 * 
 * ```typescript
 * // 1. URL Slugs and Path Segments
 * sanitizePathSegment("My Blog Post!")
 * // => "my-blog-post"
 * 
 * // 2. User Upload Filenames  
 * sanitizeFileName("用户文档 (最终版).pdf")
 * // => "用户文档-最终版.pdf"
 * 
 * // 3. Complete File Paths
 * sanitizeFilePath("uploads/用户文档/report (v2).docx")
 * // => "uploads/用户文档/report-v2.docx"
 * 
 * // 4. Multi-part File Extensions
 * sanitizeFileName("archive.tar.gz")
 * // => "archive.tar.gz"
 * 
 * // 5. High Security Contexts
 * sanitizeFileName("../../../etc/passwd", { 
 *   securityLevel: 'strict',
 *   allowUnicode: false 
 * })
 * // => "etc_passwd"
 * 
 * // 6. Preserve Original Case
 * sanitizePathSegment("CamelCase", { toLower: false })
 * // => "CamelCase"
 * 
 * // 7. Different Security Levels
 * const filename = "User File (v2)!.pdf";
 * 
 * sanitizeFileName(filename, { securityLevel: 'basic' })
 * // => "user file (v2)!.pdf"
 * 
 * sanitizeFileName(filename, { securityLevel: 'standard' })  
 * // => "user-file-v2.pdf"
 * 
 * sanitizeFileName(filename, { securityLevel: 'strict' })
 * // => "user_file__v2__.pdf"
 * ```
 */

