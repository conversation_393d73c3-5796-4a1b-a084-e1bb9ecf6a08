import { FileDataEntry } from "@/types/file";

// ================================
// File to Base64 Conversion Functions
// ================================

/**
 * Converts a file URL to pure base64 string (without data URL prefix)
 * @param url - The file URL to convert
 * @returns Promise that resolves to pure base64 string
 * @throws Error if the file URL cannot be fetched or converted
 */
export async function convertFileUrlToBase64(url: string): Promise<string> {
  try {
    const response = await fetch(url);
    const arrayBuffer = await response.arrayBuffer();
    const uint8Array = new Uint8Array(arrayBuffer);
    return convertUint8ArrayToBase64(uint8Array);
  } catch (error) {
    console.error('Error converting file URL to base64:', error);
    throw new Error('Failed to convert file URL to base64');
  }
}

/**
 * Converts a file URL to Uint8Array binary format
 * @param url - The file URL to convert
 * @returns Promise that resolves to Uint8Array containing the file's binary data
 * @throws Error if the file URL cannot be fetched or converted
 */
export async function convertFileUrlToUint8Array(url: string): Promise<Uint8Array> {
  try {
    const response = await fetch(url);
    const arrayBuffer = await response.arrayBuffer();
    return new Uint8Array(arrayBuffer);
  } catch (error) {
    console.error('Error converting file URL to Uint8Array:', error);
    throw new Error('Failed to convert file URL to Uint8Array');
  }
}

/**
 * Converts Uint8Array binary data to pure base64 string (without data URL prefix)
 * Uses chunked processing to handle large files efficiently
 * @param data - The Uint8Array data to convert
 * @returns Pure base64 string representation of the binary data
 */
export function convertUint8ArrayToBase64(data: Uint8Array): string {
  const chunks: string[] = [];
  for (let i = 0; i < data.length; i += 1024) {
    const chunk = data.slice(i, i + 1024);
    chunks.push(String.fromCharCode.apply(null, Array.from(chunk)));
  }
  return btoa(chunks.join(''));
}

/**
 * Converts pure base64 string to Uint8Array binary data
 * @param base64 - The pure base64 string to convert
 * @returns Uint8Array containing the decoded binary data
 * @throws Error if the base64 string is malformed
 */
export function convertBase64ToUint8Array(base64: string): Uint8Array {
  const binaryString = atob(base64);
  const length = binaryString.length;
  const bytes = new Uint8Array(length);
  for (let i = 0; i < length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes;
}

/**
 * Converts a File object to pure base64 string (without data URL prefix)
 * Extracts only the base64 data portion, removing the "data:mime/type;base64," prefix
 * @param file - The File object to convert
 * @returns Promise that resolves to pure base64 string
 * @throws Error if the file cannot be read
 */
export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      // Extract the base64 data from the DataURL format
      // Format: data:image/jpeg;base64,/9j/4AAQSk...
      const result = reader.result as string;
      const base64Data = result.split(',')[1];
      resolve(base64Data);
    };
    reader.onerror = error => reject(error);
  });
};

// ================================
// Core Base64 Processing Functions
// ================================

/**
 * Result interface for base64 parsing operations
 * Contains both the extracted data and metadata about the parsing process
 */
export interface Base64ParseResult {
  /** The extracted pure base64 data without any prefixes */
  pureBase64: string;
  /** MIME type extracted from data URL format (if present) */
  extractedMimeType?: string;
  /** Whether any prefix was detected and removed during parsing */
  hadPrefix: boolean;
  /** The detected input format type */
  format: 'dataUrl' | 'base64Prefix' | 'pure' | 'unknown';
}

/**
 * Parses base64 input in various formats and extracts pure base64 data with metadata
 * Supports multiple input formats:
 * - Data URL: "data:image/jpeg;base64,/9j/4AAQSk..."
 * - Base64 prefix: "base64,/9j/4AAQSk..."
 * - Pure base64: "/9j/4AAQSk..."
 * 
 * This is the authoritative function for base64 parsing logic in this module
 * @param input - The base64 string input in any supported format
 * @returns Parsing result containing extracted data and metadata
 */
export function parseBase64(input: string): Base64ParseResult {
  if (!input || typeof input !== 'string') {
    console.warn('Invalid base64 input: expected non-empty string');
    return { pureBase64: '', hadPrefix: false, format: 'unknown' };
  }

  // Handle data URL format: data:image/jpeg;base64,/9j/4AAQSk...
  if (input.startsWith('data:') && input.includes('base64,')) {
    const dataUrlMatch = input.match(/^data:([^;]+);base64,(.+)$/);
    if (dataUrlMatch && dataUrlMatch.length === 3) {
      const [, mimeType, base64Data] = dataUrlMatch;
      console.log('Parsed data URL format base64');
      return {
        pureBase64: base64Data,
        extractedMimeType: mimeType.toLowerCase(),
        hadPrefix: true,
        format: 'dataUrl'
      };
    }
  }

  // Handle base64 prefix format: base64,/9j/4AAQSk...
  if (input.includes('base64,')) {
    const parts = input.split('base64,');
    if (parts.length === 2 && parts[1]) {
      console.log('Parsed base64 prefix format');
      return {
        pureBase64: parts[1],
        hadPrefix: true,
        format: 'base64Prefix'
      };
    }
  }

  // Treat as pure base64
  console.log('Treating input as pure base64');
  return {
    pureBase64: input,
    hadPrefix: false,
    format: 'pure'
  };
}

// ================================
// MIME Type Validation Functions
// ================================

/**
 * Result interface for MIME type validation operations
 * Contains validation status and parsed components
 */
export interface MimeTypeValidationResult {
  /** Whether the MIME type is valid */
  isValid: boolean;
  /** Main type component (e.g., 'image', 'video', 'audio') */
  type?: string;
  /** Subtype component (e.g., 'jpeg', 'mp4', 'mp3') */
  subtype?: string;
  /** Detailed error message if validation fails */
  error?: string;
}

/**
 * Validates the format and structure of a MIME type string
 * Checks for proper format (type/subtype) and valid characters
 * @param mimeType - The MIME type string to validate
 * @returns Validation result with parsed components and error details
 */
export function validateMimeTypeFormat(mimeType: string): MimeTypeValidationResult {
  try {
    // Check if mimeType is empty or not a string
    if (!mimeType || typeof mimeType !== 'string') {
      return {
        isValid: false,
        error: 'MIME type is required and must be a string'
      };
    }

    // Split MIME type into type and subtype
    const [type, subtype] = mimeType.toLowerCase().split('/');

    // Check if both type and subtype exist
    if (!type || !subtype) {
      return {
        isValid: false,
        error: 'Invalid MIME type format. Expected format: type/subtype'
      };
    }

    // Validate type component contains only valid characters
    const validTypeRegex = /^[a-z0-9][a-z0-9!#$&^_.-]*$/;
    if (!validTypeRegex.test(type)) {
      return {
        isValid: false,
        error: 'Invalid characters in MIME type'
      };
    }

    // Validate subtype component contains only valid characters
    const validSubtypeRegex = /^[a-z0-9][a-z0-9!#$&^_.-]*$/;
    if (!validSubtypeRegex.test(subtype)) {
      return {
        isValid: false,
        error: 'Invalid characters in MIME subtype'
      };
    }

    // All validations passed
    return {
      isValid: true,
      type,
      subtype
    };
  } catch (error) {
    console.error('Error validating MIME type format:', error);
    return {
      isValid: false,
      error: 'Unexpected error during MIME type validation'
    };
  }
}

/**
 * Validates a FileDataEntry object's MIME type and base64 data consistency
 * Performs comprehensive validation including:
 * - MIME type format validation
 * - Base64 data presence check
 * - MIME type consistency between FileDataEntry and embedded data URL
 * @param fileData - The FileDataEntry object to validate
 * @returns Validation result with detailed error information
 */
export function validateFileDataEntry(fileData: FileDataEntry): MimeTypeValidationResult {
  try {
    // First validate the MIME type format
    const mimeValidation = validateMimeTypeFormat(fileData.mimeType);
    if (!mimeValidation.isValid) {
      return mimeValidation;
    }

    // Check if base64Data is present
    if (!fileData.base64Data) {
      return {
        isValid: false,
        error: 'Base64 data is required'
      };
    }

    // Parse base64 data and check for MIME type conflicts
    const base64Result = parseBase64(fileData.base64Data);
    if (base64Result.extractedMimeType) {
      const providedMimeType = fileData.mimeType.toLowerCase();
      const extractedMimeType = base64Result.extractedMimeType;
      
      if (providedMimeType !== extractedMimeType) {
        return {
          isValid: false,
          error: `MIME type mismatch: FileDataEntry contains '${providedMimeType}' but data URL contains '${extractedMimeType}'`
        };
      }
    }

    // All validations passed
    return {
      isValid: true,
      type: mimeValidation.type,
      subtype: mimeValidation.subtype
    };
  } catch (error) {
    console.error('Error validating FileDataEntry:', error);
    return {
      isValid: false,
      error: 'Unexpected error during FileDataEntry validation'
    };
  }
}

/**
 * Download file from URL to server (for server-side processing)
 * Returns file content as Buffer for further processing
 */
export async function fetchFileAsBuffer(url: string): Promise<{
  buffer: Buffer;
  contentType?: string;
  contentLength?: number;
}> {
  const response = await fetch(url);
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  const buffer = Buffer.from(await response.arrayBuffer());
  const contentType = response.headers.get('content-type') || undefined;
  const contentLength = parseInt(response.headers.get('content-length') || '0') || undefined;

  return {
    buffer,
    contentType,
    contentLength
  };
}
