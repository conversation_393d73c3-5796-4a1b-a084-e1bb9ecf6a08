import {
  PresignedUploadRequest,
  PresignedUploadResponse,
  ConfirmUploadRequest,
  PresignedUploadInfo,
  CleanupUploadRequest,
  UploadResult,
} from "@/types/file-upload";
import { FileCategory, FileInfo } from "@/types/file";

/**
 * Client-side File Upload Service
 *
 * @description
 * This class encapsulates the entire client-side logic for the presigned URL upload flow.
 * It provides a simple `uploadFiles` method that handles the multi-step process:
 * 1. Requesting presigned URLs from the application server.
 * 2. Uploading the file(s) directly to cloud storage using the provided URLs.
 * 3. Confirming the successful uploads with the server to finalize the process.
 * 4. Cleaning up any failed uploads.
 *
 * It uses XMLHttpRequest for uploads to provide progress tracking capabilities.
 *
 * @example
 * ```
 * import { clientUploadService } from '@/lib/client-upload-service';
 *
 * const handleFileUpload = async (files) => {
 *   const results = await clientUploadService.uploadFiles(files, { fileSource: 'user_upload' });
 *   console.log(results);
 * };
 * ```
 */
export class ClientUploadService {
  private readonly baseUrl: string;

  constructor(baseUrl: string = '') {
    this.baseUrl = baseUrl;
  }

  /**
   * Upload multiple files using presigned URLs
   * Supports both single file (array length 1) and batch uploads
   * @param files Array of File objects to upload
   * @param userUuid User UUID for authentication
   * @returns Array of upload results
   */
  async uploadFiles(
    files: File[],
    userUuid?: string
  ): Promise<UploadResult[]> {
    try {
      console.log(`Starting upload of ${files.length} files`);

      // 1. Request presigned upload infos
      const presignedUploadResponse = 
        await this.requestPresignedUploadInfos(files, userUuid);
      const presignedUploadInfos = presignedUploadResponse.presignedUploadInfos;
      console.log('Received presigned upload infos', { count: presignedUploadInfos.length });

      if (presignedUploadInfos.length !== files.length) {
        throw new Error('Received presigned upload infos length mismatch, expected: ' + files.length + ', got: ' + presignedUploadInfos.length);
      }

      // 2. Upload files to cloud storage in parallel
      const uploadPromises = files.map((file, index) =>
        this.uploadToCloudStorage(
          file,
          presignedUploadInfos[index],
          (progress) => {
            // Optional: You can add progress callbacks here
            console.log(`Upload progress for ${file.name}: ${progress}%`);
          }
        )
      );

      const uploadResults = await Promise.allSettled(uploadPromises);

      // 3. Process upload results
      const successfulUploads: string[] = [];
      const failedUploads: string[] = [];
      const results: UploadResult[] = [];

      uploadResults.forEach((result, index) => {
        const file = files[index];
        const uploadInfo = presignedUploadInfos[index];

        if (result.status === 'fulfilled') {
          successfulUploads.push(uploadInfo.fileId);
          results.push({
            fileId: uploadInfo.fileId,
            success: true,
            fileName: file.name,
            fileSize: file.size,
            mimeType: file.type,
          });
        } else {
          failedUploads.push(uploadInfo.fileId);
          results.push({
            fileId: uploadInfo.fileId,
            success: false,
            fileName: file.name,
            fileSize: file.size,
            mimeType: file.type,
            error: result.reason?.message || 'Upload failed',
          });
        }
      });

      console.log('Upload results processed', {
        successful: successfulUploads.length,
        failed: failedUploads.length
      });

      // 4. Confirm successful uploads and get file info
      if (successfulUploads.length > 0) {
        try {
          const confirmedFiles = 
            await this.confirmBatchUpload(successfulUploads, userUuid);
          
          // Update results with confirmed file info (like publicUrl for public files)
          confirmedFiles.forEach(confirmedFile => {
            const resultIndex = results.findIndex(r => r.fileId === confirmedFile.fileId);
            if (resultIndex !== -1) {
              // The server returns FileInfo which does not contain a URL.
              // For presigned uploads (private files), there is no permanent URL to return.
              // The client should handle previewing based on its own logic if needed,
              // as the file is now confirmed and active on the server.
              // We can update other fields if necessary, e.g., final server-side confirmed data.
            }
          });

          console.log('Successfully confirmed uploads', { count: confirmedFiles.length });
        } catch (error) {
          console.error('Failed to confirm uploads, but files were uploaded:', error);
          // Don't fail the entire operation if confirmation fails
        }
      }

      // 5. Clean up failed uploads
      if (failedUploads.length > 0) {
        try {
          await this.cleanupFailedUploads(failedUploads, userUuid);
          console.log('Cleaned up failed uploads', { count: failedUploads.length });
        } catch (error) {
          console.warn('Failed to cleanup failed uploads:', error);
          // Don't fail the entire operation if cleanup fails
        }
      }

      console.log('Upload operation completed', {
        totalFiles: files.length,
        successful: successfulUploads.length,
        failed: failedUploads.length
      });

      return results;
    } catch (error) {
      console.error('Upload operation failed:', error);
      throw new Error(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Requests a batch of presigned URLs from the server API.
   * @param files An array of `File` objects for which to request URLs.
   * @param userUuid User UUID for authentication.
   * @returns A promise resolving to the server's `PresignedUploadResponse`.
   */
  private async requestPresignedUploadInfos(
    files: File[],
    userUuid?: string
  ): Promise<PresignedUploadResponse> {
    const requestBody: PresignedUploadRequest = {
      filesInfo: files.map(file => ({
        fileName: file.name,
        mimeType: file.type,
        fileSize: file.size,
      })),
      fileCategory: FileCategory.USER_UPLOADS,
    };

    const response = await fetch(`${this.baseUrl}/api/files/upload/presigned`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(userUuid ? { 'x-user-uuid': userUuid } : {}),
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
      throw new Error(`Failed to get presigned upload URLs: ${errorData.message || response.statusText}`);
    }

    const responseJson = await response.json();
    
    if (responseJson.code !== 0) {
      throw new Error(`Server error: ${responseJson.message}`);
    }

    return responseJson.data as PresignedUploadResponse;
  }

  /**
   * Upload a single file to cloud storage using presigned URL
   * @param file The `File` object to upload.
   * @param presignedUpload The presigned URL and fields data from the server.
   * @param onProgress An optional callback function to track upload progress (0-100).
   * @returns A promise that resolves on successful upload or rejects on failure.
   */
  private async uploadToCloudStorage(
    file: File,
    presignedUploadInfo: PresignedUploadInfo,
    onProgress?: (progress: number) => void
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      // Set up progress tracking
      if (onProgress) {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100);
            onProgress(progress);
          }
        });
      }

      // Set up completion handlers
      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          resolve();
        } else {
          reject(new Error(`Upload failed: ${xhr.status} ${xhr.statusText}`));
        }
      });

      xhr.addEventListener('error', (event) => {
        console.error('Upload failed with network error:', {
          status: xhr.status,
          readyState: xhr.readyState,
          responseText: xhr.responseText
        });
        reject(new Error('Upload failed: Network error'));
      });

      xhr.addEventListener('abort', () => {
        reject(new Error('Upload aborted'));
      });

      xhr.open('PUT', presignedUploadInfo.presignedUrl);
      
      xhr.setRequestHeader('Content-Type', file.type);
      
      xhr.send(file);
    });
  }

  /**
   * Confirms a batch of successful uploads with the server.
   * @param fileIds An array of file IDs that were successfully uploaded to cloud storage.
   * @param userUuid User UUID for authentication.
   * @returns A promise resolving to an array of `FileInfo` objects for the confirmed files.
   */
  private async confirmBatchUpload(fileIds: string[], userUuid?: string): Promise<FileInfo[]> {
    const requestBody: ConfirmUploadRequest = { fileIds };

    const response = await fetch(`${this.baseUrl}/api/files/upload/confirm`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(userUuid ? { 'x-user-uuid': userUuid } : {}),
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
      throw new Error(`Failed to confirm uploads: ${errorData.message || response.statusText}`);
    }

    const responseJson = await response.json();
    
    if (responseJson.code !== 0) {
      throw new Error(`Server error: ${responseJson.message}`);
    }

    return responseJson.data.confirmedFiles as FileInfo[];
  }

  /**
   * Reports failed uploads to the server for cleanup.
   * This is a "fire and forget" operation; it logs warnings on failure but doesn't throw errors.
   * @param fileIds An array of file IDs that failed to upload.
   * @param userUuid User UUID for authentication.
   */
  private async cleanupFailedUploads(fileIds: string[], userUuid?: string): Promise<void> {
    const requestBody: CleanupUploadRequest = { fileIds };

    try {
      const response = await fetch(`${this.baseUrl}/api/files/upload/cleanup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(userUuid ? { 'x-user-uuid': userUuid } : {}),
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        console.warn('Failed to cleanup uploads:', response.statusText);
      }
    } catch (error) {
      console.warn('Failed to cleanup uploads:', error);
    }
  }
}

// Export singleton instance
export const clientUploadService = new ClientUploadService();
