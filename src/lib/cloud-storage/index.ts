/**
 * Conditional Cloud Storage Module
 * 
 * This module automatically selects the appropriate cloud storage implementation
 * based on the DEPLOYMENT_TARGET environment variable, optimizing bundle size:
 * 
 * - DEPLOYMENT_TARGET=cloudflare: Uses aws4fetch (~25KB bundle)
 * - DEPLOYMENT_TARGET=node: Uses AWS SDK (~580KB bundle, better performance)
 * - Default: Auto-detection based on runtime environment
 * 
 * Bundle optimization through conditional imports ensures only the required
 * dependencies are included in each deployment target.
 * 
 * ===== AI ASSISTANT USAGE GUIDE =====
 * 
 * ALWAYS import functions and types from this file:
 * 
 * ```typescript
 * // ✅ CORRECT - Import from the main entry point
 * import { 
 *   createPresignedPreviewUrl,
 *   createPresignedDownloadUrl,
 *   createPresignedUploadUrl,
 *   uploadFromUrl,
 *   uploadFromBuffer,
 *   deleteFiles,
 *   isOwnStorageUrl,
 *   verifyFileExists,
 * } from '@/lib/cloud-storage';
 * 
 * // ✅ CORRECT - Import types from the main entry point
 * import type { 
 *   StorageConfig,
 *   PresignedUploadResult,
 *   UploadResult,
 *   FileExistsResult,
 * } from '@/lib/cloud-storage';
 * 
 * // ❌ WRONG - Do NOT import from implementation files
 * import { ... } from '@/lib/cloud-storage/cloud-storage-aws';
 * import { ... } from '@/lib/cloud-storage/cloud-storage-cf';
 * import { ... } from '@/lib/cloud-storage/types';
 * ```
 * 
 * This ensures proper conditional loading and bundle optimization.
 */

// Import types for reference elision (TypeScript will remove these from JS output)
import type * as CloudStorageAWS from './cloud-storage-aws';
import type * as CloudStorageCF from './cloud-storage-cf';
import { getTargetPlatform, targetPlatform } from '@/lib/platform';

// Conditional module loading with dead code elimination support
let cloudStorageModule: typeof CloudStorageAWS | typeof CloudStorageCF;

/**
 * Build-time dead code elimination:
 * Bundlers like Webpack/Terser will eliminate the unused branch
 * based on the DEPLOYMENT_TARGET environment variable
 */
if (targetPlatform === 'cloudflare') {
  // Cloudflare Workers / Edge Runtime - aws4fetch version
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  cloudStorageModule = require('./cloud-storage-cf');
  console.log('Cloud Storage: Using aws4fetch implementation for Cloudflare Workers');
} else {
  // Node.js Runtime - AWS SDK version (default)
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  cloudStorageModule = require('./cloud-storage-aws');
  console.log('Cloud Storage: Using AWS SDK implementation for Node.js');
}

// Export unified interface
export const {
  createPresignedPreviewUrl,
  createPresignedDownloadUrl,
  createPresignedUploadUrl,
  uploadFromUrl,
  uploadFromBuffer,
  deleteFiles,
  isOwnStorageUrl,
  verifyFileExists,
} = cloudStorageModule;

// Re-export all types from the unified interface
export type {
  StorageConfig,
  PreviewUrlParams,
  DownloadUrlParams,
  UploadUrlParams,
  PresignedUploadResult,
  UploadResult,
  FileExistsResult,
  CloudStorageProvider,
} from './types';
