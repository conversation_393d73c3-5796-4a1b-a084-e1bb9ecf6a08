import { 
  S3<PERSON>lient, 
  DeleteO<PERSON><PERSON>ommand, 
  GetObjectCommand, 
  Head<PERSON><PERSON>Command,
  PutObjectCommand
} from "@aws-sdk/client-s3";
import { Upload } from "@aws-sdk/lib-storage";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import logger from "../logger";
import type {
  StorageConfig,
  PreviewUrlParams,
  DownloadUrlParams,
  UploadUrlParams,
  PresignedUploadResult,
  UploadResult,
  FileExistsResult,
  CloudStorageProvider,
} from './types';

/**
 * Cloud Storage Utilities Layer - AWS SDK Implementation
 * Provides high-level cloud storage operations with business-specific logic.
 * This layer handles presigned URLs, file operations, and direct storage interactions.
 * 
 * This implementation uses AWS SDK v3 for optimal performance in Node.js environments.
 * 
 * 🔑 KEY KNOWLEDGE POINTS:
 * 
 * 1. 🌐 URL FORMATS:
 *    - AWS SDK automatically generates Virtual-hosted-style URLs
 *    - Format: https://bucket.endpoint.com/key (modern standard)
 *    - Compatible with Cloudflare R2 and all S3-compatible storage
 * 
 * 2. 🎯 CUSTOM DOMAIN USAGE:
 *    - Preview/Download URLs: ✅ Can use custom domains (GET requests)
 *    - Upload URLs: ❌ Must use original R2 domain (PUT requests, signature validation)
 * 
 * 3. 🔐 CORS CONFIGURATION:
 *    - Upload operations require CORS on original R2 domain
 *    - Download/Preview can use CORS on custom domain
 * 
 * 4. 🔧 AWS SDK vs aws4fetch:
 *    - AWS SDK: Full featured, Node.js optimized (~580KB)
 *    - aws4fetch: Lightweight, Edge Runtime compatible (~25KB)
 *    - Both generate compatible presigned URLs
 * 
 * ===== AI ASSISTANT USAGE GUIDE =====
 * 
 * ❌ DO NOT import functions directly from this file!
 * 
 * This is an internal implementation file. Always import from the main entry point:
 * 
 * ```typescript
 * // ✅ CORRECT - Import from the main entry point
 * import { 
 *   createPresignedPreviewUrl,
 *   uploadFromBuffer,
 *   // ... other functions
 * } from '@/lib/cloud-storage';
 * 
 * // ❌ WRONG - Do NOT import from this file
 * import { ... } from '@/lib/cloud-storage/cloud-storage-aws';
 * ```
 * 
 * The main entry point will automatically select this implementation when
 * running in Node.js environments or when DEPLOYMENT_TARGET=node.
 */

// ===== Storage Client =====

// StorageConfig is now imported from './types'

/**
 * Get or create S3 client instance
 */
function getS3Client(config?: StorageConfig): S3Client {
  return new S3Client({
    endpoint: config?.endpoint || process.env.STORAGE_ENDPOINT || "",
    region: config?.region || process.env.STORAGE_REGION || "auto",
    credentials: {
      accessKeyId: config?.accessKey || process.env.STORAGE_ACCESS_KEY || "",
      secretAccessKey: config?.secretKey || process.env.STORAGE_SECRET_KEY || "",
    },
  });
}

/**
 * Get bucket name from environment or parameter
 */
function getBucket(bucket?: string): string {
  const effectiveBucket = bucket || process.env.STORAGE_BUCKET || "";
  if (!effectiveBucket) {
    throw new Error("Bucket is required");
  }
  return effectiveBucket;
}

// ===== Presigned URL Functions =====

/**
 * Create presigned URL for file preview (inline viewing in browser).
 * 
 * ✅ CUSTOM DOMAIN SUPPORT: Preview URLs can use custom domains because:
 * 1. 📖 READ-ONLY OPERATION: GET requests don't modify data, less security restrictions
 * 2. 🌐 DOMAIN FLEXIBILITY: Custom domains can proxy/redirect to original R2 URLs
 * 3. 🎨 USER EXPERIENCE: Custom domains provide better branding and caching
 * 
 * The function automatically replaces R2 domain with custom domain if configured.
 * Fallback: If custom domain replacement fails, original R2 URL is returned.
 */
export async function createPresignedPreviewUrl(params: PreviewUrlParams): Promise<string> {
  const s3 = getS3Client();
  const bucket = getBucket();
  const expiresIn = params.expiresIn || 3600; // 1 hour default
  
  try {
    const command = new GetObjectCommand({
      Bucket: bucket,
      Key: params.storageKey,
      ResponseContentDisposition: 'inline',
    });

    const presignedUrl = await getSignedUrl(s3, command, { expiresIn });
    
    // Replace R2 domain with custom domain if available
    if (process.env.STORAGE_DOMAIN && process.env.STORAGE_ENDPOINT) {
      try {
        const presignedUrlObj = new URL(presignedUrl);
        const customDomainUrlObj = new URL(process.env.STORAGE_DOMAIN);
        presignedUrlObj.hostname = customDomainUrlObj.hostname;
        presignedUrlObj.protocol = customDomainUrlObj.protocol;
        if (customDomainUrlObj.port) {
          presignedUrlObj.port = customDomainUrlObj.port;
        }
        return presignedUrlObj.toString();
      } catch (error) {
        logger.error(
          "Failed to replace domain in presigned URL, using original URL", error, {
            storageKey: params.storageKey,
          },{
            filePath: "lib/cloud-storage-aws.ts",
            functionName: 'createPresignedPreviewUrl'
          });
        return presignedUrl;
      }
    }
    
    return presignedUrl;
  } catch (error) {
    logger.error(
      `Failed to create presigned preview URL for key ${params.storageKey}`, 
      error, {
        storageKey: params.storageKey,
      }, {
        filePath: "lib/cloud-storage-aws.ts",
        functionName: 'createPresignedPreviewUrl'
      });
    throw new Error("Failed to generate presigned preview URL");
  }
}

/**
 * Create presigned URL for file download (force download with custom filename).
 * 
 * ✅ CUSTOM DOMAIN SUPPORT: Download URLs can use custom domains because:
 * 1. 📖 READ-ONLY OPERATION: GET requests don't modify data, less security restrictions
 * 2. 🌐 DOMAIN FLEXIBILITY: Custom domains can proxy/redirect to original R2 URLs
 * 3. 📁 FILENAME CONTROL: Custom domains maintain proper Content-Disposition headers
 * 
 * The function automatically replaces R2 domain with custom domain if configured.
 * Fallback: If custom domain replacement fails, original R2 URL is returned.
 */
export async function createPresignedDownloadUrl(params: DownloadUrlParams): Promise<string> {
  const s3 = getS3Client();
  const bucket = getBucket();
  const expiresIn = params.expiresIn || 1800; // 30 minutes default
  
  try {
    // Generate proper Content-Disposition header with international support
    const generateContentDisposition = (filename: string): string => {
      const encodedFilename = encodeURIComponent(filename);
      return `attachment; filename*=UTF-8''${encodedFilename}`;
    };

    const command = new GetObjectCommand({
      Bucket: bucket,
      Key: params.storageKey,
      ResponseContentDisposition: params.customFilename
        ? generateContentDisposition(params.customFilename)
        : 'attachment',
    });

    const presignedUrl = await getSignedUrl(s3, command, { expiresIn });
    
    // Replace R2 domain with custom domain if available
    if (process.env.STORAGE_DOMAIN && process.env.STORAGE_ENDPOINT) {
      try {
        const presignedUrlObj = new URL(presignedUrl);
        const customDomainUrlObj = new URL(process.env.STORAGE_DOMAIN);
        presignedUrlObj.hostname = customDomainUrlObj.hostname;
        presignedUrlObj.protocol = customDomainUrlObj.protocol;
        if (customDomainUrlObj.port) {
          presignedUrlObj.port = customDomainUrlObj.port;
        }

        logger.info(
          `Presigned download URL created with custom domain`, {
            storageKey: params.storageKey,
            storageDomain: process.env.STORAGE_DOMAIN,
            storageEndpoint: process.env.STORAGE_ENDPOINT,
            presignedUrlWithCustomDomain: presignedUrlObj.toString(),
          }, {
            filePath: "lib/cloud-storage-aws.ts",
            functionName: 'createPresignedDownloadUrl'
          });
        
        return presignedUrlObj.toString();
      } catch (error) {
        logger.error(
          "Failed to replace domain in presigned URL, using original URL", 
          error, {
            storageKey: params.storageKey,
          }, {
            filePath: "lib/cloud-storage-aws.ts",
            functionName: 'createPresignedDownloadUrl'
          });
        return presignedUrl;
      }
    }

    logger.info(
      `Presigned download URL created`, {
        storageKey: params.storageKey,
      }, {
        filePath: "lib/cloud-storage-aws.ts",
        functionName: 'createPresignedDownloadUrl'
      });
    
    return presignedUrl;
  } catch (error) {
    logger.error(
      `Failed to create presigned download URL`, 
      error, {
        storageKey: params.storageKey,
      }, {
        filePath: "lib/cloud-storage-aws.ts",
        functionName: 'createPresignedDownloadUrl'
      });
    throw new Error("Failed to generate presigned download URL");
  }
}

/**
 * Create a presigned URL for direct client-side uploading (PUT method).
 * Client is responsible for all file validation and processing.
 * 
 * ⚠️ IMPORTANT: Upload URLs vs Download/Preview URLs
 * 
 * Unlike preview/download URLs, presigned UPLOAD URLs CANNOT use custom domains.
 * This is a fundamental limitation of S3-compatible storage systems including Cloudflare R2.
 * 
 * Why Upload URLs Must Use Original Domain:
 * 1. 🔐 SIGNATURE VALIDATION: The AWS signature is cryptographically tied to the exact hostname
 * 2. 📋 S3 API REQUIREMENT: PUT operations require the original S3 endpoint for proper authentication
 * 3. 🌐 CORS CONFIGURATION: Upload CORS must be configured on the original R2 domain, not custom domain
 * 
 * URL Format Differences:
 * - Preview/Download: https://cdn.windflow.dev/path/to/file (✅ Custom domain OK)
 * - Upload:          https://bucket.account.r2.cloudflarestorage.com/path/to/file (❌ Original domain REQUIRED)
 * 
 * References:
 * - Cloudflare R2 Docs: "Presigned URLs can only be used with <accountid>.r2.cloudflarestorage.com"
 * - AWS S3 Docs: Presigned URLs require exact hostname match for signature validation
 */
export async function createPresignedUploadUrl(params: UploadUrlParams): Promise<PresignedUploadResult> {
  const s3 = getS3Client();
  const bucket = getBucket();
  const expiresIn = params.expiresIn || 3600; // 1 hour default
  
  try {
    // Calculate expiration time
    const expiresAt = new Date(Date.now() + expiresIn * 1000).toISOString();

    // Use PUT method for direct upload
    const command = new PutObjectCommand({
      Bucket: bucket,
      Key: params.storageKey,
      ContentType: params.mimeType,
    });

    const presignedUrl = await getSignedUrl(s3, command, { expiresIn });

    return {
      presignedUrl,
      fields: {}, // For PUT uploads, no form fields needed
      storageKey: params.storageKey,
      expiresAt,
    };
  } catch (error) {
    logger.error(
      `Failed to create presigned upload URL`, 
      error, {
        storageKey: params.storageKey,
      }, {
        filePath: "lib/cloud-storage-aws.ts",
        functionName: 'createPresignedUploadUrl'
      });
    throw new Error("Failed to generate presigned upload URL");
  }
}


// ===== Direct Storage Operations =====

/**
 * Upload a file from a URL to storage.
 * @param url The source URL to download from.
 * @param storageKey The destination key in the storage bucket.
 * @param contentType The MIME type of the file.
 * @returns The result from the storage backend.
 */
export async function uploadFromUrl(
  url: string, 
  storageKey: string, 
  contentType: string
): Promise<UploadResult> {
  const s3 = getS3Client();
  const bucket = getBucket();
  
  const maxRetries = 3;
  const timeout = 30000; // 30 seconds timeout
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // Create AbortController for timeout handling
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);
      
      try {
        logger.info(
          `Downloading from ${url}, attempt ${attempt}/${maxRetries}`, {
            url,
            attempt,
            maxRetries,
          }, {
            filePath: "lib/cloud-storage-aws.ts",
            functionName: 'uploadFromUrl'
          });
        
        // Fetch the file from the provided URL with timeout
        const response = await fetch(url, {
          signal: controller.signal,
          headers: {
            'User-Agent': 'STWD-Bot/1.0',
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive'
          },
          // Add these for better network handling
          method: 'GET',
          redirect: 'follow',
          referrerPolicy: 'no-referrer'
        });
        
        clearTimeout(timeoutId);
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status} ${response.statusText}`);
        }

        if (!response.body) {
          throw new Error("No body in response");
        }

        // Convert the response to an ArrayBuffer
        const arrayBuffer = await response.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);

        // Log more details about the downloaded file
        const actualContentType = response.headers.get('content-type') || contentType;
        const contentLength = response.headers.get('content-length');

        logger.info(
          `Successfully downloaded file from ${url} (${buffer.length} bytes) on attempt ${attempt}`, {
            url,
            bufferLength: buffer.length,
            actualContentType,
            contentLength,
            attempt,
          }, {
            filePath: "lib/cloud-storage-aws.ts",
            functionName: 'uploadFromUrl'
          });

        // Upload the file to storage with actual content type
        const upload = new Upload({
          client: s3,
          params: {
            Bucket: bucket,
            Key: storageKey,
            Body: buffer,
            ContentType: actualContentType,
            ContentDisposition: "inline",
          },
        });

        const res = await upload.done();
        
        // Use safe key for consistent null handling across implementations
        const safeKey = res.Key || storageKey;

        return {
          url: res.Location || "",
          bucket: res.Bucket || "",
          key: res.Key || "",
          filename: safeKey?.split("/").pop(),
          fileSize: buffer.length, // Add actual file size from downloaded buffer
          customDomainUrl: process.env.STORAGE_DOMAIN
            ? `${process.env.STORAGE_DOMAIN}/${safeKey}`
            : res.Location || "",
        };
        
      } finally {
        clearTimeout(timeoutId);
      }
      
    } catch (error) {
      logger.error(
        `Download attempt ${attempt}/${maxRetries} failed for ${url}`, 
        error, {
          url,
          errorName: error instanceof Error ? error.name : 'Unknown',
          errorMessage: error instanceof Error ? error.message : String(error),
        }, {
          filePath: "lib/cloud-storage-aws.ts",
          functionName: 'uploadFromUrl'
        });
      
      if (attempt === maxRetries) {
        throw new Error(`Failed to download file after ${maxRetries} attempts: ${error instanceof Error ? error.message : String(error)}`);
      }
      
      // Wait before retrying (exponential backoff)
      const waitTime = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s...
      logger.info(`Waiting ${waitTime}ms before retry...`, {
        waitTime,
        nextAttempt: attempt + 1,
      }, {
        filePath: "lib/cloud-storage-aws.ts",
        functionName: 'uploadFromUrl'
      });
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
  
  throw new Error('Unexpected end of retry loop');
}

/**
 * Upload a file from a buffer to storage.
 * @param buffer The file content as a Buffer.
 * @param storageKey The destination key in the storage bucket.
 * @param contentType The MIME type of the file.
 * @returns The result from the storage backend.
 */
export async function uploadFromBuffer(buffer: Buffer, storageKey: string, contentType: string): Promise<UploadResult> {
  const s3 = getS3Client();
  const bucket = getBucket();
  
  try {
    // Create upload instance
    const upload = new Upload({
      client: s3,
      params: {
        Bucket: bucket,
        Key: storageKey,
        Body: buffer,
        ContentType: contentType,
        ContentDisposition: 'inline',
      },
    });

    // Execute the upload
    const res = await upload.done();
    
    // Use safe key for consistent null handling across implementations
    const safeKey = res.Key || storageKey;

    return {
      url: res.Location || "",
      bucket: res.Bucket || "",
      key: res.Key || "",
      filename: safeKey?.split("/").pop(),
      fileSize: buffer.length, // Add actual file size from buffer
      customDomainUrl: process.env.STORAGE_DOMAIN
        ? `${process.env.STORAGE_DOMAIN}/${safeKey}`
        : res.Location || "",
    };
  } catch (error) {
    logger.error(
      `Failed to upload buffer to storage at key ${storageKey}`, 
      error, {
        storageKey,
      }, {
        filePath: "lib/cloud-storage-aws.ts",
        functionName: 'uploadFromBuffer'
      });
    throw new Error("Failed to upload file from buffer");
  }
}


/**
 * Delete multiple files from the storage service.
 * @param keys Array of keys of the files to delete.
 * @returns Array of booleans indicating success for each deletion.
 */
export async function deleteFiles(keys: string[]): Promise<boolean[]> {
  if (!keys || !Array.isArray(keys) || keys.length === 0) {
    throw new Error('Keys array cannot be empty');
  }

  const s3 = getS3Client();
  const bucket = getBucket();
  
  const deletePromises = keys.map(async (key) => {
    if (!key) {
      logger.error(
        'Attempted to delete a file with an empty key.', undefined, {
          key,
        }, {
          filePath: "lib/cloud-storage-aws.ts",
          functionName: 'deleteFiles'
        });
      return false;
    }

    try {
      const command = new DeleteObjectCommand({
        Bucket: bucket,
        Key: key,
      });
      
      await s3.send(command);
      return true;
    } catch (error) {
      logger.error(
        `Error deleting file from storage: ${key}`, 
        error,
        {
          key,
        }, {
          filePath: "lib/cloud-storage-aws.ts",
          functionName: 'deleteFiles'
        }
      );
      return false;
    }
  });
  
  return Promise.all(deletePromises);
}

// ===== Helper Functions =====

/**
 * Check if a URL belongs to our configured storage domain.
 */
export function isOwnStorageUrl(url: string): boolean {
  const storageDomain = process.env.STORAGE_DOMAIN;
  if (!storageDomain) return false;
  // Also check for the raw R2 domain for robustness
  // 应该检查是否包含存储桶名称字段吧？---------------------------------------------
  return url.startsWith(storageDomain) || url.includes('.r2.cloudflarestorage.com');
}

/**
 * Verify if a file exists in cloud storage.
 * @param storageKey The key of the file to verify.
 * @returns File metadata if it exists, otherwise null.
 */
export async function verifyFileExists(
  storageKey: string
): Promise<FileExistsResult> {
  try {
    const s3 = getS3Client();
    const bucket = getBucket();
    
    const command = new HeadObjectCommand({
      Bucket: bucket,
      Key: storageKey,
    });

    const response = await s3.send(command);

    return {
      exists: true,
      etag: response.ETag?.replace(/"/g, ""), // Remove quotes from ETag
      size: response.ContentLength,
    };
  } catch (error: any) {
    // If object doesn't exist, return exists: false
    if (error.name === "NotFound" || error.$metadata?.httpStatusCode === 404) {
      return { exists: false };
    }
    
    logger.error(
      'Failed to verify file existence', 
      error, {
        storageKey,
      }, {
        filePath: "lib/cloud-storage-aws.ts",
        functionName: 'verifyFileExists'
      });
    return { exists: false };
  }
}
