import { NextResponse } from "next/server";

export function respData(data: any) {
  return respJson(0, "ok", data || []);
}

export function respOk() {
  return respJson(0, "ok");
}

export function respErr(message: string) {
  return respJson(-1, message);
}

export function respJson(code: number, message: string, data?: any) {
  let json = {
    code: code,
    message: message,
    data: data,
  };
  if (data) {
    json["data"] = data;
  }

  return Response.json(json);
}

// =============================================================================
// 基础结构：返回普通 JSON 对象（用于调试或构造 Response）
export function buildJson(code: number, message: string, data?: any) {
  return {
    code,
    message,
    data: data ?? [],
  };
}

// === NextResponse 版本（推荐用于 App Router） ===
export function nextRespJson(code: number, message: string, data?: any): NextResponse {
  const json = buildJson(code, message, data);
  return NextResponse.json(json);
}

export function nextRespData(data: any): NextResponse {
  return nextRespJson(0, "ok", data || []);
}

export function nextRespOk(): NextResponse {
  return nextRespJson(0, "ok");
}

export function nextRespErr(message: string): NextResponse {
  return nextRespJson(-1, message);
}
