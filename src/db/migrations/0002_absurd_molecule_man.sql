ALTER TABLE "ai_tasks" RENAME TO "tasks";--> statement-breakpoint
ALTER TABLE "tasks" DROP CONSTRAINT "ai_tasks_task_id_unique";--> statement-breakpoint
DROP INDEX "idx_ai_tasks_user_uuid";--> statement-breakpoint
DROP INDEX "idx_ai_tasks_status";--> statement-breakpoint
DROP INDEX "idx_ai_tasks_created_at";--> statement-breakpoint
DROP INDEX "idx_ai_tasks_model_id";--> statement-breakpoint
CREATE INDEX "idx_tasks_user_uuid" ON "tasks" USING btree ("user_uuid");--> statement-breakpoint
CREATE INDEX "idx_tasks_status" ON "tasks" USING btree ("status");--> statement-breakpoint
CREATE INDEX "idx_tasks_created_at" ON "tasks" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "idx_tasks_model_id" ON "tasks" USING btree ("model_id");--> statement-breakpoint
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_task_id_unique" UNIQUE("task_id");