/**
 * Task Service for AI generation jobs
 * Handles complete task lifecycle including creation, status tracking, result processing, and file management
 * Integrates with AI providers and cloud storage for comprehensive content generation workflow
 */
import { v4 as uuidv4 } from 'uuid';
import * as tasksModel from '@/models/tasks';
import { TaskStatus, TaskStatusType, TaskType, Task, TaskResults } from '@/types/task';
import { createFilesFromUrls, getTaskFiles } from '@/services/file';
import { FileCategory, FileRecord } from '@/types/file';
import logger from '@/lib/logger';

/**
 * Parameters required for creating a new AI generation task
 * Contains all necessary information to initiate AI processing
 */
export interface CreateTaskParams {
  /** UUID of the user requesting the task */
  userUuid: string;
  /** Type of AI generation task (image, text, audio, video) */
  taskType: TaskType;
  /** Task-specific parameters (prompts, settings, etc.) */
  params: any;
  /** Identifier of the AI model to use */
  modelId: string;
  /** Name of the AI provider (OpenAI, Stability, etc.) */
  provider: string;
}

/**
 * Extended task result interface that includes associated files
 * Provides complete view of task outcome including generated content
 */
export interface TaskResultWithFiles {
  /** The task record with metadata and status */
  task: Task;
  /** Array of files generated by the task */
  files: FileRecord[];
}

/**
 * Create a new AI generation task in the system
 * Initializes task record in database with pending status for provider submission
 * @param params Task creation parameters including user, type, and configuration
 * @returns Promise resolving to unique task ID for tracking
 * @throws Error if task creation fails due to validation or database issues
 */
export async function createTask(params: CreateTaskParams): Promise<string> {
  const taskId = uuidv4();
  
  await tasksModel.createTask({
    task_id: taskId,
    user_uuid: params.userUuid,
    task_type: params.taskType,
    status: TaskStatus.PENDING,
    params: params.params,
    model_id: params.modelId,
    provider: params.provider
  });
  
  logger.info("Successfully created new AI generation task", {
    taskId,
    userUuid: params.userUuid,
    taskType: params.taskType,
  }, 
  { filePath: "services/task.ts", functionName: 'createTask' });
  return taskId;
}

/**
 * Update task with provider's task ID after successful submission
 * Links internal task with provider's tracking system and marks as processing
 * Called after submitting task to AI provider (OpenAI, Stability, etc.)
 * @param taskId Internal task identifier
 * @param providerTaskId Provider's task identifier for status polling
 * @throws Error if task update fails
 */
export async function setProviderTaskId(taskId: string, providerTaskId: string): Promise<void> {
  await tasksModel.updateProviderTaskId(taskId, providerTaskId);
  await updateTaskStatus(taskId, TaskStatus.PROCESSING);
}

/**
 * Update task status and progress throughout the generation lifecycle
 * Maintains accurate status for user interfaces and monitoring systems
 * @param taskId Unique task identifier to update
 * @param status New status from TaskStatus enum
 * @param progress Optional progress percentage (0-100) for status tracking
 * @throws Error if status update fails
 */
export async function updateTaskStatus(
  taskId: string, 
  status: TaskStatusType, 
  progress: number = 0
): Promise<void> {
  if (status === TaskStatus.COMPLETED) {
    await tasksModel.updateCompletedAt(taskId);
  }

  if (status === TaskStatus.FAILED) {
    await tasksModel.failTaskWithError(taskId, 'Task failed');
  }

  await tasksModel.updateTaskStatus(taskId, status, progress);
  
  logger.info('Successfully updated task status', { taskId, status, progress }, 
    { filePath: "services/task.ts", functionName: 'updateTaskStatus' });
}

/**
 * Process AI generation results and complete task with file management
 * Downloads generated files from provider URLs, uploads to our storage, and creates database records
 * This is the primary method for handling successful AI generation results
 * @param taskId The task ID to complete
 * @param userUuid The user UUID who owns the generated content
 * @param fileUrls Array of file URLs returned by the AI provider
 * @param modelId Model identifier for metadata tracking
 * @param metadata Additional task metadata (processing time, parameters, etc.)
 * @returns Promise resolving to complete task result with files
 * @throws Error if file processing or task completion fails
 */
export async function completeTaskWithResults(
  taskId: string,
  userUuid: string,
  fileUrls: string[],
  modelId: string,
  metadata?: Partial<TaskResults>
): Promise<TaskResultWithFiles> {
  try {
    // Upload all generated files using the file management service
    const uploadResults = await createFilesFromUrls(
      fileUrls,
      userUuid,
      FileCategory.TASK_OUTPUT,
      taskId,
      { 
        metadata: {
          model: modelId, 
          provider_info: metadata?.provider_response
        }
      }
    );
    
    // Prepare comprehensive task results metadata (no file URLs stored here)
    const taskResults: TaskResults = {
      generated_count: uploadResults.length,
      model_version: modelId,
      ...metadata
    };
    
    // Mark task as completed with results metadata
    await tasksModel.completeTaskWithResults(taskId, taskResults);
    
    // Retrieve updated task record
    const task = await tasksModel.getTask(taskId);
    if (!task) {
      throw new Error('Task not found after completion');
    }
    
    const files = await getTaskFiles(taskId);
    
    logger.info('Successfully completed task with file processing', 
      { taskId, filesCount: files.length }, 
      { filePath: "services/task.ts", functionName: 'completeTaskWithResults' });
    
    return { task, files };
  } catch (error) {
    logger.error('Failed to complete task with file processing', error, 
      { taskId, fileUrlsCount: fileUrls.length }, 
      { filePath: "services/task.ts", functionName: 'completeTaskWithResults' });
    throw error;
  }
}

/**
 * Mark task as failed with error message
 * Records error details for debugging and user notification
 * @param taskId The task ID to mark as failed
 * @param errorMessage Descriptive error message for troubleshooting
 * @throws Error if task failure update fails
 */
export async function setTaskError(taskId: string, errorMessage: string): Promise<void> {
  await tasksModel.failTaskWithError(taskId, errorMessage);
}

/**
 * Retrieve complete task details with associated generated files
 * Provides unified view of task status and all generated content
 * @param taskId The task ID to retrieve
 * @returns Promise resolving to task with files or null if not found
 */
export async function getTaskWithFiles(taskId: string): Promise<TaskResultWithFiles | null> {
  const task = await tasksModel.getTask(taskId);
  if (!task) {
    return null;
  }
  
  const files = await getTaskFiles(taskId);
  
  return { task, files };
}

/**
 * Get basic task details and status without file information
 * Lightweight method for status checking and basic task information
 * @param taskId The task ID to retrieve
 * @returns Promise resolving to task details or null if not found
 */
export async function getTask(taskId: string): Promise<Task | null> {
  return tasksModel.getTask(taskId);
}

/**
 * Find task by provider's task ID for webhook processing
 * Used when receiving status updates or results from AI providers
 * @param providerTaskId The provider's task identifier
 * @returns Promise resolving to task details or null if not found
 */
export async function getTaskByProviderTaskId(providerTaskId: string): Promise<Task | null> {
  return tasksModel.getTaskByProviderTaskId(providerTaskId);
}

/**
 * Get paginated list of tasks for a specific user
 * Supports user dashboards and task history interfaces
 * @param userUuid The user UUID to filter tasks for
 * @param limit Maximum number of tasks to return (pagination)
 * @param offset Number of tasks to skip (pagination)
 * @returns Promise resolving to array of user's tasks
 */
export async function getUserTasks(userUuid: string, limit?: number, offset?: number): Promise<Task[]> {
  return tasksModel.getUserTasks(userUuid, limit, offset);
}
