/**
 * Business Modules Export
 * 
 * Central export point for all business logic modules.
 */

// Language switching functionality - Note: these require a ContentService instance
export {
  getAvailableLanguageVersions,
  handleContentLanguageSwitch,
  generateHreflangAttributes,
  type LanguageSwitchParams,
  type LanguageSwitchResult
} from './language-switching'

// SEO metadata generation
export {
  generateSEOMetadata,
  generateListPageSEOMetadata,
  type SEOMetadata
} from './seo-metadata'

// Static generation support
export {
  generateStaticParams,
  getAllContent,
  getContentByLocale,
  getContentStatistics,
  validateContentForBuild
} from './static-generation'

// Content relations and recommendations
export {
  getRelatedContent,
  getContentByTags,
  getFeaturedContent,
  getRecentContent,
  getContentByAuthor,
  getContentRecommendations
} from './content-relations'