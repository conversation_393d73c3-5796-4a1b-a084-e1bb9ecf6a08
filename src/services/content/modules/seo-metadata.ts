/**
 * SEO Metadata Module
 * 
 * Provides comprehensive SEO metadata generation for content items,
 * including Open Graph and Twitter Card support.
 */

import type { 
  ContentItem,
  ContentType
} from '../types'

/**
 * SEO metadata interface
 */
export interface SEOMetadata {
  title: string
  description: string
  keywords: string[]
  canonicalUrl: string
  openGraph: {
    title: string
    description: string
    image?: string
    type: 'article' | 'website'
  }
  twitterCard: {
    card: 'summary' | 'summary_large_image'
    title: string
    description: string
    image?: string
  }
}

/**
 * Generate comprehensive SEO metadata for content
 * 
 * @param content - Content item to generate metadata for
 * @param options - Additional options for metadata generation
 * @returns Complete SEO metadata object
 */
export function generateSEOMetadata(
  content: ContentItem,
  options: {
    baseUrl?: string
    siteName?: string
    defaultImage?: string
  } = {}
): SEOMetadata {
  const { 
    baseUrl = '', 
    siteName = 'ShipAny',
    defaultImage = '/images/og-default.jpg'
  } = options

  // Generate base metadata
  const title = generateSEOTitle(content, siteName)
  const description = generateSEODescription(content)
  const keywords = generateSEOKeywords(content)
  const canonicalUrl = generateCanonicalUrl(content, baseUrl)
  
  // Determine the best image for social sharing
  const socialImage = content.coverImage || defaultImage

  return {
    title,
    description,
    keywords,
    canonicalUrl,
    openGraph: {
      title: generateOpenGraphTitle(content, siteName),
      description: generateOpenGraphDescription(content),
      image: socialImage,
      type: getOpenGraphType(content.type)
    },
    twitterCard: {
      card: socialImage ? 'summary_large_image' : 'summary',
      title: generateTwitterTitle(content),
      description: generateTwitterDescription(content),
      image: socialImage
    }
  }
}

/**
 * Generate SEO metadata for content list pages
 * 
 * @param contentType - Type of content list
 * @param locale - Page locale
 * @param options - Additional options
 * @returns SEO metadata for list page
 */
export function generateListPageSEOMetadata(
  contentType: ContentType,
  locale: string,
  options: {
    baseUrl?: string
    siteName?: string
    category?: string
    page?: number
  } = {}
): SEOMetadata {
  const { baseUrl = '', siteName = 'ShipAny', category, page = 1 } = options

  const typeLabels = {
    blog: 'Blog',
    product: 'Products',
    'case-study': 'Case Studies'
  }

  const typeLabel = typeLabels[contentType]
  const categoryText = category ? ` - ${category}` : ''
  const pageText = page > 1 ? ` - Page ${page}` : ''
  
  const title = `${typeLabel}${categoryText}${pageText} | ${siteName}`
  const description = generateListPageDescription(contentType, category)
  
  // Generate canonical URL for list page
  const prefix = locale === 'en' ? '' : `/${locale}`
  const typeSegment = contentType === 'case-study' ? 'case-studies' : `${contentType}s`
  const canonicalUrl = `${baseUrl}${prefix}/${typeSegment}`

  return {
    title,
    description,
    keywords: generateListPageKeywords(contentType, category),
    canonicalUrl,
    openGraph: {
      title,
      description,
      type: 'website'
    },
    twitterCard: {
      card: 'summary',
      title,
      description
    }
  }
}

// Helper functions

function generateSEOTitle(content: ContentItem, siteName: string): string {
  // Keep title under 60 characters for optimal SEO
  const maxLength = 60 - siteName.length - 3 // Account for " | " separator
  const truncatedTitle = content.title.length > maxLength 
    ? `${content.title.substring(0, maxLength - 3)}...`
    : content.title

  return `${truncatedTitle} | ${siteName}`
}

function generateOpenGraphTitle(content: ContentItem, siteName: string): string {
  // Open Graph titles can be longer than SEO titles
  return `${content.title} | ${siteName}`
}

function generateTwitterTitle(content: ContentItem): string {
  // Twitter titles should be concise
  const maxLength = 70
  return content.title.length > maxLength
    ? `${content.title.substring(0, maxLength - 3)}...`
    : content.title
}

function generateSEODescription(content: ContentItem): string {
  let description = content.description || ''

  // Ensure description is within optimal length (150-160 characters)
  const maxLength = 160
  if (description.length > maxLength) {
    description = `${description.substring(0, maxLength - 3)}...`
  }

  return description
}

function generateOpenGraphDescription(content: ContentItem): string {
  // Open Graph descriptions can be longer
  return content.description || ''
}

function generateTwitterDescription(content: ContentItem): string {
  // Twitter descriptions should be concise
  const description = content.description || ''
  const maxLength = 200
  
  return description.length > maxLength
    ? `${description.substring(0, maxLength - 3)}...`
    : description
}

function generateSEOKeywords(content: ContentItem): string[] {
  const keywords: string[] = []

  // Add content tags
  if (content.tags) {
    keywords.push(...content.tags)
  }

  // Add content type specific keywords
  keywords.push(...getContentTypeKeywords(content))

  // Remove duplicates and limit to 10 keywords
  return [...new Set(keywords)].slice(0, 10)
}

function generateCanonicalUrl(content: ContentItem, baseUrl: string): string {
  const prefix = content.lang === 'en' ? '' : `/${content.lang}`
  const typeSegment = content.type === 'case-study' ? 'case-studies' : `${content.type}s`
  return `${baseUrl}${prefix}/${typeSegment}/${content.slug}`
}

function generateListPageDescription(contentType: ContentType, category?: string): string {
  const descriptions = {
    blog: 'Discover our latest blog posts, insights, and updates on technology, business, and innovation.',
    product: 'Explore our comprehensive product catalog with detailed specifications and features.',
    'case-study': 'Read our detailed case studies showcasing successful projects and client outcomes.'
  }

  let description = descriptions[contentType]
  
  if (category) {
    description = description.replace(/our latest|our comprehensive|our detailed/, `our ${category}`)
  }

  return description
}

function generateListPageKeywords(contentType: ContentType, category?: string): string[] {
  const baseKeywords = {
    blog: ['blog', 'articles', 'insights', 'news', 'updates'],
    product: ['products', 'catalog', 'solutions', 'services'],
    'case-study': ['case studies', 'success stories', 'projects', 'results']
  }

  const keywords = [...baseKeywords[contentType]]
  
  if (category) {
    keywords.push(category, `${category} ${contentType}`)
  }

  return keywords
}

function getContentTypeKeywords(content: ContentItem): string[] {
  const baseKeywords = ['shipany']
  
  switch (content.type) {
    case 'blog':
      return [...baseKeywords, 'blog', 'article', 'insights']
    case 'product':
      return [...baseKeywords, 'product', 'solution', 'service']
    case 'case-study':
      return [...baseKeywords, 'case study', 'success story', 'project']
    default:
      return baseKeywords
  }
}

function getOpenGraphType(contentType: ContentType): 'article' | 'website' {
  return contentType === 'blog' ? 'article' : 'website'
}