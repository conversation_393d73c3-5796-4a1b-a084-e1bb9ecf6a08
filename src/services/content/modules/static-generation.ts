/**
 * Static Generation Module
 * 
 * Provides specialized functions for Next.js static generation,
 * including support for generateStaticParams and build-time operations.
 */

import type { 
  ContentType, 
  ContentItem
} from '../types'
import type { ContentService } from '../core/content-service'

/**
 * Generate static params for App Router
 * 
 * @param service - Content service instance
 * @param type - Content type to generate params for
 * @returns Array of param objects for Next.js
 */
export async function generateStaticParams(
  service: ContentService,
  type: ContentType
): Promise<Array<{ locale: string; slug: string }>> {
  if (!service.isFeatureEnabled('staticGeneration')) {
    throw new Error('Static generation feature is not enabled')
  }

  try {
    const slugs = await service.getAllContentSlugs(type)
    
    // Convert to the format expected by Next.js generateStaticParams
    return slugs.map(({ locale, slug }) => ({
      locale,
      slug
    }))
  } catch (error) {
    console.error(`Error generating static params for ${type}:`, error)
    return []
  }
}

/**
 * Get all content for static generation
 * 
 * @param service - Content service instance
 * @param type - Content type to retrieve
 * @returns Array of all content items
 */
export async function getAllContent<T extends ContentItem>(
  service: ContentService,
  type: ContentType
): Promise<T[]> {
  if (!service.isFeatureEnabled('staticGeneration')) {
    throw new Error('Static generation feature is not enabled')
  }

  try {
    return await service.getContentForStaticGeneration<T>(type)
  } catch (error) {
    console.error(`Error retrieving all content for ${type}:`, error)
    return []
  }
}

/**
 * Get content by locale for static generation
 * 
 * @param service - Content service instance
 * @param type - Content type
 * @param locale - Target locale
 * @returns Array of content items in the specified locale
 */
export async function getContentByLocale<T extends ContentItem>(
  service: ContentService,
  type: ContentType,
  locale: string
): Promise<T[]> {
  if (!service.isFeatureEnabled('staticGeneration')) {
    throw new Error('Static generation feature is not enabled')
  }

  try {
    const allContent = await service.getContentForStaticGeneration<T>(type)
    return allContent.filter(item => item.lang === locale)
  } catch (error) {
    console.error(`Error retrieving content for ${type}/${locale}:`, error)
    return []
  }
}

/**
 * Get content statistics for build reporting
 * 
 * @param service - Content service instance
 * @returns Content statistics object
 */
export async function getContentStatistics(
  service: ContentService
): Promise<{
  total: number
  byType: Record<ContentType, number>
  byLocale: Record<string, number>
}> {
  try {
    const contentTypes = service.getSupportedContentTypes()
    const locales = service.getSupportedLocales()
    
    let total = 0
    const byType = {} as Record<ContentType, number>
    const byLocale = {} as Record<string, number>
    
    // Initialize counters
    contentTypes.forEach(type => { byType[type] = 0 })
    locales.forEach(locale => { byLocale[locale] = 0 })
    
    // Count content for each type
    for (const type of contentTypes) {
      const content = await getAllContent(service, type)
      byType[type] = content.length
      total += content.length
      
      // Count by locale
      content.forEach(item => {
        if (locales.includes(item.lang)) {
          byLocale[item.lang]++
        }
      })
    }
    
    return {
      total,
      byType,
      byLocale
    }
  } catch (error) {
    console.error('Error generating content statistics:', error)
    return {
      total: 0,
      byType: { blog: 0, product: 0, 'case-study': 0 },
      byLocale: {}
    }
  }
}

/**
 * Validate content for build
 * 
 * @param service - Content service instance
 * @returns Validation results
 */
export async function validateContentForBuild(
  service: ContentService
): Promise<{
  valid: boolean
  errors: Array<{ type: ContentType; slug: string; locale: string; error: string }>
  warnings: Array<{ type: ContentType; slug: string; locale: string; warning: string }>
}> {
  const errors: Array<{ type: ContentType; slug: string; locale: string; error: string }> = []
  const warnings: Array<{ type: ContentType; slug: string; locale: string; warning: string }> = []
  
  try {
    const contentTypes = service.getSupportedContentTypes()
    
    for (const type of contentTypes) {
      const content = await getAllContent(service, type)
      
      content.forEach(item => {
        // Check required fields
        if (!item.title) {
          errors.push({
            type,
            slug: item.slug,
            locale: item.lang,
            error: 'Missing title'
          })
        }
        
        if (!item.description) {
          warnings.push({
            type,
            slug: item.slug,
            locale: item.lang,
            warning: 'Missing description (recommended for SEO)'
          })
        }
        
        // Check slug format
        if (!/^[a-z0-9-]+$/.test(item.slug)) {
          errors.push({
            type,
            slug: item.slug,
            locale: item.lang,
            error: 'Invalid slug format'
          })
        }
      })
    }
  } catch (error) {
    console.error('Error validating content for build:', error)
    errors.push({
      type: 'blog',
      slug: 'unknown',
      locale: 'unknown',
      error: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`
    })
  }
  
  return {
    valid: errors.length === 0,
    errors,
    warnings
  }
}