/**
 * Content Relations Module
 * 
 * Provides functionality for finding and managing relationships between
 * content items, including related content suggestions.
 */

import type { 
  ContentItem,
  ContentType
} from '../types'
import type { ContentService } from '../core/content-service'

/**
 * Get related content items
 * 
 * @param service - Content service instance
 * @param type - Content type
 * @param currentSlug - Current content slug
 * @param locale - Language locale
 * @param limit - Maximum number of related items
 * @returns Array of related content items
 */
export async function getRelatedContent<T extends ContentItem>(
  service: ContentService,
  type: ContentType,
  currentSlug: string,
  locale: string,
  limit: number = 3
): Promise<T[]> {
  if (!service.isFeatureEnabled('relatedContent')) {
    return []
  }

  return service.getRelatedContent<T>(type, currentSlug, locale, limit)
}

/**
 * Get content by tags
 * 
 * @param service - Content service instance
 * @param type - Content type
 * @param tags - Tags to filter by
 * @param locale - Language locale
 * @param limit - Maximum number of items
 * @returns Array of content items with matching tags
 */
export async function getContentByTags<T extends ContentItem>(
  service: ContentService,
  type: ContentType,
  tags: string[],
  locale: string,
  limit?: number
): Promise<T[]> {
  const content = await service.getContentList<T>(type, locale, {
    tags,
    limit
  })
  
  return content
}

/**
 * Get featured content
 * 
 * @param service - Content service instance
 * @param type - Content type
 * @param locale - Language locale
 * @param limit - Maximum number of items
 * @returns Array of featured content items
 */
export async function getFeaturedContent<T extends ContentItem>(
  service: ContentService,
  type: ContentType,
  locale: string,
  limit: number = 5
): Promise<T[]> {
  return service.getContentList<T>(type, locale, {
    featured: true,
    sortBy: 'publishedAt',
    order: 'desc',
    limit
  })
}

/**
 * Get recent content
 * 
 * @param service - Content service instance
 * @param type - Content type
 * @param locale - Language locale
 * @param limit - Maximum number of items
 * @returns Array of recent content items
 */
export async function getRecentContent<T extends ContentItem>(
  service: ContentService,
  type: ContentType,
  locale: string,
  limit: number = 10
): Promise<T[]> {
  return service.getContentList<T>(type, locale, {
    sortBy: 'publishedAt',
    order: 'desc',
    limit
  })
}

/**
 * Get content by author
 * 
 * @param service - Content service instance
 * @param type - Content type
 * @param author - Author name
 * @param locale - Language locale
 * @param limit - Maximum number of items
 * @returns Array of content items by the specified author
 */
export async function getContentByAuthor<T extends ContentItem>(
  service: ContentService,
  type: ContentType,
  author: string,
  locale: string,
  limit?: number
): Promise<T[]> {
  return service.getContentList<T>(type, locale, {
    author,
    sortBy: 'publishedAt',
    order: 'desc',
    limit
  })
}

/**
 * Get content recommendations
 * 
 * @param service - Content service instance
 * @param currentContent - Current content item
 * @param limit - Maximum number of recommendations
 * @returns Array of recommended content items
 */
export async function getContentRecommendations<T extends ContentItem>(
  service: ContentService,
  currentContent: ContentItem,
  limit: number = 5
): Promise<T[]> {
  const recommendations: T[] = []
  
  // First, try to get related content
  const related = await getRelatedContent<T>(
    service,
    currentContent.type,
    currentContent.slug,
    currentContent.lang,
    limit
  )
  
  recommendations.push(...related)
  
  // If not enough related content, add featured content
  if (recommendations.length < limit) {
    const featured = await getFeaturedContent<T>(
      service,
      currentContent.type,
      currentContent.lang,
      limit - recommendations.length
    )
    
    // Filter out the current content and already recommended items
    const filteredFeatured = featured.filter(item => 
      item.slug !== currentContent.slug &&
      !recommendations.some(rec => rec.slug === item.slug)
    )
    
    recommendations.push(...filteredFeatured)
  }
  
  // If still not enough, add recent content
  if (recommendations.length < limit) {
    const recent = await getRecentContent<T>(
      service,
      currentContent.type,
      currentContent.lang,
      limit - recommendations.length
    )
    
    // Filter out the current content and already recommended items
    const filteredRecent = recent.filter(item => 
      item.slug !== currentContent.slug &&
      !recommendations.some(rec => rec.slug === item.slug)
    )
    
    recommendations.push(...filteredRecent)
  }
  
  return recommendations.slice(0, limit)
}