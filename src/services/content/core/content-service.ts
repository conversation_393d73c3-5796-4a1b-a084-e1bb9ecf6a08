/**
 * Content Service Implementation
 * 
 * Main service class that provides a unified interface for content management
 * operations. This service delegates to the configured content provider while
 * adding business logic, caching, and validation layers.
 */

import type {
  ContentProvider,
  ContentItem,
  ContentType,
  QueryOptions,
  ContentMetadata,
  LanguageVersion,
  ContentServiceConfig
} from '../types'

/**
 * Content Service Class
 * 
 * Provides high-level content management operations with additional
 * business logic on top of the raw provider functionality.
 */
export class ContentService {
  private provider: ContentProvider
  private config: ContentServiceConfig

  constructor(provider: ContentProvider, config: ContentServiceConfig) {
    this.provider = provider
    this.config = config
  }

  /**
   * Get the current provider name
   */
  getProviderName(): string {
    return this.provider.name
  }

  /**
   * Get the current provider version
   */
  getProviderVersion(): string {
    return this.provider.version
  }

  /**
   * Get a single content item
   */
  async getContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null> {
    // Validate locale
    if (!this.isValidLocale(locale)) {
      console.warn(`Invalid locale: ${locale}`)
      return null
    }

    // Validate content type
    if (!this.isValidContentType(type)) {
      console.warn(`Invalid content type: ${type}`)
      return null
    }

    return this.provider.getContent<T>(type, slug, locale)
  }

  /**
   * Get a list of content items
   */
  async getContentList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    options?: QueryOptions
  ): Promise<T[]> {
    // Validate locale
    if (!this.isValidLocale(locale)) {
      console.warn(`Invalid locale: ${locale}`)
      return []
    }

    // Validate content type
    if (!this.isValidContentType(type)) {
      console.warn(`Invalid content type: ${type}`)
      return []
    }

    return this.provider.getContentList<T>(type, locale, options)
  }

  /**
   * Get all content for static generation
   */
  async getContentForStaticGeneration<T extends ContentItem>(
    type: ContentType
  ): Promise<T[]> {
    if (!this.config.features.staticGeneration) {
      throw new Error('Static generation feature is not enabled')
    }

    if (!this.isValidContentType(type)) {
      console.warn(`Invalid content type: ${type}`)
      return []
    }

    return this.provider.getContentForStaticGeneration<T>(type)
  }

  /**
   * Get all content slugs for static path generation
   */
  async getAllContentSlugs(
    type: ContentType
  ): Promise<Array<{ locale: string; slug: string }>> {
    if (!this.config.features.staticGeneration) {
      throw new Error('Static generation feature is not enabled')
    }

    if (!this.isValidContentType(type)) {
      console.warn(`Invalid content type: ${type}`)
      return []
    }

    return this.provider.getAllContentSlugs(type)
  }

  /**
   * Check if content exists
   */
  async contentExists(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<boolean> {
    if (!this.isValidLocale(locale) || !this.isValidContentType(type)) {
      return false
    }

    return this.provider.contentExists(type, slug, locale)
  }

  /**
   * Get content title
   */
  async getContentTitle(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<string | null> {
    if (!this.isValidLocale(locale) || !this.isValidContentType(type)) {
      return null
    }

    return this.provider.getContentTitle(type, slug, locale)
  }

  /**
   * Get content metadata
   */
  async getContentMetadata(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<ContentMetadata | null> {
    if (!this.isValidLocale(locale) || !this.isValidContentType(type)) {
      return null
    }

    return this.provider.getContentMetadata(type, slug, locale)
  }

  /**
   * Get available language versions
   */
  async getAvailableLanguages(
    type: ContentType,
    slug: string
  ): Promise<LanguageVersion[]> {
    if (!this.config.features.languageSwitching) {
      return []
    }

    if (!this.isValidContentType(type)) {
      return []
    }

    const versions = await this.provider.getAvailableLanguages(type, slug)
    
    // Filter to only supported locales
    return versions.filter(v => this.isValidLocale(v.lang))
  }

  /**
   * Get related content
   */
  async getRelatedContent<T extends ContentItem>(
    type: ContentType,
    currentSlug: string,
    locale: string,
    limit?: number
  ): Promise<T[]> {
    if (!this.config.features.relatedContent) {
      return []
    }

    if (!this.isValidLocale(locale) || !this.isValidContentType(type)) {
      return []
    }

    if (this.provider.getRelatedContent) {
      return this.provider.getRelatedContent<T>(type, currentSlug, locale, limit)
    }

    // Fallback implementation if provider doesn't support related content
    const allContent = await this.getContentList<T>(type, locale)
    return allContent
      .filter(item => item.slug !== currentSlug)
      .slice(0, limit || 3)
  }

  /**
   * Validate if a locale is supported
   */
  private isValidLocale(locale: string): boolean {
    return this.config.supportedLocales.includes(locale)
  }

  /**
   * Validate if a content type is supported
   */
  private isValidContentType(type: ContentType): boolean {
    return this.config.contentTypes.includes(type)
  }

  /**
   * Get the default locale
   */
  getDefaultLocale(): string {
    return this.config.defaultLocale
  }

  /**
   * Get all supported locales
   */
  getSupportedLocales(): string[] {
    return this.config.supportedLocales
  }

  /**
   * Get all supported content types
   */
  getSupportedContentTypes(): ContentType[] {
    return this.config.contentTypes
  }

  /**
   * Check if a feature is enabled
   */
  isFeatureEnabled(feature: keyof ContentServiceConfig['features']): boolean {
    return this.config.features[feature]
  }
}