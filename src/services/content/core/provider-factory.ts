/**
 * Provider Factory
 * 
 * Factory class responsible for creating content provider instances
 * based on configuration. This has been updated to use build-time
 * provider selection for optimal bundle size and performance.
 */

import type { ContentProvider, ProviderType, ProviderConfig } from '../types'
import { getContentProvider, getProviderName, isProvider } from './provider-selector'

/**
 * Provider Factory Class
 * 
 * Creates and configures content provider instances based on the
 * build-time provider selection. The actual provider is determined
 * by the CONTENT_PROVIDER environment variable.
 */
export class ProviderFactory {
  private static cachedProvider: ContentProvider | null = null

  /**
   * Create a content provider instance
   * 
   * @param type - The provider type to create (optional, for backwards compatibility)
   * @param config - Provider-specific configuration
   * @returns Configured content provider instance
   * @throws Error if provider type is not supported
   */
  static async create(type?: ProviderType, config?: ProviderConfig): Promise<ContentProvider> {
    // If type is provided and doesn't match build-time selection, warn
    if (type && type !== getProviderName()) {
      console.warn(
        `[ProviderFactory] Requested provider '${type}' does not match ` +
        `build-time selection '${getProviderName()}'. Using '${getProviderName()}'.`
      )
    }

    // Return cached provider if available
    if (this.cachedProvider) {
      return this.cachedProvider
    }

    // Create provider using build-time selector
    this.cachedProvider = await getContentProvider(config)
    return this.cachedProvider
  }

  /**
   * Clear cached provider
   */
  static clearCache(): void {
    this.cachedProvider = null
  }

  /**
   * Get the active provider name
   * 
   * @returns The provider name determined at build time
   */
  static getActiveProviderName(): string {
    return getProviderName()
  }

  /**
   * Check if a specific provider is active
   * 
   * @param providerName - The provider name to check
   * @returns True if the provider is active
   */
  static isProviderActive(providerName: string): boolean {
    return isProvider(providerName)
  }

  /**
   * Check if a provider type is supported
   * 
   * @param type - The provider type to check
   * @returns True if the provider is supported
   */
  static isSupported(type: string): type is ProviderType {
    // Only contentlayer2 and next-mdx-remote are currently implemented
    return ['contentlayer2', 'next-mdx-remote'].includes(type)
  }

  /**
   * Get list of available provider types
   * 
   * @returns Array of supported provider types
   */
  static getAvailableProviders(): ProviderType[] {
    // Only return implemented providers
    return ['contentlayer2', 'next-mdx-remote']
  }

  /**
   * Get provider implementation status
   * 
   * @returns Object mapping provider types to their implementation status
   */
  static getProviderStatus(): Record<string, { implemented: boolean; active: boolean; description: string }> {
    const activeProvider = getProviderName()
    
    return {
      contentlayer2: {
        implemented: true,
        active: activeProvider === 'contentlayer2',
        description: 'File-based CMS with TypeScript support (Best for Vercel)'
      },
      'next-mdx-remote': {
        implemented: true,
        active: activeProvider === 'next-mdx-remote',
        description: 'MDX with pre-compilation for edge environments (Best for Cloudflare Workers)'
      },
      mdx: {
        implemented: false,
        active: false,
        description: 'Next.js built-in MDX support'
      },
      strapi: {
        implemented: false,
        active: false,
        description: 'Headless CMS with REST/GraphQL API'
      },
      sanity: {
        implemented: false,
        active: false,
        description: 'Real-time headless CMS'
      }
    }
  }
}