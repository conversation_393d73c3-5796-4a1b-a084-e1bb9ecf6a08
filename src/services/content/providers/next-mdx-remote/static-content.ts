/**
 * Static Content Index for Cloudflare Workers
 *
 * This file is auto-generated by build-mdx-content.ts
 * DO NOT EDIT MANUALLY
 *
 * Last generated: 2025-07-24T06:52:39.873Z
 */

import type { ContentItem } from '../../types'

// Lazy-loaded content chunks
let contentCache: Record<string, ContentItem> | null = null

/**
 * Get all static content with lazy loading and caching
 *
 * @returns Complete content bundle
 */
export async function getStaticContent(): Promise<Record<string, ContentItem>> {
  // Return cached content if available
  if (contentCache) {
    return contentCache
  }

  console.log('[StaticContent] Loading content chunks...')

  try {
    // Dynamically import all content chunks
    const [blogs, products, caseStudies, docs] = await Promise.all([
      import('./static-content-blogs').catch(() => ({ default: {} })),
      import('./static-content-products').catch(() => ({ default: {} })),
      import('./static-content-case-studies').catch(() => ({ default: {} })),
      import('./static-content-docs').catch(() => ({ default: {} }))
    ])

    // Combine all chunks
    contentCache = {
      ...blogs.default,
      ...products.default,
      ...caseStudies.default,
      ...docs.default
    }

    console.log(`[StaticContent] Loaded ${Object.keys(contentCache).length} content items`)
    return contentCache
  } catch (error) {
    console.error('[StaticContent] Failed to load content chunks:', error)
    return {}
  }
}

/**
 * Clear content cache (useful for development)
 */
export function clearContentCache(): void {
  contentCache = null
}

/**
 * Get content synchronously (requires content to be pre-loaded)
 *
 * @returns Cached content or empty object
 */
export function getStaticContentSync(): Record<string, ContentItem> {
  return contentCache || {}
}
