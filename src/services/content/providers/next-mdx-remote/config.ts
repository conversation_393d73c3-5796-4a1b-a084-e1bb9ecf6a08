/**
 * Default configuration for Next.js MDX Remote Provider
 * 
 * This module provides default settings for the MDX compilation
 * and content management process.
 */

import type { NextMDXRemoteConfig } from './types'

/**
 * Default configuration values
 * 
 * These can be overridden when instantiating the provider
 * or through environment variables.
 */
export const DEFAULT_CONFIG: NextMDXRemoteConfig = {
  contentDir: './content',
  compiledDir: './.mdx-compiled'
}

/**
 * Get the path to the compiled content bundle
 * 
 * @param config - Provider configuration
 * @param minified - Whether to use the minified version
 * @returns Path to the content bundle file
 */
export function getBundlePath(config: NextMDXRemoteConfig, minified = false): string {
  const filename = minified ? 'content-bundle.min.json' : 'content-bundle.json'
  return `${config.compiledDir}/${filename}`
}