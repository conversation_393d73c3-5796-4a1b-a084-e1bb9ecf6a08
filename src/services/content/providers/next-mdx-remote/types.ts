/**
 * Type definitions for Next.js MDX Remote Provider
 * 
 * This module defines the configuration and data structures
 * specific to the next-mdx-remote content provider implementation.
 */

/**
 * Configuration options for NextMDXRemoteProvider
 */
export interface NextMDXRemoteConfig {
  /**
   * Directory containing MDX source files
   * @default './content'
   */
  contentDir?: string
  
  /**
   * Directory for compiled MDX output
   * @default './.mdx-compiled'
   */
  compiledDir?: string
}

/**
 * Structure of compiled MDX content
 * 
 * This represents the data structure after MDX compilation,
 * stored in the compiled bundle files.
 */
export interface CompiledMDXContent {
  /** Content identifier */
  slug: string
  
  /** Content type (blog, product, case-study) */
  type: string
  
  /** Language code (en, zh) */
  lang: string
  
  /** Content title */
  title: string
  
  /** Content description */
  description?: string
  
  /** Compiled content body */
  body: {
    /** Compiled HTML output */
    html: string
    /** Original MDX source */
    raw: string
  }
  
  /** URL path for this content */
  url: string
  
  /** Creation date */
  createdAt: string
  
  /** Publication date */
  publishedAt?: string
  
  /** Whether content is featured */
  featured: boolean
  
  /** Content tags */
  tags: string[]
  
  /** Author name */
  author?: string
  
  /** Cover image URL */
  coverImage?: string
  
  /** Additional frontmatter fields */
  [key: string]: unknown
}

/**
 * Compiled content bundle structure
 */
export interface ContentBundle {
  [key: string]: CompiledMDXContent
}