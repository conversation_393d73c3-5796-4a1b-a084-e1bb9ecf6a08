/**
 * Content Validation Utilities
 * 
 * Provides validation functions for content data integrity and format checking.
 */

import type { ContentItem, ContentType } from '../types'

/**
 * Validation result interface
 */
export interface ValidationResult {
  valid: boolean
  errors: string[]
  warnings: string[]
}

/**
 * Validate content item
 * 
 * @param content - Content item to validate
 * @returns Validation result
 */
export function validateContent(content: Partial<ContentItem>): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  // Required fields
  if (!content.slug) {
    errors.push('Missing required field: slug')
  } else if (!isValidSlug(content.slug)) {
    errors.push('Invalid slug format')
  }

  if (!content.title) {
    errors.push('Missing required field: title')
  }

  if (!content.lang) {
    errors.push('Missing required field: lang')
  }

  if (!content.url) {
    errors.push('Missing required field: url')
  }

  if (!content.body) {
    errors.push('Missing required field: body')
  }

  if (!content.createdAt) {
    errors.push('Missing required field: createdAt')
  }

  // Recommended fields
  if (!content.description) {
    warnings.push('Missing recommended field: description')
  }

  if (!content.tags || content.tags.length === 0) {
    warnings.push('No tags specified')
  }

  // Date format validation
  if (content.publishedAt && !isValidDate(content.publishedAt)) {
    errors.push('Invalid publishedAt date format')
  }

  if (content.createdAt && !isValidDate(content.createdAt)) {
    errors.push('Invalid createdAt date format')
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Validate content type
 * 
 * @param type - Content type to validate
 * @returns True if valid
 */
export function isValidContentType(type: string): type is ContentType {
  return ['blog', 'product', 'case-study'].includes(type)
}

/**
 * Validate locale
 * 
 * @param locale - Locale to validate
 * @returns True if valid
 */
export function isValidLocale(locale: string): boolean {
  return ['en', 'zh'].includes(locale)
}

/**
 * Validate slug format
 * 
 * @param slug - Slug to validate
 * @returns True if valid
 */
export function isValidSlug(slug: string): boolean {
  const slugPattern = /^[a-z0-9]+(?:-[a-z0-9]+)*$/
  return slugPattern.test(slug) && slug.length >= 1 && slug.length <= 100
}

/**
 * Validate date format
 * 
 * @param date - Date string to validate
 * @returns True if valid ISO date
 */
export function isValidDate(date: string): boolean {
  const parsed = new Date(date)
  return !isNaN(parsed.getTime()) && parsed.toISOString() === date
}

/**
 * Validate URL format
 * 
 * @param url - URL to validate
 * @returns True if valid
 */
export function isValidUrl(url: string): boolean {
  try {
    // Check if it's a valid relative URL
    if (url.startsWith('/')) {
      return true
    }
    // Check if it's a valid absolute URL
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * Validate image URL
 * 
 * @param url - Image URL to validate
 * @returns True if valid image URL
 */
export function isValidImageUrl(url: string): boolean {
  if (!url || !isValidUrl(url)) {
    return false
  }
  
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg']
  return imageExtensions.some(ext => url.toLowerCase().includes(ext))
}

/**
 * Sanitize content for display
 * 
 * @param content - Content to sanitize
 * @returns Sanitized content
 */
export function sanitizeContent(content: string): string {
  // Basic sanitization - in production, use a proper sanitization library
  return content
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
    .trim()
}

/**
 * Validate content length
 * 
 * @param content - Content to check
 * @param maxLength - Maximum allowed length
 * @returns True if within limits
 */
export function isWithinLength(content: string, maxLength: number): boolean {
  return content.length <= maxLength
}

/**
 * Validate tags
 * 
 * @param tags - Tags to validate
 * @returns Validation result
 */
export function validateTags(tags: string[]): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  if (tags.length > 10) {
    warnings.push('Too many tags (recommended maximum: 10)')
  }

  tags.forEach((tag, index) => {
    if (!tag || tag.trim().length === 0) {
      errors.push(`Empty tag at position ${index + 1}`)
    }
    if (tag.length > 50) {
      errors.push(`Tag too long at position ${index + 1} (maximum: 50 characters)`)
    }
  })

  return {
    valid: errors.length === 0,
    errors,
    warnings
  }
}