/**
 * URL Generation Utilities
 * 
 * Provides utilities for generating URLs for content pages across
 * different languages and content types.
 */

import type { ContentType } from '../types'

/**
 * URL generation configuration
 */
const URL_CONFIG = {
  /** Content type to URL path mapping */
  contentPaths: {
    blog: 'blogs',
    product: 'products',
    'case-study': 'case-studies'
  } as const,
  
  /** Default locale (no prefix in URL) */
  defaultLocale: 'en' as const,
  
  /** Whether to include trailing slashes */
  trailingSlash: false
} as const

/**
 * Generate URL for content
 * 
 * @param contentType - Type of content
 * @param slug - Content identifier (empty for list pages)
 * @param locale - Target locale code
 * @param baseUrl - Optional base URL for absolute URLs
 * @returns Properly formatted URL string
 */
export function generateContentUrl(
  contentType: ContentType,
  slug: string,
  locale: string,
  baseUrl: string = ''
): string {
  // Get the path segment for this content type
  const contentPath = URL_CONFIG.contentPaths[contentType]
  
  // Build locale prefix (empty for default locale)
  const localePrefix = locale === URL_CONFIG.defaultLocale ? '' : `/${locale}`
  
  // Build the path
  let path = `${localePrefix}/${contentPath}`
  
  // Add slug if provided
  if (slug && slug.trim()) {
    path += `/${slug}`
  }
  
  // Add trailing slash if configured
  if (URL_CONFIG.trailingSlash && !path.endsWith('/')) {
    path += '/'
  }
  
  // Return with base URL if provided
  return baseUrl ? `${baseUrl}${path}` : path
}

/**
 * Generate content list page URL
 * 
 * @param contentType - Type of content
 * @param locale - Target locale
 * @param baseUrl - Optional base URL
 * @returns Content list page URL
 */
export function generateContentListUrl(
  contentType: ContentType,
  locale: string,
  baseUrl: string = ''
): string {
  return generateContentUrl(contentType, '', locale, baseUrl)
}

/**
 * Parse content URL to extract components
 * 
 * @param url - URL to parse
 * @returns Parsed URL components
 */
export function parseContentUrl(url: string): {
  contentType: ContentType | null
  slug: string | null
  locale: string
  isListPage: boolean
} {
  // Remove query parameters
  const [pathname] = url.split('?')
  
  // Split path into segments
  const segments = pathname.split('/').filter(Boolean)
  
  // Check if first segment is a locale
  let locale: string = URL_CONFIG.defaultLocale
  let pathStart = 0
  
  if (segments[0] && ['en', 'zh'].includes(segments[0])) {
    locale = segments[0]
    pathStart = 1
  }
  
  // Check if next segment is a content type
  const contentSegment = segments[pathStart]
  let contentType: ContentType | null = null
  
  for (const [type, path] of Object.entries(URL_CONFIG.contentPaths)) {
    if (contentSegment === path) {
      contentType = type as ContentType
      break
    }
  }
  
  // Extract slug if present
  const slug = segments[pathStart + 1] || null
  const isListPage = contentType !== null && slug === null
  
  return {
    contentType,
    slug,
    locale,
    isListPage
  }
}

/**
 * Get content base path for a content type
 * 
 * @param contentType - Content type
 * @returns Base path segment
 */
export function getContentBasePath(contentType: ContentType): string {
  return URL_CONFIG.contentPaths[contentType] || ''
}