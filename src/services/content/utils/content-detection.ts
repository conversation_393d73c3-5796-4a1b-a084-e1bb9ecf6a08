/**
 * Content Detection Utilities
 * 
 * Provides utilities for URL analysis and content type detection.
 */

import type { ContentType } from '../types'

/**
 * Content page information
 */
export interface ContentPageInfo {
  contentType: ContentType
  slug: string | null
  isContentPage: boolean
  pathSegments?: string[]
}

/**
 * Content URL patterns configuration
 */
const CONTENT_PATTERNS = {
  blog: '/blogs/',
  product: '/products/',
  'case-study': '/case-studies/'
} as const

/**
 * Detect content page information from URL pathname
 * 
 * @param pathname - Current pathname from router or URL
 * @param locale - Current locale identifier
 * @returns Content page information object
 */
export function detectContentPage(pathname: string, locale: string): ContentPageInfo {
  // Normalize pathname by removing locale prefix
  const cleanPath = normalizePathname(pathname, locale)
  
  // Check each content pattern
  for (const [contentType, pattern] of Object.entries(CONTENT_PATTERNS)) {
    if (cleanPath.startsWith(pattern)) {
      const slug = extractSlugFromPath(cleanPath, pattern)
      
      return {
        contentType: contentType as ContentType,
        slug,
        isContentPage: slug.length > 0,
        pathSegments: cleanPath.split('/').filter(Boolean)
      }
    }
  }

  // Default case for non-content pages
  return {
    contentType: 'blog', // default fallback
    slug: '',
    isContentPage: false,
    pathSegments: cleanPath.split('/').filter(Boolean)
  }
}

/**
 * Check if a pathname represents a content list page
 * 
 * @param pathname - URL pathname to check
 * @param locale - Current locale
 * @returns True if this is a content list page
 */
export function isContentListPage(pathname: string, locale: string): boolean {
  const cleanPath = normalizePathname(pathname, locale)
  
  return Object.values(CONTENT_PATTERNS).some(pattern => 
    cleanPath === pattern.slice(0, -1) || cleanPath === pattern
  )
}

/**
 * Check if a pathname represents a content detail page
 * 
 * @param pathname - URL pathname to check
 * @param locale - Current locale
 * @returns True if this is a content detail page
 */
export function isContentDetailPage(pathname: string, locale: string): boolean {
  const pageInfo = detectContentPage(pathname, locale)
  return pageInfo.isContentPage && pageInfo.slug !== null && pageInfo.slug.length > 0
}

/**
 * Extract content type from pathname
 * 
 * @param pathname - URL pathname
 * @param locale - Current locale
 * @returns Content type or null if not a content page
 */
export function extractContentType(pathname: string, locale: string): ContentType | null {
  const pageInfo = detectContentPage(pathname, locale)
  return pageInfo.isContentPage ? pageInfo.contentType : null
}

/**
 * Validate content slug format
 * 
 * @param slug - Content slug to validate
 * @returns True if the slug is valid
 */
export function validateContentSlug(slug: string): boolean {
  if (!slug || typeof slug !== 'string') {
    return false
  }
  
  // Slug should be lowercase, contain only letters, numbers, and hyphens
  const slugPattern = /^[a-z0-9]+(?:-[a-z0-9]+)*$/
  return slugPattern.test(slug) && slug.length >= 1 && slug.length <= 100
}

// Private helper functions

function normalizePathname(pathname: string, locale: string): string {
  // Remove locale prefix if present (except for default locale 'en')
  if (locale !== 'en' && pathname.startsWith(`/${locale}`)) {
    return pathname.replace(`/${locale}`, '') || '/'
  }
  
  return pathname
}

function extractSlugFromPath(path: string, pattern: string): string {
  const slug = path.replace(pattern, '').split('/')[0]
  return slug || ''
}