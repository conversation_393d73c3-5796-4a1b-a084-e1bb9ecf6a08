/**
 * Type Exports
 * 
 * Central export point for all content service type definitions.
 */

// Content types
export type {
  ContentType,
  MDXContent,
  BaseContentItem,
  BlogContent,
  ProductContent,
  CaseStudyContent,
  ContentItem,
  ContentMetadata,
  LanguageVersion,
  ContentTypeMap,
  ContentByType
} from './content'

// Provider types
export type {
  QueryOptions,
  ContentProvider
} from './provider'

// Configuration types
export type {
  ProviderType,
  ContentFeatures,
  ProviderConfig,
  ContentServiceConfig
} from './config'

// Language types
export type {
  SupportedLocale,
  LanguageSwitchParams,
  LanguageSwitchResult,
  LanguageDetectionResult,
  I18nConfig,
  ContentLocalizationMetadata
} from './language'

export { defaultConfig } from './config'