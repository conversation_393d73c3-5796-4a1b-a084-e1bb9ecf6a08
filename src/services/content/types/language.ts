/**
 * Language and Internationalization Type Definitions
 * 
 * This module defines types related to language switching, internationalization,
 * and multi-language content management. These types ensure consistent handling
 * of language-related operations across the content service layer.
 */

/**
 * Supported locales in the system
 */
export type SupportedLocale = 'en' | 'zh'

/**
 * Language version interface
 * 
 * Represents different language versions of the same content item.
 * Used for language switching and alternate language detection.
 */
export interface LanguageVersion {
  /** Language code */
  lang: SupportedLocale
  /** Content title in this language */
  title: string
  /** URL for this language version */
  url: string
  /** Whether content is available in this language */
  available: boolean
  /** Whether this is the current language */
  current?: boolean
}

/**
 * Language switch parameters interface
 * 
 * Defines the parameters needed to perform a language switch operation,
 * including current context and target language information.
 */
export interface LanguageSwitchParams {
  /** Current page pathname */
  currentPath: string
  /** Current locale */
  currentLocale: SupportedLocale
  /** Target locale to switch to */
  targetLocale: SupportedLocale
  /** Base URL for the application */
  baseUrl?: string
  /** Additional context for the switch */
  context?: {
    /** Content type if on a content page */
    contentType?: string
    /** Content slug if on a content page */
    slug?: string
  }
}

/**
 * Language switch result interface
 * 
 * Represents the result of a language switching operation,
 * including the target URL and strategy used for the switch.
 */
export interface LanguageSwitchResult {
  /** Target URL for the language switch */
  url: string
  /** Strategy used for the language switch */
  strategy: 'direct' | 'fallback' | 'home'
  /** Whether the target content is available */
  available: boolean
  /** Additional information about the switch */
  metadata?: {
    /** Original content title */
    originalTitle?: string
    /** Target content title */
    targetTitle?: string
    /** Reason for fallback (if applicable) */
    fallbackReason?: string
  }
}

/**
 * Language detection result interface
 * 
 * Represents the result of language detection from various sources
 * such as URL, browser preferences, or user settings.
 */
export interface LanguageDetectionResult {
  /** Detected locale */
  locale: SupportedLocale
  /** Confidence level of the detection (0-1) */
  confidence: number
  /** Source of the detection */
  source: 'url' | 'browser' | 'cookie' | 'default'
  /** Whether the detected locale is supported */
  supported: boolean
}

/**
 * Internationalization configuration interface
 * 
 * Defines the configuration options for internationalization
 * features in the content service layer.
 */
export interface I18nConfig {
  /** Default locale */
  defaultLocale: SupportedLocale
  /** List of supported locales */
  supportedLocales: SupportedLocale[]
  /** Whether to show language switching UI */
  enableLanguageSwitching: boolean
  /** Fallback strategy when content is not available */
  fallbackStrategy: 'redirect' | 'show-default' | 'show-404'
  /** URL structure for different locales */
  urlStructure: {
    /** Whether to include locale in URL for default language */
    includeDefaultLocale: boolean
    /** URL prefix pattern */
    pattern: 'prefix' | 'domain' | 'subdomain'
  }
}

/**
 * Content localization metadata interface
 * 
 * Represents metadata about content localization status
 * and translation information.
 */
export interface ContentLocalizationMetadata {
  /** Original locale of the content */
  originalLocale: SupportedLocale
  /** Available translations */
  availableLocales: SupportedLocale[]
  /** Translation status for each locale */
  translationStatus: Record<SupportedLocale, {
    /** Whether translation exists */
    exists: boolean
    /** Translation quality/completeness (0-1) */
    quality?: number
    /** Last update timestamp */
    lastUpdated?: string
    /** Translator information */
    translator?: string
  }>
  /** Whether content needs translation updates */
  needsUpdate: boolean
}
