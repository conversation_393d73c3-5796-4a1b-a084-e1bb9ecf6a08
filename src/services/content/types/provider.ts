/**
 * Content Provider Interface
 * 
 * This module defines the contract that all CMS providers must implement
 * to ensure consistent functionality across different CMS backends.
 * The provider interface abstracts away the specific implementation
 * details of different CMS systems while providing a unified API.
 */

import type {
  ContentItem,
  ContentType,
  ContentMetadata,
  LanguageVersion
} from './content'

/**
 * Query Options Interface
 * 
 * Defines the available options for querying content from
 * CMS providers. These options provide flexible filtering,
 * sorting, and pagination capabilities that work across
 * different CMS backends.
 */
export interface QueryOptions {
  // Filtering options
  /** Filter by featured status */
  featured?: boolean
  /** Filter by tags (content must have all specified tags) */
  tags?: string[]
  /** Filter by author name or identifier */
  author?: string
  
  // Sorting options
  /** Field to sort by */
  sortBy?: 'title' | 'publishedAt' | 'createdAt' | 'featured'
  /** Sort order (ascending or descending) */
  order?: 'asc' | 'desc'
  
  // Pagination options
  /** Maximum number of items to return */
  limit?: number
  /** Number of items to skip (for pagination) */
  offset?: number
}

/**
 * Content Provider Interface
 * 
 * Defines the contract that all CMS providers must implement.
 * This interface ensures that different CMS backends can be
 * used interchangeably while providing consistent functionality.
 */
export interface ContentProvider {
  // Provider identification
  /** Human-readable name of the CMS provider */
  readonly name: string
  /** Version of the provider implementation */
  readonly version: string
  
  /**
   * Retrieve a single content item by type, slug, and locale
   * 
   * @param type - The content type to retrieve
   * @param slug - The unique slug identifier
   * @param locale - The language locale
   * @returns The content item or null if not found
   */
  getContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null>

  /**
   * Retrieve a list of content items with optional filtering and sorting
   * 
   * @param type - The content type to list
   * @param locale - The language locale
   * @param options - Optional query parameters for filtering and sorting
   * @returns Array of content items matching the criteria
   */
  getContentList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    options?: QueryOptions
  ): Promise<T[]>

  /**
   * Get all content for static site generation
   * 
   * @param type - The content type to retrieve
   * @returns Array of all content items of the specified type
   */
  getContentForStaticGeneration<T extends ContentItem>(
    type: ContentType
  ): Promise<T[]>

  /**
   * Get all content slugs for static path generation
   * 
   * @param type - The content type to get slugs for
   * @returns Array of slug/locale combinations
   */
  getAllContentSlugs(
    type: ContentType
  ): Promise<Array<{ locale: string; slug: string }>>
  
  /**
   * Check if content exists
   * 
   * @param type - The content type
   * @param slug - The content slug
   * @param locale - The language locale
   * @returns True if the content exists, false otherwise
   */
  contentExists(
    type: ContentType, 
    slug: string, 
    locale: string
  ): Promise<boolean>
  
  /**
   * Get content title only
   * 
   * @param type - The content type
   * @param slug - The content slug
   * @param locale - The language locale
   * @returns The content title or null if not found
   */
  getContentTitle(
    type: ContentType, 
    slug: string, 
    locale: string
  ): Promise<string | null>
  
  /**
   * Get content metadata
   * 
   * @param type - The content type
   * @param slug - The content slug
   * @param locale - The language locale
   * @returns Content metadata or null if not found
   */
  getContentMetadata(
    type: ContentType, 
    slug: string, 
    locale: string
  ): Promise<ContentMetadata | null>
  
  /**
   * Get available language versions
   * 
   * @param type - The content type
   * @param slug - The content slug (language-agnostic)
   * @returns Array of available language versions
   */
  getAvailableLanguages(
    type: ContentType, 
    slug: string
  ): Promise<LanguageVersion[]>
  
  /**
   * Get related content (optional feature)
   * 
   * @param type - The content type
   * @param currentSlug - The slug of the current content item
   * @param locale - The language locale
   * @param limit - Maximum number of related items to return
   * @returns Array of related content items
   */
  getRelatedContent?<T extends ContentItem>(
    type: ContentType,
    currentSlug: string,
    locale: string,
    limit?: number
  ): Promise<T[]>
}