/**
 * Markdown Renderer
 * 
 * A provider-agnostic component that renders raw markdown/MDX content.
 * Supports both standard markdown and MDX with custom components.
 */

'use client'

import { cn } from '@/lib/utils'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeRaw from 'rehype-raw'
import { Video, YouTube, Bilibili, VideoGallery } from './video-components'
import React from 'react'

interface MarkdownRendererProps {
  source: string
  components: Record<string, any>
  className?: string
}

/**
 * Process MDX-style components in markdown content
 * Converts <ComponentName prop="value" /> to HTML that can be rendered
 */
function processMDXComponents(content: string): string {
  let processed = content
  
  // Handle Video components
  processed = processed.replace(
    /<Video\s+([^>]*?)\/>/g,
    (match, props) => {
      // Extract props using regex
      const srcMatch = props.match(/src=["']([^"']+)["']/)
      const posterMatch = props.match(/poster=["']([^"']+)["']/)
      const src = srcMatch ? srcMatch[1] : ''
      const poster = posterMatch ? posterMatch[1] : ''
      
      // Return HTML video element
      return `<video src="${src}" poster="${poster}" controls class="w-full rounded-lg">Your browser does not support the video tag.</video>`
    }
  )
  
  // Handle YouTube components
  processed = processed.replace(
    /<YouTube\s+([^>]*?)\/>/g,
    (match, props) => {
      const videoIdMatch = props.match(/videoId=["']([^"']+)["']/)
      const titleMatch = props.match(/title=["']([^"']+)["']/)
      const videoId = videoIdMatch ? videoIdMatch[1] : ''
      const title = titleMatch ? titleMatch[1] : ''
      
      return `<iframe src="https://www.youtube.com/embed/${videoId}" title="${title}" class="w-full aspect-video rounded-lg" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen></iframe>`
    }
  )
  
  // Handle Bilibili components
  processed = processed.replace(
    /<Bilibili\s+([^>]*?)\/>/g,
    (match, props) => {
      const bvidMatch = props.match(/bvid=["']([^"']+)["']/)
      const titleMatch = props.match(/title=["']([^"']+)["']/)
      const bvid = bvidMatch ? bvidMatch[1] : ''
      const title = titleMatch ? titleMatch[1] : ''
      
      return `<iframe src="//player.bilibili.com/player.html?isOutside=true&bvid=${bvid}" title="${title}" class="w-full aspect-video rounded-lg" frameborder="0" allowfullscreen></iframe>`
    }
  )
  
  // Handle VideoGallery - convert to a simple div with videos
  processed = processed.replace(
    /<VideoGallery[\s\S]*?videos=\{(\[[\s\S]*?\])\}[\s\S]*?\/>/g,
    (match, videosArray) => {
      try {
        // This is a simplified approach - in production you might want a more robust parser
        return '<div class="grid grid-cols-2 gap-4"><!-- VideoGallery placeholder --></div>'
      } catch (e) {
        return '<!-- VideoGallery parsing error -->'
      }
    }
  )
  
  return processed
}

/**
 * Enhanced components mapping that includes HTML element handlers
 */
const createComponents = (baseComponents: Record<string, any>) => ({
  ...baseComponents,
  // Custom handlers for HTML elements
  video: ({ node, ...props }: any) => (
    <video {...props} />
  ),
  iframe: ({ node, ...props }: any) => (
    <iframe {...props} />
  ),
})

/**
 * Generic markdown renderer that works with any provider
 * This provides full markdown support with custom component mappings
 */
export default function MarkdownRenderer({ source, components, className }: MarkdownRendererProps) {
  // Process MDX components in the source
  const processedSource = processMDXComponents(source)
  
  return (
    <div className={cn('prose prose-neutral dark:prose-invert max-w-none', className)}>
      <ReactMarkdown 
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw]}
        components={createComponents(components)}
      >
        {processedSource}
      </ReactMarkdown>
    </div>
  )
}