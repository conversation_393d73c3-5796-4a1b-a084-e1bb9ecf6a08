/**
 * Video Components for MDX Content
 * 
 * This file contains video-related components that can be used within MDX content.
 * Supports self-hosted videos, YouTube, and Bilibili embeds with responsive design.
 */

'use client'

import { useState, useRef, useEffect } from 'react'
import { Play, Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

export interface VideoProps {
  src: string
  poster?: string
  className?: string
  controls?: boolean
  autoplay?: boolean
  muted?: boolean
  loop?: boolean
  width?: string | number
  height?: string | number
}

/**
 * Self-hosted Video Component
 * Supports MP4, WebM, and other HTML5 video formats
 */
export function Video({
  src,
  poster,
  className,
  controls = true,
  autoplay = false,
  muted = false,
  loop = false,
  width = "100%",
  height = "auto",
  ...props
}: VideoProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [errorMessage, setErrorMessage] = useState<string>('')
  const [isInitialized, setIsInitialized] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)

  // 页面加载完成后再初始化视频
  useEffect(() => {
    const initializeVideo = () => {
      setTimeout(() => {
        setIsInitialized(true)
      }, 100)
    }

    if (document.readyState === 'complete') {
      initializeVideo()
    } else {
      window.addEventListener('load', initializeVideo)
      return () => window.removeEventListener('load', initializeVideo)
    }
  }, [])

  // 添加超时处理
  useEffect(() => {
    if (!isInitialized) return

    const timeout = setTimeout(() => {
      if (isLoading && !hasError) {
        setIsLoading(false)
        setHasError(true)
        setErrorMessage('视频加载超时，请检查网络连接或重试')
      }
    }, 30000) // 30秒超时

    return () => clearTimeout(timeout)
  }, [isLoading, hasError, isInitialized])

  const handleLoadStart = () => {
    setIsLoading(true)
    setHasError(false)
    setErrorMessage('')
  }

  const handleCanPlay = () => {
    setIsLoading(false)
    setHasError(false)
  }

  const handleLoadedData = () => {
    setIsLoading(false)
    setHasError(false)
  }

  const handleLoadedMetadata = () => {
    // Video metadata loaded
  }

  const handleError = (e: any) => {
    setIsLoading(false)
    setHasError(true)

    // Get more detailed error information
    const video = e.target as HTMLVideoElement
    const error = video.error

    let message = '视频加载失败'
    if (error) {
      switch (error.code) {
        case MediaError.MEDIA_ERR_ABORTED:
          message = '视频加载被中止'
          break
        case MediaError.MEDIA_ERR_NETWORK:
          message = '网络错误，无法加载视频'
          break
        case MediaError.MEDIA_ERR_DECODE:
          message = '视频解码错误'
          break
        case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
          message = '视频格式不支持或文件不存在'
          break
        default:
          message = `视频加载失败 (错误代码: ${error.code})`
      }
    }

    setErrorMessage(message)
  }

  const handleRetry = () => {
    setIsLoading(true)
    setHasError(false)
    setErrorMessage('')

    if (videoRef.current) {
      const currentTime = videoRef.current.currentTime
      videoRef.current.load()

      // 恢复播放进度
      if (currentTime > 0) {
        videoRef.current.addEventListener('loadedmetadata', () => {
          videoRef.current!.currentTime = currentTime
        }, { once: true })
      }
    }
  }

  return (
    <div className={cn("relative mb-6 overflow-hidden rounded-lg border", className)}>
      {isLoading && !hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted z-10">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">加载视频中...</p>
          </div>
        </div>
      )}

      {!isInitialized ? (
        <div className="flex h-64 items-center justify-center bg-muted text-muted-foreground">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">初始化视频组件...</p>
          </div>
        </div>
      ) : hasError ? (
        <div className="flex h-64 items-center justify-center bg-muted text-muted-foreground">
          <div className="text-center">
            <p className="mb-2 font-medium">{errorMessage}</p>
            <p className="text-sm mb-4">视频源: {src}</p>
            <button
              onClick={handleRetry}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
            >
              重试
            </button>
          </div>
        </div>
      ) : (
        <video
          ref={videoRef}
          src={src}
          poster={poster}
          controls={controls}
          autoPlay={autoplay}
          muted={muted}
          loop={loop}
          width={width}
          height={height}
          onLoadStart={handleLoadStart}
          onCanPlay={handleCanPlay}
          onLoadedData={handleLoadedData}
          onLoadedMetadata={handleLoadedMetadata}
          onError={handleError}
          className="w-full"
          {...props}
        >
          您的浏览器不支持视频播放。
        </video>
      )}
    </div>
  )
}

export interface YouTubeProps {
  videoId: string
  title?: string
  className?: string
  autoplay?: boolean
  muted?: boolean
  controls?: boolean
  start?: number
  end?: number
}

/**
 * YouTube Video Embed Component
 * Responsive YouTube video player with lazy loading
 */
export function YouTube({ 
  videoId, 
  title = "YouTube Video", 
  className,
  autoplay = false,
  muted = false,
  controls = true,
  start,
  end
}: YouTubeProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsLoaded(true)
          observer.disconnect()
        }
      },
      { threshold: 0.1 }
    )

    if (containerRef.current) {
      observer.observe(containerRef.current)
    }

    return () => observer.disconnect()
  }, [])

  const buildYouTubeUrl = () => {
    const params = new URLSearchParams()
    if (autoplay) params.set('autoplay', '1')
    if (muted) params.set('mute', '1')
    if (!controls) params.set('controls', '0')
    if (start) params.set('start', start.toString())
    if (end) params.set('end', end.toString())
    
    const queryString = params.toString()
    return `https://www.youtube.com/embed/${videoId}${queryString ? `?${queryString}` : ''}`
  }

  return (
    <div 
      ref={containerRef}
      className={cn("relative mb-6 aspect-video overflow-hidden rounded-lg border", className)}
    >
      {!isLoaded ? (
        <div className="flex h-full items-center justify-center bg-muted">
          <div className="text-center">
            <Play className="mx-auto mb-2 h-12 w-12 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">点击加载 YouTube 视频</p>
          </div>
        </div>
      ) : (
        <iframe
          src={buildYouTubeUrl()}
          title={title}
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          allowFullScreen
          className="absolute inset-0 h-full w-full border-0"
        />
      )}
    </div>
  )
}

export interface BilibiliProps {
  bvid?: string
  aid?: string
  cid?: string
  page?: number
  title?: string
  className?: string
  autoplay?: boolean
  muted?: boolean
}

/**
 * Bilibili Video Embed Component
 * Responsive Bilibili video player with lazy loading
 */
export function Bilibili({ 
  bvid, 
  aid, 
  cid,
  page = 1,
  title = "Bilibili Video", 
  className,
  autoplay = false,
  muted = false
}: BilibiliProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsLoaded(true)
          observer.disconnect()
        }
      },
      { threshold: 0.1 }
    )

    if (containerRef.current) {
      observer.observe(containerRef.current)
    }

    return () => observer.disconnect()
  }, [])

  const buildBilibiliUrl = () => {
    const params = new URLSearchParams()
    if (bvid) params.set('bvid', bvid)
    if (aid) params.set('aid', aid)
    if (cid) params.set('cid', cid)
    if (page > 1) params.set('page', page.toString())
    if (autoplay) params.set('autoplay', '1')
    if (muted) params.set('muted', '1')
    
    const queryString = params.toString()
    return `//player.bilibili.com/player.html?${queryString}`
  }

  if (!bvid && !aid) {
    return (
      <div className="mb-6 flex h-64 items-center justify-center rounded-lg border bg-muted">
        <p className="text-muted-foreground">请提供 Bilibili 视频的 bvid 或 aid</p>
      </div>
    )
  }

  return (
    <div 
      ref={containerRef}
      className={cn("relative mb-6 aspect-video overflow-hidden rounded-lg border", className)}
    >
      {!isLoaded ? (
        <div className="flex h-full items-center justify-center bg-muted">
          <div className="text-center">
            <Play className="mx-auto mb-2 h-12 w-12 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">点击加载 Bilibili 视频</p>
          </div>
        </div>
      ) : (
        <iframe
          src={buildBilibiliUrl()}
          title={title}
          allowFullScreen
          className="absolute inset-0 h-full w-full border-0"
        />
      )}
    </div>
  )
}

/**
 * Video Gallery Component
 * Display multiple videos in a responsive grid
 */
export interface VideoGalleryProps {
  videos: Array<{
    type: 'video' | 'youtube' | 'bilibili'
    src?: string
    videoId?: string
    bvid?: string
    aid?: string
    title?: string
    poster?: string
  }>
  columns?: 1 | 2 | 3
  className?: string
}

export function VideoGallery({ videos, columns = 2, className }: VideoGalleryProps) {
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
  }

  return (
    <div className={cn(`grid gap-6 ${gridCols[columns]}`, className)}>
      {videos.map((video, index) => {
        switch (video.type) {
          case 'video':
            return (
              <Video
                key={index}
                src={video.src!}
                poster={video.poster}
              />
            )
          case 'youtube':
            return (
              <YouTube
                key={index}
                videoId={video.videoId!}
                title={video.title}
              />
            )
          case 'bilibili':
            return (
              <Bilibili
                key={index}
                bvid={video.bvid}
                aid={video.aid}
                title={video.title}
              />
            )
          default:
            return null
        }
      })}
    </div>
  )
}
