/**
 * Type-safe Link Component
 *
 * This component provides a type-safe wrapper around next-intl's Link component
 * with automatic locale handling and path generation. It supports both direct
 * href usage and structured path building using our paths utility.
 */

'use client'

import { Link as NextIntlLink } from '@/i18n/navigation'
import { paths } from '@/lib/route-utils'
import { useLocale } from 'next-intl'
import { ComponentProps } from 'react'

type PathKey = keyof typeof paths

interface SafeLinkProps extends Omit<ComponentProps<typeof NextIntlLink>, 'href'> {
  // For top-level paths like 'home', 'pricing', 'showcase'
  to?: PathKey
  // For nested paths like blogs.post, products.detail
  section?: 'blogs' | 'products' | 'caseStudies' | 'posts'
  action?: 'index' | 'detail' | 'post'  // Type of action within the section
  slug?: string                         // Content slug for dynamic routes
  // Fallback for custom hrefs
  href?: string
}

/**
 * Main SafeLink component with automatic path resolution
 *
 * This component handles different types of link generation:
 * 1. Direct href usage (fallback)
 * 2. Top-level paths using the 'to' prop
 * 3. Nested paths using section/action/slug combination
 *
 * The component automatically includes the current locale in the generated URLs.
 */
export function SafeLink({
  to,
  section,
  action = 'index',
  slug,
  href,
  children,
  ...props
}: SafeLinkProps) {
  const locale = useLocale()

  let finalHref: string

  if (href) {
    // Use custom href if provided (fallback option)
    finalHref = href
  } else if (to) {
    // Handle top-level paths like 'home', 'pricing', etc.
    const pathBuilder = paths[to]
    if (typeof pathBuilder === 'function') {
      finalHref = pathBuilder(locale as any)
    } else {
      finalHref = '/'
    }
  } else if (section && action) {
    // Handle nested paths like blogs.post, products.detail, etc.
    const sectionPaths = paths[section] as any

    if (sectionPaths && typeof sectionPaths[action] === 'function') {
      if (action === 'detail' || action === 'post') {
        // Dynamic routes require a slug
        if (!slug) {
          throw new Error(`Slug is required for ${section}.${action}`)
        }
        finalHref = sectionPaths[action](slug, locale)
      } else {
        // Index routes don't need a slug
        finalHref = sectionPaths[action](locale)
      }
    } else {
      // Fallback to home if path generation fails
      finalHref = '/'
    }
  } else {
    // Default fallback
    finalHref = '/'
  }

  return (
    <NextIntlLink href={finalHref} {...props}>
      {children}
    </NextIntlLink>
  )
}

/**
 * Convenience components for common content types
 *
 * These components provide a simpler API for linking to specific content types
 * without needing to specify section/action manually. They automatically
 * determine whether to link to an index page or detail page based on the slug.
 */

// Blog link component - links to blog index or specific post
export function BlogLink({ slug, children, ...props }: { slug?: string } & Omit<SafeLinkProps, 'section' | 'action' | 'slug'>) {
  return (
    <SafeLink
      section="blogs"
      action={slug ? 'post' : 'index'}
      slug={slug}
      {...props}
    >
      {children}
    </SafeLink>
  )
}

// Product link component - links to products index or specific product
export function ProductLink({ slug, children, ...props }: { slug?: string } & Omit<SafeLinkProps, 'section' | 'action' | 'slug'>) {
  return (
    <SafeLink
      section="products"
      action={slug ? 'detail' : 'index'}
      slug={slug}
      {...props}
    >
      {children}
    </SafeLink>
  )
}

// Case study link component - links to case studies index or specific case study
export function CaseStudyLink({ slug, children, ...props }: { slug?: string } & Omit<SafeLinkProps, 'section' | 'action' | 'slug'>) {
  return (
    <SafeLink
      section="caseStudies"
      action={slug ? 'detail' : 'index'}
      slug={slug}
      {...props}
    >
      {children}
    </SafeLink>
  )
}
