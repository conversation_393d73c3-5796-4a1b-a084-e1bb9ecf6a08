"use client";

import {
  Select,
  SelectContent,
  <PERSON>Item,
  SelectTrigger,
} from "@/components/ui/select";
import { useParams, usePathname, useRouter } from "next/navigation";
import { toast } from "sonner";

import { MdLanguage } from "react-icons/md";
import { localeNames, locales } from "@/i18n/locale";
import {
  detectContentPage,
  handleContentLanguageSwitch
} from "@/services/content";

export default function ({ isIcon = false }: { isIcon?: boolean }) {
  const params = useParams();
  const locale = params.locale as string;
  const router = useRouter();
  const pathname = usePathname();

  const handleSwitchLanguage = async (value: string) => {
    if (value !== locale) {
      // First detect if this is a content page
      const contentInfo = detectContentPage(pathname, locale);

      if (!contentInfo.isContentPage) {
        // Non-content pages: use original simple logic
        let newPathName = pathname.replace(`/${locale}`, `/${value}`);
        if (!newPathName.startsWith(`/${value}`)) {
          newPathName = `/${value}${newPathName}`;
        }
        router.push(newPathName);
      } else {
        try {
          // Content pages: use intelligent switching logic
          const switchResult = await handleContentLanguageSwitch({
            currentPath: pathname,
            currentLocale: locale as any,
            targetLocale: value as any,
            context: {
              contentType: contentInfo.contentType,
              slug: contentInfo.slug || ''
            }
          });

          // Show user feedback for fallback scenarios first, then navigate
          if (switchResult.strategy === 'fallback') {
            toast.info(
              `This content is not available in ${localeNames[value]}. Redirected to the content list.`,
              {
                duration: 2000,
                position: 'top-center',
              }
            );
            // Navigate after showing the message
            setTimeout(() => {
              router.push(switchResult.url);
            }, 2000);
          } else {
            // Direct navigation - no delay needed
            router.push(switchResult.url);
          }
        } catch (error) {
          console.error('Error during language switch:', error);
          toast.error('Failed to switch language. Please try again.');
        }
      }

      // Future: can add other page type handling here
      // else if (contentInfo.type === 'admin') { ... }
      // else if (contentInfo.type === 'special') { ... }
    }
  };

  return (
    <Select value={locale} onValueChange={handleSwitchLanguage}>
      <SelectTrigger className="flex items-center gap-2 border-none text-muted-foreground outline-hidden hover:bg-transparent focus:ring-0 focus:ring-offset-0">
        <MdLanguage className="text-xl" />
        {!isIcon && (
          <span className="hidden md:block">{localeNames[locale]}</span>
        )}
      </SelectTrigger>
      <SelectContent className="z-50 bg-background">
        {Object.keys(localeNames).map((key: string) => {
          const name = localeNames[key];
          return (
            <SelectItem className="cursor-pointer px-4" key={key} value={key}>
              {name}
            </SelectItem>
          );
        })}
      </SelectContent>
    </Select>
  );
}
