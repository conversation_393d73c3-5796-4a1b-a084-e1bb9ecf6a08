"use client";

import { useTranslations } from 'next-intl';

interface VideoGeneratorHeaderProps {
  className?: string;
  showSubtitle?: boolean;
}

export default function VideoGeneratorHeader({
  className = '',
  showSubtitle = true
}: VideoGeneratorHeaderProps) {
  const t = useTranslations('components.video_generator');

  return (
    <div className={`text-center mb-8 ${className}`}>
      <h1 className="text-4xl font-bold tracking-tight lg:text-5xl">
        {t('block.title', { fallback: 'AI Video Generator' })}
      </h1>
      {showSubtitle && (
        <p className="mt-4 text-xl text-muted-foreground max-w-3xl mx-auto">
          {t('block.subtitle', { 
            fallback: 'Transform your ideas into videos with advanced AI technology'
          })}
        </p>
      )}
    </div>
  );
} 