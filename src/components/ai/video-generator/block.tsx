"use client";

import VideoGenerator from "@/components/ai/video-generator/index";
import { VideoGenerationMode } from "@/types/ai/video-gen-types";
import VideoGeneratorHeader from "@/components/ai/video-generator/header";

interface VideoGeneratorBlockProps {
  id?: string;
  className?: string;
  showHeader?: boolean;
  showSubtitle?: boolean;
}

export default function VideoGeneratorBlock({
  id,
  className = "",
  showHeader = true,
  showSubtitle = true
}: VideoGeneratorBlockProps) {
  
  return (
    <section id={id} className={`pt-24 pb-24 ${className}`}>
      <div className="container">
        {/* Header component - can be toggled with showHeader prop */}
        {showHeader && (
          <div className="mb-16">
            <VideoGeneratorHeader showSubtitle={showSubtitle} />
          </div>
        )}
        {/* Main video generator component */}
        <div className="mx-auto w-full max-w-5xl">
          <VideoGenerator initialMode={VideoGenerationMode.TextToVideo} />
        </div>
      </div>
    </section>
  );
} 