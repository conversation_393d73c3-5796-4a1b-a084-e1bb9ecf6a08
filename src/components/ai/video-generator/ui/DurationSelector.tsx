"use client";

import { useTranslations } from 'next-intl';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { VideoDuration } from '@/types/ai/video-gen-types';
import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON>ip<PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface DurationOption {
  value: VideoDuration;
  label: string;
  description: string;
}

interface DurationSelectorProps {
  selectedDuration: VideoDuration | null;
  onChange: (duration: VideoDuration) => void;
  className?: string;
  disabled?: boolean;
}

export default function DurationSelector({
  selectedDuration,
  onChange,
  className = '',
  disabled = false
}: DurationSelectorProps) {
  const t = useTranslations('components.video_generator');
  
  // Duration options with descriptions
  const durationOptions: DurationOption[] = [
    { 
      value: VideoDuration.Seconds5, 
      label: t('duration.seconds5.label', { fallback: 'Short (5s)' }), 
      description: t('duration.seconds5.description', { fallback: 'Standard length, suitable for most scenes' })
    },
    { 
      value: VideoDuration.Seconds10, 
      label: t('duration.seconds10.label', { fallback: 'Long (10s)' }), 
      description: t('duration.seconds10.description', { fallback: 'Extended duration for complex scenes or transitions' })
    },
  ];

  return (
    <div className={`w-full ${className}`}>
      <div className="mb-4">
        <h3 className="text-md font-medium">
          {t('duration_selector.title', { fallback: 'Video Duration' })}
        </h3>
        <p className="text-sm text-muted-foreground">
          {t('duration_selector.description', { 
            fallback: 'Select the duration of your generated video'
          })}
        </p>
      </div>
      
      <RadioGroup
        className="grid grid-cols-1 md:grid-cols-2 gap-4"
        value={selectedDuration?.toString() || VideoDuration.Seconds5.toString()}
        onValueChange={(value) => onChange(parseInt(value) as VideoDuration)}
        disabled={disabled}
      >
        <TooltipProvider>
          {durationOptions.map((option) => (
            <div key={option.value} className="relative">
              <RadioGroupItem
                value={option.value.toString()}
                id={`duration-${option.value}`}
                className="peer sr-only"
                disabled={disabled}
              />
              <Tooltip>
                <TooltipTrigger asChild>
                  <Label
                    htmlFor={`duration-${option.value}`}
                    className="flex flex-col items-center justify-center rounded-md border-2 border-muted bg-transparent p-4 hover:bg-muted/50 hover:border-primary [&:has([data-state=checked])]:border-primary [&:has([data-state=checked])]:bg-primary/10 cursor-pointer transition-all peer-disabled:opacity-50 peer-disabled:cursor-not-allowed text-center h-full"
                  >
                    <span className="block text-sm font-medium">{option.label}</span>
                  </Label>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{option.description}</p>
                </TooltipContent>
              </Tooltip>
            </div>
          ))}
        </TooltipProvider>
      </RadioGroup>
    </div>
  );
} 