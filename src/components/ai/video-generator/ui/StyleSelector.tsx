"use client";

import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react';
import { VideoStyle } from '@/types/ai/video-gen-types';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface StyleOption {
  value: VideoStyle;
  label: string;
  description?: string;
}

interface StyleSelectorProps {
  selectedStyle: VideoStyle | null;
  onChange: (style: VideoStyle) => void;
  className?: string;
  disabled?: boolean;
}

/**
 * StyleSelector Component
 * 
 * A component that displays a grid of style options that users can select from.
 * Each style is represented as a card with a label and optional tooltip description.
 * 
 * @param selectedStyle - The currently selected video style
 * @param onChange - Callback function when a style is selected
 * @param className - Additional CSS classes
 * @param disabled - Whether the component is disabled
 */
export default function StyleSelector({
  selectedStyle,
  onChange,
  className = '',
  disabled = false
}: StyleSelectorProps) {
  const t = useTranslations('components.video_generator');
  
  // Log when selectedStyle changes to help with debugging
  useEffect(() => {
    console.log('StyleSelector: selectedStyle changed to', selectedStyle);
  }, [selectedStyle]);
  
  // Define all available style options with descriptions
  const styleOptions: StyleOption[] = [
    { 
      value: VideoStyle.NoStyle, 
      label: t('styles.no_style.label', { fallback: 'No Style' }), 
      description: t('styles.no_style.description', { fallback: 'Generate video without any specific style' })
    },
    { 
      value: VideoStyle.RealisticStyle, 
      label: t('styles.realistic.label', { fallback: 'Realistic' }), 
      description: t('styles.realistic.description', { fallback: 'Photorealistic video with natural elements' })
    },
    { 
      value: VideoStyle.CinematicStyle, 
      label: t('styles.cinematic.label', { fallback: 'Cinematic' }), 
      description: t('styles.cinematic.description', { fallback: 'Professional movie-like appearance with film grain and dramatic lighting' })
    },
    { 
      value: VideoStyle.AnimeStyle, 
      label: t('styles.anime.label', { fallback: 'Anime' }), 
      description: t('styles.anime.description', { fallback: 'Japanese animation style with colorful characters' })
    },
    { 
      value: VideoStyle.CartoonStyle, 
      label: t('styles.cartoon.label', { fallback: 'Cartoon' }), 
      description: t('styles.cartoon.description', { fallback: 'Classic cartoon animation with exaggerated features' })
    },
    { 
      value: VideoStyle.PixarStyle, 
      label: t('styles.pixar.label', { fallback: 'Pixar' }), 
      description: t('styles.pixar.description', { fallback: '3D animation style similar to Pixar films' })
    },
    { 
      value: VideoStyle.GhibliStyle, 
      label: t('styles.ghibli.label', { fallback: 'Ghibli' }), 
      description: t('styles.ghibli.description', { fallback: 'Inspired by Studio Ghibli\'s distinctive artistic style' })
    },
    { 
      value: VideoStyle.PixelArtStyle, 
      label: t('styles.pixel_art.label', { fallback: 'Pixel Art' }), 
      description: t('styles.pixel_art.description', { fallback: 'Retro video game inspired pixel aesthetics' })
    },
  ];

  // Ensure we have a valid style selected
  const currentStyle = selectedStyle || VideoStyle.NoStyle;
  
  /**
   * Handle clicking on a style option
   * @param style - The style being selected
   */
  const handleStyleClick = (style: VideoStyle) => {
    if (!disabled) {
      console.log('StyleSelector: style clicked', style);
      onChange(style);
    }
  };

  return (
    <div className={`w-full ${className}`}>
      {/* Development-only debug information */}
      {/* {process.env.NODE_ENV === 'development' && (
        <div className="text-xs text-muted-foreground mb-2">
          Current style: {currentStyle}
        </div>
      )} */}
      
      {/* Grid of style options */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <TooltipProvider>
          {styleOptions.map((option) => {
            // Check if this style is the selected one
            const isSelected = currentStyle === option.value;
            
            return (
              <div key={option.value} className="relative">
                <Tooltip>
                  <TooltipTrigger asChild>
                    {/* Style option card */}
                    <button
                      type="button"
                      onClick={() => handleStyleClick(option.value)}
                      disabled={disabled}
                      className={`
                        w-full h-full flex flex-col items-center justify-center
                        rounded-md border-2 p-4 text-center
                        transition-all duration-200
                        ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:bg-muted/50 hover:border-primary'}
                        ${isSelected 
                          ? 'border-primary bg-primary/10 shadow-sm' 
                          : 'border-muted bg-transparent'}
                      `}
                      aria-pressed={isSelected}
                    >
                      <span className="block text-sm font-medium">{option.label}</span>
                    </button>
                  </TooltipTrigger>
                  {option.description && (
                    <TooltipContent side="top">
                      <p>{option.description}</p>
                    </TooltipContent>
                  )}
                </Tooltip>
              </div>
            );
          })}
        </TooltipProvider>
      </div>
    </div>
  );
} 