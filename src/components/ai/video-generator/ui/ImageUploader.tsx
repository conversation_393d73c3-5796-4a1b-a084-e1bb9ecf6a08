"use client";

import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useState } from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { FileUploader } from '@/components/ui/file-uploader';
import { UploadResult } from '@/types/file-upload';
import { FileInfo, FileCategory } from '@/types/file';

interface UploadedFile extends FileInfo {
  /** Local blob URL for immediate preview */
  previewUrl: string;
}

interface ImageUploaderProps {
  onFileUpload: (file: UploadedFile | null) => void;
  onFileRemove?: () => void;
  height?: number | string;
  className?: string;
  disabled?: boolean;
  title?: string;
  description?: string;
  /** User UUID for authentication */
  userUuid?: string;
}

export default function ImageUploader({
  onFileUpload,
  onFileRemove,
  height = 300,
  className = '',
  disabled = false,
  title,
  description,
  userUuid
}: ImageUploaderProps) {
  const t = useTranslations('components.video_generator');
  const [uploadedFile, setUploadedFile] = useState<UploadedFile | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Use provided title/description or fallbacks from translations
  const displayTitle = title || t('image_to_video.uploader_title', { fallback: 'Reference Image' });
  const displayDescription = description || t('image_to_video.uploader_description', { 
    fallback: 'Upload an image to animate into a video'
  });

  // Handle successful file upload
  const handleUploadComplete = (results: UploadResult[], files: File[]) => {
    if (results.length > 0 && results[0].success && files.length > 0) {
      const result = results[0];
      const newFile: UploadedFile = {
        fileId: result.fileId,
        fileName: result.fileName,
        fileSize: result.fileSize,
        mimeType: result.mimeType,
        category: FileCategory.USER_UPLOADS, // Default category for video generator uploads
        url: '',
        previewUrl: URL.createObjectURL(files[0]), // Create local URL for preview
      };
      
      setUploadedFile(newFile);
      onFileUpload(newFile);
      setError(null);
      console.log('File uploaded successfully for video generation:', newFile);
    }
  };

  // Handle upload error
  const handleUploadError = (error: string) => {
    console.error('Upload failed:', error);
    setError(error);
  };

  // Handle image removal
  const handleRemoveImage = () => {
    setUploadedFile(null);
    onFileUpload(null);
    onFileRemove?.(); // Notify parent component about file removal
    setError(null);
  };

  return (
    <div className={`w-full ${className}`}>
      {/* Display title outside the container when an image is present */}
      {uploadedFile && (
        <div className="mb-4">
          <h3 className="text-md font-medium">{displayTitle}</h3>
        </div>
      )}
      
      <div style={{ height: typeof height === 'number' ? `${height}px` : height }}>
        {!uploadedFile ? (
          <FileUploader
            acceptMimeList={['image/jpeg', 'image/png', 'image/gif', 'image/webp']}
            maxFileCount={1}
            maxFileSize={10 * 1024 * 1024} // 10MB
            userUuid={userUuid}
            onUploadComplete={handleUploadComplete}
            onUploadError={handleUploadError}
            disabled={disabled}
            className="h-full"
          />
        ) : (
          <div className="relative w-full h-full">
            <Image
              src={uploadedFile.previewUrl}
              alt={uploadedFile.fileName}
              fill
              className="object-contain rounded-md"
              sizes="(max-width: 640px) 100vw, 50vw"
            />
            <Button
              variant="destructive"
              size="icon"
              className="absolute top-2 right-2"
              onClick={handleRemoveImage}
              disabled={disabled}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

      {/* Error display */}
      {error && (
        <div className="mt-4 p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
        </div>
      )}
    </div>
  );
} 