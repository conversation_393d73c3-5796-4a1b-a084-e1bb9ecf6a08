"use client";

import { useTranslations } from 'next-intl';
import { useEffect, useState, useRef, useCallback } from 'react';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { useAppContext } from "@/contexts/app"; // Import AppContext
import {
  VideoGenerationMode,
  VideoStyle,
  VideoDuration,
  VideoAspectRatio,
  VideoGenerationRequest,
} from '@/types/ai/video-gen-types';
import PromptInput from './ui/PromptInput';
import StyleSelector from './ui/StyleSelector';
import DurationSelector from './ui/DurationSelector';
import ImageUploader from './ui/ImageUploader';
import GenerationResults from './ui/GenerationResults';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import {
  Tooltip,
  TooltipContent,
  Toolt<PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { GenerationStatus } from '@/types/ai/common';
import { UploadedFile } from '@/types/file-upload';
import { FileInfo } from '@/types/file';
import { useFileCleanup } from '@/hooks/useFileCleanup';

// Define size configuration interface
interface SizeConfig {
  inputHeight?: number;     // Height for prompt input
  uploaderHeightPercentage?: number;  // Height for image uploader
  minInputHeight?: number;  // Minimum height for input
  maxInputHeight?: number;  // Maximum height for input
  
  // Responsive size configurations
  mobile?: Partial<SizeConfig>;  // For mobile devices (< 640px)
  tablet?: Partial<SizeConfig>;  // For tablet devices (< 1024px)
}

// Default size configuration
const DEFAULT_SIZE_CONFIG: SizeConfig = {
  inputHeight: 220,
  uploaderHeightPercentage: 100,
  minInputHeight: 120,
  maxInputHeight: 300,
  
  // Responsive defaults
  mobile: {
    inputHeight: 150,
    minInputHeight: 100
  },
  tablet: {
    inputHeight: 160
  }
};

/**
 * Creates a custom size configuration for the video generator
 */
export function createSizeConfig(config: Partial<SizeConfig>): Partial<SizeConfig> {
  return {
    ...config,
    // Ensure nested responsive configs are merged properly
    mobile: config.mobile ? { ...config.mobile } : undefined,
    tablet: config.tablet ? { ...config.tablet } : undefined,
  };
}

/**
 * AI Video Generator Component
 * Follows clean separation of concerns:
 * - Manages UI state and user interactions
 * - Collects form data from various inputs
 * - Communicates directly with backend API
 * - Displays generation results
 */
export default function VideoGenerator({
  initialMode = VideoGenerationMode.TextToVideo,
  className = '',
  onVideosGenerated,
  sizeConfig,
}: {
  initialMode?: VideoGenerationMode;
  className?: string;
  onVideosGenerated?: (videoUrls: string[]) => void;
  sizeConfig?: Partial<SizeConfig>;
}) {
  const t = useTranslations('components.video_generator');
  const [mode, setMode] = useState<VideoGenerationMode>(initialMode);
  const [prompt, setPrompt] = useState('');
  const [uploadedFile, setUploadedFile] = useState<UploadedFile | null>(null);
  const [removedFileIds, setRemovedFileIds] = useState<string[]>([]);
  
  // Get user context for authentication and user identification
  const { user } = useAppContext();
  
  // Initialize file cleanup hook
  const { cleanupFiles } = useFileCleanup(user?.uuid);
  
  // Handle responsive size configurations
  const [currentSizeConfig, setCurrentSizeConfig] = useState<SizeConfig>(
    { ...DEFAULT_SIZE_CONFIG, ...sizeConfig }
  );
  
  // Update size config based on screen size
  useEffect(() => {
    // Function to merge appropriate config based on window width
    const updateSizeConfig = () => {
      // Start with base config (merged from default and user provided)
      const baseConfig = { ...DEFAULT_SIZE_CONFIG, ...sizeConfig };
      let responsiveConfig = { ...baseConfig };
      
      // Apply mobile config for small screens
      if (window.innerWidth < 640 && baseConfig.mobile) {
        responsiveConfig = { ...responsiveConfig, ...baseConfig.mobile };
      } 
      // Apply tablet config for medium screens
      else if (window.innerWidth < 1024 && baseConfig.tablet) {
        responsiveConfig = { ...responsiveConfig, ...baseConfig.tablet };
      }
      
      // Remove nested configs before setting state
      const { mobile, tablet, ...cleanConfig } = responsiveConfig;
      setCurrentSizeConfig(cleanConfig as SizeConfig);
    };
    
    // Initial update
    updateSizeConfig();
    
    // Set up listener for window resize
    window.addEventListener('resize', updateSizeConfig);
    
    // Clean up
    return () => window.removeEventListener('resize', updateSizeConfig);
  }, [sizeConfig]);
  
  // Style and configuration options
  const [videoStyle, setVideoStyle] = useState<VideoStyle | null>(VideoStyle.NoStyle);
  const [aspectRatio, setAspectRatio] = useState<VideoAspectRatio | null>(VideoAspectRatio.Square);
  const [duration, setDuration] = useState<VideoDuration | null>(VideoDuration.Seconds5);
  
  // Enhanced generation status with file metadata
  const [status, setStatus] = useState<GenerationStatus>({
    isGenerating: false,
    progress: 0,
    files: [],
    error: undefined,
  });

  // Task polling
  const [taskId, setTaskId] = useState<string | null>(null);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Poll for task status
  const pollTaskStatus = useCallback(async (id: string) => {
    try {
      const response = await fetch(`/api/ai/tasks/${id}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to get task status');
      }

      const taskData = data.data;
      
      // Handle different status codes from KIE
      if (taskData.status === 'completed') {
        // Task completed successfully - files are now included in the main response
        clearInterval(pollingIntervalRef.current as NodeJS.Timeout);
        pollingIntervalRef.current = null;

        // Extract file metadata and convert to FileInfo format
        let fileInfos: FileInfo[] = [];
        
        if (taskData.files && taskData.files.length > 0) {
          // Direct use of all returned files - no filtering needed
          fileInfos = taskData.files.map((file: any) => ({
            fileId: file.file_id,
            fileName: file.file_name || `generated-video-${Date.now()}.mp4`,
            fileSize: file.file_size || 0,
            mimeType: file.mime_type || 'video/mp4',
            category: file.file_category || 'task-output',
            url: `/api/files/${file.file_id}/preview`
          }));
        }
        
        console.log("Extracted video file info from response:", {
          filesCount: taskData.files.length,
          fileInfos
        });

        setStatus({
          isGenerating: false,
          progress: 100,
          files: fileInfos,
          error: undefined
        });

        // Notify parent component if callback provided
        if (onVideosGenerated && fileInfos.length > 0) {
          const videoUrls = fileInfos.map(file => file.url);
          onVideosGenerated(videoUrls);
        }
      } 
      else if (taskData.status === 'failed') {
        // Task failed
        clearInterval(pollingIntervalRef.current as NodeJS.Timeout);
        pollingIntervalRef.current = null;
        
        setStatus({
          isGenerating: false,
          progress: 0,
          files: [],
          error: taskData.error || t('errors.generation_failed', { fallback: 'Video generation failed' })
        });
      }
      else {
        // Task still in progress
        setStatus((prev: GenerationStatus) => ({
          ...prev,
          isGenerating: true,
          progress: taskData.progress || 
            (taskData.status === 'processing' ? 50 : 10) // Default progress values based on status
        }));
      }
    } catch (error) {
      console.error('Error polling task status:', error);
      
      // Don't stop polling on temporary errors
      // Let the UI continue to show the loading state
    }
  }, [t, onVideosGenerated]);
  
  // Clean up polling interval on component unmount
  useEffect(() => {
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
    };
  }, []);

  // Scroll to results when generation starts
  const [resultsRef, setResultsRef] = useState<HTMLDivElement | null>(null);
  
  useEffect(() => {
    if (status.isGenerating && resultsRef) {
      resultsRef.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, [status.isGenerating, resultsRef]);

  /**
   * Handle mode change between different video generation types
   */
  const handleModeChange = (newMode: VideoGenerationMode) => {
    setMode(newMode);
    // Clear uploaded file when switching modes to prevent state inconsistency
    setUploadedFile(null);
    // Reset style to default rather than null for consistent selection state
    setVideoStyle(VideoStyle.NoStyle);
    setStatus({
      ...status,
      files: [],
      error: undefined,
    });
  };

  // Handle style selection with explicit logging to help debug issues
  const handleStyleChange = (newStyle: VideoStyle) => {
    console.log('VideoGenerator: style changed to', newStyle);
    setVideoStyle(newStyle);
  };

  // Handle file upload for image-to-video mode
  const handleFileUpload = (file: UploadedFile | null) => {
    // If we're replacing an existing file, mark the old one for cleanup
    if (uploadedFile?.fileId && file?.fileId !== uploadedFile.fileId) {
      setRemovedFileIds(prev => [...prev, uploadedFile.fileId]);
    }
    
    setUploadedFile(file);
  };

  // Handle file removal for image-to-video mode
  const handleFileRemove = () => {
    if (uploadedFile?.fileId) {
      setRemovedFileIds(prev => [...prev, uploadedFile.fileId]);
    }
    setUploadedFile(null);
  };

  // Handle prompt change
  const handlePromptChange = (newPrompt: string) => {
    setPrompt(newPrompt);
  };

  // 创建时长选项
  const durationOptions = [
    { 
      value: VideoDuration.Seconds5, 
      label: t('duration.seconds5.label', { fallback: 'Short (5s)' }), 
      description: t('duration.seconds5.description', { fallback: 'Standard length, suitable for most scenes' })
    },
    { 
      value: VideoDuration.Seconds10, 
      label: t('duration.seconds10.label', { fallback: 'Long (10s)' }), 
      description: t('duration.seconds10.description', { fallback: 'Extended duration for complex scenes or transitions' })
    },
  ];

  /**
   * Handles the video generation process by:
   * 1. Validating user input
   * 2. Setting initial generation status
   * 3. Sending data directly to backend API
   * 4. Updating UI with results or errors
   */
  const handleGenerate = async () => {
    try {
      // Clean up files that user has removed before starting generation
      if (removedFileIds.length > 0) {
        await cleanupFiles(removedFileIds, 'removed by user');
        setRemovedFileIds([]);
      }

      // Validate prompt
      if (!prompt.trim()) {
        setStatus({
          isGenerating: false,
          progress: 0,
          files: [],
          error: t('errors.prompt_required', { fallback: 'Please enter a prompt' })
        });
        return;
      }

      // For image-to-video mode, check if file is uploaded
      if (mode === VideoGenerationMode.ImageToVideo && !uploadedFile) {
        setStatus({
          isGenerating: false,
          progress: 0,
          files: [],
          error: t('errors.image_required', { fallback: 'Please upload an image' })
        });
        return;
      }

      // Reset previous results and set loading state
      setStatus({
        isGenerating: true,
        progress: 10, // Initial progress indicator
        files: [],
        error: undefined
      });

      // Construct request object
      const request: VideoGenerationRequest = {
        mode,
        prompt,
        aspectRatio: aspectRatio || undefined,
        duration: duration || undefined,
        videoStyle: videoStyle || undefined,
        // outputFormat: DEFAULT_EXTENSIONS.VIDEO,
      };

      // Add file ID for image-to-video mode
      if (mode === VideoGenerationMode.ImageToVideo && uploadedFile) {
        request.fileId = uploadedFile.fileId;
      }

      // Send request to API endpoint
      const response = await fetch('/api/ai/generate-video', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to generate video');
      }

      // Check if we have an immediate result (synchronous response)
      if (data.data.files && data.data.files.length > 0) {
        // Immediate result available
        setStatus({
          isGenerating: false,
          progress: 100,
          files: data.data.files,
          error: undefined
        });

        // Notify parent component if callback provided
        if (onVideosGenerated) {
          const videoUrls = data.data.files.map((file: FileInfo) => file.url);
          onVideosGenerated(videoUrls);
        }
      } else if (data.data.taskId) {
        // Asynchronous processing - start polling
        const generationTaskId = data.data.taskId;
        setTaskId(generationTaskId);
        
        // Start polling for task status
        pollingIntervalRef.current = setInterval(() => {
          pollTaskStatus(generationTaskId);
        }, 3000); // Poll every 3 seconds
        
        console.log("Started polling for task:", generationTaskId);
      } else {
        // Unexpected response format
        throw new Error('Unexpected response format from server');
      }
    } catch (error) {
      console.error('Error generating video:', error);
      
      // Set error state
      setStatus({
        isGenerating: false,
        progress: 0,
        files: [],
        error: error instanceof Error 
          ? error.message 
          : t('errors.unknown', { fallback: 'An unknown error occurred' })
      });
    }
  };

  /**
   * Clears the form and results
   * Also cleans up all uploaded files since user wants to start fresh
   */
  const handleClear = async () => {
    // Collect all files that need to be cleaned up (current + removed)
    const allFileIds = [
      ...(uploadedFile?.fileId ? [uploadedFile.fileId] : []),
      ...removedFileIds
    ];
    
    // Clean up all files since user wants to start fresh
    if (allFileIds.length > 0) {
      await cleanupFiles(allFileIds, 'form cleared');
    }

    // Reset all form state
    setPrompt('');
    setUploadedFile(null);
    setRemovedFileIds([]);
    setVideoStyle(VideoStyle.NoStyle);
    setStatus({
      isGenerating: false,
      progress: 0,
      files: [],
      error: undefined
    });
    setTaskId(null);
    
    // Clear polling interval if active
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
  };

  return (
    <div className={`w-full space-y-6 ${className}`}>
      {/* Mode selector tabs */}
      <Tabs value={mode} onValueChange={(value) => handleModeChange(value as VideoGenerationMode)}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value={VideoGenerationMode.TextToVideo}>
            {t('modes.text_to_video', { fallback: 'Text to Video' })}
          </TabsTrigger>
          <TabsTrigger value={VideoGenerationMode.ImageToVideo}>
            {t('modes.image_to_video', { fallback: 'Image to Video' })}
          </TabsTrigger>
        </TabsList>

        {/* Text to Video tab */}
        <TabsContent value={VideoGenerationMode.TextToVideo} className="mt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Left column: Style selector and options */}
            <div className="w-full flex flex-col justify-between h-full">
              {/* Style selector */}
              <div className="flex-grow">
                <div className="mb-3">
                  <label className="block text-sm font-medium mb-2">
                    {t('styles.title', { fallback: 'Video Style' })}
                  </label>
                  <StyleSelector 
                    selectedStyle={videoStyle}
                    onChange={handleStyleChange}
                    disabled={status.isGenerating}
                  />
                </div>
              </div>

              {/* Duration and Aspect Ratio dropdowns */}
              <div className="flex flex-col sm:flex-row gap-4 mt-2">
                <div className="flex-1">
                  <label className="block text-sm font-medium mb-2">
                    {t('duration_selector.title', { fallback: 'Duration' })}
                  </label>
                  <Select 
                    value={duration ? duration.toString() : undefined} 
                    onValueChange={(value) => setDuration(value as unknown as VideoDuration)}
                    disabled={status.isGenerating}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t('duration_selector.description', { fallback: 'Select duration' })} />
                    </SelectTrigger>
                    <SelectContent>
                      {durationOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value.toString()}>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span>{option.label}</span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{option.description}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex-1">
                  <label className="block text-sm font-medium mb-2">
                    {t('aspect_ratio.title', { fallback: 'Aspect Ratio' })}
                  </label>
                  <Select 
                    value={aspectRatio ? aspectRatio.toString() : undefined} 
                    onValueChange={(value) => setAspectRatio(value as unknown as VideoAspectRatio)}
                    disabled={status.isGenerating}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t('aspect_ratio.placeholder', { fallback: 'Select aspect ratio' })} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={VideoAspectRatio.Square.toString()}>
                        {t('aspect_ratio.square', { fallback: 'Square (1:1)' })}
                      </SelectItem>
                      <SelectItem value={VideoAspectRatio.Portrait.toString()}>
                        {t('aspect_ratio.portrait', { fallback: 'Portrait (9:16)' })}
                      </SelectItem>
                      <SelectItem value={VideoAspectRatio.Landscape.toString()}>
                        {t('aspect_ratio.landscape', { fallback: 'Landscape (16:9)' })}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Right column: Prompt and buttons */}
            <div className="w-full flex flex-col justify-between h-full">
              {/* Prompt input */}
              <div className="flex-grow">
                <PromptInput
                  value={prompt}
                  onChange={handlePromptChange}
                  minHeight={180}
                  maxHeight={300}
                  disabled={status.isGenerating}
                />
              </div>

              {/* Action buttons */}
              <div className="flex justify-end gap-4 mt-4">
                <Button 
                  onClick={handleClear}
                  variant="outline" 
                  size="lg"
                  disabled={status.isGenerating}
                  className="min-w-[100px]"
                >
                  {t('buttons.clear', { fallback: 'Clear' })}
                </Button>
                
                <Button 
                  onClick={handleGenerate} 
                  size="lg"
                  disabled={status.isGenerating || !prompt.trim() || !user?.uuid}
                  className="min-w-[160px]"
                  title={!user?.uuid ? 'Please login to generate videos' : ''}
                >
                  {status.isGenerating
                    ? t('buttons.generating', { fallback: 'Generating...' })
                    : t('buttons.generate', { fallback: 'Generate Video' })}
                </Button>
              </div>
            </div>
          </div>
        </TabsContent>

        {/* Image to Video tab */}
        <TabsContent value={VideoGenerationMode.ImageToVideo} className="mt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Image uploader */}
            <div className="w-full">
              <ImageUploader 
                onFileUpload={handleFileUpload}
                onFileRemove={handleFileRemove}
                height={310}
                disabled={status.isGenerating}
                title={t('image_to_video.uploader_title', { fallback: 'Reference Image' })}
                description={t('image_to_video.uploader_description', { 
                  fallback: 'Upload an image to animate into a video'
                })}
                userUuid={user?.uuid}
              />
            </div>

            {/* Prompt, duration and buttons */}
            <div className="w-full flex flex-col justify-between h-full">
              {/* Prompt input */}
              <div className="flex-grow">
                <PromptInput
                  value={prompt}
                  onChange={handlePromptChange}
                  minHeight={160}
                  maxHeight={250}
                  placeholder={t('image_to_video.prompt_placeholder', { 
                    fallback: "Describe how you want the image to animate (e.g., 'zoom in slowly', 'character walking'...)" 
                  })}
                  disabled={status.isGenerating}
                />
              </div>

              {/* Duration and Aspect Ratio dropdowns */}
              <div className="flex flex-col sm:flex-row gap-4 mt-4">
                <div className="flex-1">
                  <label className="block text-sm font-medium mb-2">
                    {t('duration_selector.title', { fallback: 'Duration' })}
                  </label>
                  <Select 
                    value={duration ? duration.toString() : undefined} 
                    onValueChange={(value) => setDuration(value as unknown as VideoDuration)}
                    disabled={status.isGenerating}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t('duration_selector.description', { fallback: 'Select duration' })} />
                    </SelectTrigger>
                    <SelectContent>
                      {durationOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value.toString()}>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span>{option.label}</span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{option.description}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex-1">
                  <label className="block text-sm font-medium mb-2">
                    {t('aspect_ratio.title', { fallback: 'Aspect Ratio' })}
                  </label>
                  <Select 
                    value={aspectRatio ? aspectRatio.toString() : undefined} 
                    onValueChange={(value) => setAspectRatio(value as unknown as VideoAspectRatio)}
                    disabled={status.isGenerating}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t('aspect_ratio.placeholder', { fallback: 'Select aspect ratio' })} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={VideoAspectRatio.Square.toString()}>
                        {t('aspect_ratio.square', { fallback: 'Square (1:1)' })}
                      </SelectItem>
                      <SelectItem value={VideoAspectRatio.Portrait.toString()}>
                        {t('aspect_ratio.portrait', { fallback: 'Portrait (9:16)' })}
                      </SelectItem>
                      <SelectItem value={VideoAspectRatio.Landscape.toString()}>
                        {t('aspect_ratio.landscape', { fallback: 'Landscape (16:9)' })}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Action buttons */}
              <div className="flex justify-end gap-4 mt-4">
                <Button 
                  onClick={handleClear}
                  variant="outline" 
                  size="default"
                  disabled={status.isGenerating}
                  className="min-w-[100px]"
                >
                  {t('buttons.clear', { fallback: 'Clear' })}
                </Button>
                
                <Button 
                  onClick={handleGenerate} 
                  size="default"
                  disabled={status.isGenerating || !prompt.trim() || !uploadedFile || !user?.uuid}
                  className="min-w-[160px]"
                  title={!user?.uuid ? 'Please login to generate videos' : ''}
                >
                  {status.isGenerating
                    ? t('buttons.generating', { fallback: 'Generating...' })
                    : t('buttons.generate', { fallback: 'Generate Video' })}
                </Button>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* Results section */}
      <GenerationResults 
        status={status}
        onRetry={handleGenerate}
        setResultsRef={setResultsRef}
        className="mt-8"
      />
    </div>
  );
}
