"use client";

import { useTranslations } from 'next-intl';
import { useEffect, useState, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import {
  AspectRatio,
  ColorOption,
  CompositionOption,
  ImageGenerationMode,
  ImageGenerationRequest,
  ImageStyle,
} from '@/types/ai/image-gen-types';
import ImageUploader from './ui/ImageUploader';
import StyleSelector from './ui/StyleSelector';
import GenerationResults from './ui/GenerationResults';
import PromptInput from './ui/PromptInput';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { TaskStatus } from '@/types/task';
import { useAppContext } from "@/contexts/app";
import { GenerationStatus } from '@/types/ai/common';
import { UploadedFile } from '@/types/file-upload';
import { FileInfo } from '@/types/file';
import { cn } from '@/lib/utils';
import { useFileCleanup } from '@/hooks/useFileCleanup';

// Constants
const MAX_PROMPT_LENGTH = 1000;

// Define size configuration interface
interface SizeConfig {
  inputHeight?: number;     // Height for prompt input
  uploaderHeightPercentage?: number;  // Height for image uploader
  minInputHeight?: number;  // Minimum height for input
  maxInputHeight?: number;  // Maximum height for input
  
  // Responsive size configurations
  mobile?: Partial<SizeConfig>;  // For mobile devices (< 640px)
  tablet?: Partial<SizeConfig>;  // For tablet devices (< 1024px)
}

// const DEFAULT_INPUT_HEIGHT = 220;
// Default size configuration
const DEFAULT_SIZE_CONFIG: SizeConfig = {
  inputHeight: 220,
  uploaderHeightPercentage: 100,
  minInputHeight: 120,
  maxInputHeight: 300,
  
  // Responsive defaults
  mobile: {
    inputHeight: 150,
    minInputHeight: 100
  },
  tablet: {
    inputHeight: 160
  }
};

/**
 * Creates a custom size configuration for the image generator
 * Supports responsive configurations for different screen sizes
 * 
 * @example
 * // Create a custom size configuration with responsive settings
 * const customSizeConfig = createSizeConfig({
 *   inputHeight: '220px',
 *   mobile: {
 *     inputHeight: '160px',
 *   },
 *   tablet: {
 *     inputHeight: '180px',
 *   }
 * });
 * 
 * // Then use it with the ImageGenerator component
 * <ImageGenerator sizeConfig={customSizeConfig} />
 */
export function createSizeConfig(config: Partial<SizeConfig>): Partial<SizeConfig> {
  return {
    ...config,
    // Ensure nested responsive configs are merged properly
    mobile: config.mobile ? { ...config.mobile } : undefined,
    tablet: config.tablet ? { ...config.tablet } : undefined,
  };
}

/**
 * AI Image Generator Component
 * Follows clean separation of concerns:
 * - Manages UI state and user interactions
 * - Collects form data from various inputs
 * - Communicates directly with backend API
 * - Displays generation results
 */
export default function ImageGenerator({
  initialMode = ImageGenerationMode.TextToImage,
  className = '',
  onImagesGenerated,
  sizeConfig,
}: {
  initialMode?: ImageGenerationMode;
  className?: string;
  onImagesGenerated?: (imageUrls: string[]) => void;
  sizeConfig?: Partial<SizeConfig>;
}) {
  const t = useTranslations('components.image_generator.core');
  const [mode, setMode] = useState<ImageGenerationMode>(initialMode);
  const [prompt, setPrompt] = useState('');
  const [negativePrompt, setNegativePrompt] = useState('');
  const [outputCount, setOutputCount] = useState(1);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [removedFileIds, setRemovedFileIds] = useState<string[]>([]);
  
  // Handle responsive size configurations
  const [currentSizeConfig, setCurrentSizeConfig] = useState<SizeConfig>(
    { ...DEFAULT_SIZE_CONFIG, ...sizeConfig }
  );
  
  // Track uploader container width for dynamic layout
  const [uploaderWidth, setUploaderWidth] = useState<number>(220);
  
  // Style options
  const [imageStyle, setImageStyle] = useState<ImageStyle | null>(null);
  const [aspectRatio, setAspectRatio] = useState<AspectRatio | null>(AspectRatio.Square);
  const [colorOption, setColorOption] = useState<ColorOption | null>(null);
  const [compositionOption, setCompositionOption] = useState<CompositionOption | null>(null);
  
  // Enhanced status state with file metadata
  const [status, setStatus] = useState<GenerationStatus>({
    isGenerating: false,
    progress: 0,
    files: [],
    error: undefined,
  });

  // Scroll to results when generation starts
  const [resultsRef, setResultsRef] = useState<HTMLDivElement | null>(null);
  
  useEffect(() => {
    if (status.isGenerating && resultsRef) {
      resultsRef.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, [status.isGenerating, resultsRef]);

  /**
   * Handle mode change between different image generation types
   * This function:
   * 1. Updates the current generation mode
   * 2. Clears previously uploaded images to prevent confusion between modes
   * 3. Resets generation results and any previous errors
   */
  const handleModeChange = (newMode: ImageGenerationMode) => {
    setMode(newMode);
    // Clear uploaded files when switching modes to prevent state inconsistency
    setUploadedFiles([]);
    setStatus({
      ...status,
      files: [],
      error: undefined,
    });
  };

  // Handle file upload
  const handleFileUpload = (files: UploadedFile[]) => {
    setUploadedFiles(files);
    console.log('Files uploaded via presigned URL:', files);
  };

  // Handle file removal
  const handleFileRemove = (index: number) => {
    const removedFile = uploadedFiles[index];
    
    // Remove from UI immediately
    setUploadedFiles(prevFiles => prevFiles.filter((_, i) => i !== index));
    
    // Record the file for cleanup later
    if (removedFile?.fileId) {
      setRemovedFileIds(prev => [...prev, removedFile.fileId]);
    }
  };

  // Handle prompt change
  const handlePromptChange = (newPrompt: string) => {
    setPrompt(newPrompt);
  };

  // Handle uploader width change
  const handleUploaderWidthChange = (width: number) => {
    setUploaderWidth(width);
  };

  // Add polling related state - optimized with useRef for reliable cleanup
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const [taskId, setTaskId] = useState<string | null>(null);
  
  // Clear polling interval on component unmount with reliable cleanup
  useEffect(() => {
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
    };
  }, []);

  // 获取用户上下文
  const { user } = useAppContext();
  
  // Initialize file cleanup hook
  const { cleanupFiles } = useFileCleanup(user?.uuid);

  /**
   * Handles the image generation process by:
   * 1. Creating a task in the backend
   * 2. Starting polling for task status
   * 3. Updating UI with progress, results, or errors
   */
  const handleGenerate = async () => {
    try {
      // Clean up files that user has removed before starting generation
      if (removedFileIds.length > 0) {
        await cleanupFiles(removedFileIds, 'removed by user');
        setRemovedFileIds([]);
      }

      // Reset previous results and set loading state
      setStatus({
        isGenerating: true,
        progress: 0,
        files: [],
        error: undefined
      });

      // Construct request object (same as before)
      const request: ImageGenerationRequest = {
        mode,
        prompt,
        negativePrompt,
        outputCount: outputCount,
        aspectRatio: aspectRatio ? aspectRatio : undefined,
        imageStyle: imageStyle || undefined,
        colorOption: colorOption || undefined,
        compositionOption: compositionOption || undefined,
        // cleanupTempImages: false, 
      };

      // Add file IDs for image processing modes
      if (mode === ImageGenerationMode.ImageToImage || 
          mode === ImageGenerationMode.BatchGeneration || 
          mode === ImageGenerationMode.ImageFusion) {
        
        if (uploadedFiles.length > 0) {
          request.fileIds = uploadedFiles.map(file => file.fileId);
          console.log('Using uploaded files:', request.fileIds);
        }
      }

      // Call the API to create a task
      const response = await fetch('/api/ai/generate-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(user?.uuid ? { 'x-user-uuid': user.uuid } : {})
        },
        body: JSON.stringify(request),
      });

      // Parse response
      const responseData = await response.json();

      // Handle error response
      if (!response.ok || responseData.code !== 0) {
        // Error handling
        setStatus({
          isGenerating: false,
          progress: 0,
          files: [],
          error: responseData.message || t('errors.generationFailed')
        });
      } else {
        // Start polling for task status
        startPolling(responseData.data.taskId);
      }
    } catch (error) {
      console.error('Generation error:', error);
      setStatus({
        isGenerating: false,
        progress: 0,
        files: [],
        error: typeof error === 'string' ? error : t('errors.generationFailed'),
      });
    }
  };

  /**
   * Start polling for task status
   * @param taskId The task ID to poll
   */
  const startPolling = (taskId: string) => {
    console.log("startPolling:", taskId);

    // Cancel any existing polling
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
    
    // Store task ID for reference
    setTaskId(taskId);
    
    // Set initial generating state
    setStatus({
      isGenerating: true,
      progress: 0,
      files: [],
      error: undefined,
    });

    // Add error tracking mechanisms
    let hasStartedPolling = false;
    let consecutiveErrorCount = 0;
    const MAX_CONSECUTIVE_ERRORS = 5;
    
    try {
      // Set up polling interval (2 seconds)
      const interval = setInterval(() => {
        // Wrap the polling logic in an async IIFE with proper error handling
        (async () => {
          try {
            hasStartedPolling = true;
            console.log("Polling task status:", taskId);
            
            const response = await fetch(`/api/ai/tasks/${taskId}`);
            console.log("Polling response status:", response.status);
            
            if (!response.ok) {
              throw new Error(`Server responded with status: ${response.status}`);
            }
            
            // Reset error count on successful response
            consecutiveErrorCount = 0;
            
            const data = await response.json();
            console.log("Task data received:", 
              JSON.stringify(data, null, 2));
            
            if (data.code === 0) {
              const task = data.data;
              
              // Update status based on task state
              setStatus(prevStatus => {
                // Create new status object
                const newStatus = {
                  isGenerating: task.status !== TaskStatus.COMPLETED && task.status !== TaskStatus.FAILED,
                  progress: task.progress || 0,
                  files: prevStatus.files, // Keep existing files until we get new ones
                  error: task.status === TaskStatus.FAILED ? task.error : undefined,
                };
                
                // Log status changes
                console.log("Task status updated:", {
                  prevGenerating: prevStatus.isGenerating,
                  newGenerating: newStatus.isGenerating,
                  prevProgress: prevStatus.progress,
                  newProgress: newStatus.progress,
                  status: task.status,
                  hasResults: !!task.results
                });
                
                return newStatus;
              });
              
              // If task is complete, fetch files and stop polling
              if (task.status === TaskStatus.COMPLETED || task.status === TaskStatus.FAILED) {
                console.log("Task completed or failed, stopping polling", {
                  status: task.status
                });
                
                // For completed tasks, extract file metadata from response
                if (task.status === TaskStatus.COMPLETED) {
                  // Extract file metadata and convert to FileInfo format
                  let fileInfos: FileInfo[] = [];
                  
                  if (task.files && task.files.length > 0) {
                    // Direct use of all returned files - no filtering needed
                    fileInfos = task.files.map((file: any) => ({
                      fileId: file.file_id,
                      fileName: file.file_name || `generated-image-${Date.now()}.png`,
                      fileSize: file.file_size || 0,
                      mimeType: file.mime_type || 'image/png',
                      category: file.file_category || 'task-output',
                      url: `/api/files/${file.file_id}/preview`
                    }));
                    
                    console.log("Extracted file info from response:", {
                      filesCount: task.files.length,
                      fileInfos
                    });
                  }
                  
                  // Update status with file metadata
                  setStatus(prevStatus => ({
                    ...prevStatus,
                    files: fileInfos,
                    isGenerating: false
                  }));
                  
                  // Notify parent components if needed
                  if (fileInfos.length > 0 && onImagesGenerated) {
                    const imageUrls = fileInfos.map(file => file.url);
                    onImagesGenerated(imageUrls);
                  }
                }
                
                clearInterval(interval);
                pollingIntervalRef.current = null;
              }
            } else {
              // API error response
              console.warn("API returned error code", data.code, data.message);
              
              // Update the status to show the error but keep polling
              setStatus(prevStatus => ({
                ...prevStatus,
                error: data.message || "Server returned error response"
              }));
            }
          } catch (error) {
            console.error('Error during polling iteration:', error);
            
            // Increment consecutive error count
            consecutiveErrorCount++;
            
            // Stop polling after too many errors
            if (consecutiveErrorCount >= MAX_CONSECUTIVE_ERRORS) {
              console.error(`Stopping polling after ${consecutiveErrorCount} consecutive errors`);
              clearInterval(interval);
              pollingIntervalRef.current = null;
              
              // Update status with error
              setStatus(prevStatus => ({
                ...prevStatus,
                isGenerating: false,
                error: `Failed to check generation status: ${error instanceof Error ? error.message : String(error)}`
              }));
            }
          }
        })();
      }, 2000);
      
      // Store interval ID with logging
      console.log("Interval ID created:", interval);
      pollingIntervalRef.current = interval;
      console.log("Polling interval stored in ref");
      
    } catch (error) {
      // Catch any errors in initial setInterval setup
      console.error('Failed to initialize polling:', error);
      setStatus(prevStatus => ({
        ...prevStatus,
        isGenerating: false,
        error: `Failed to initialize polling: ${error instanceof Error ? error.message : String(error)}`
      }));
    }
  };

  /**
   * Clears the form and results
   * Also cleans up all uploaded files since user wants to start fresh
   */
  const handleClear = async () => {
    // Collect all files that need to be cleaned up (displayed + removed)
    const allFileIds = [
      ...uploadedFiles.map(f => f.fileId).filter(Boolean),
      ...removedFileIds
    ];
    
    // Clean up all files since user wants to start fresh
    if (allFileIds.length > 0) {
      await cleanupFiles(allFileIds, 'form cleared');
    }

    // Reset all form state
    setPrompt('');
    setNegativePrompt('');
    setUploadedFiles([]);
    setRemovedFileIds([]);
    setStatus({
      isGenerating: false,
      progress: 0,
      files: [],
      error: undefined
    });
    setTaskId(null);
    
    // Clear polling interval if active
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
  };

  // Validate inputs before generation
  const isGenerationDisabled = () => {
    const hasPrompt = prompt.trim();
    const hasImages = uploadedFiles.length > 0;
    
    // Different validation rules based on mode
    switch (mode) {
      case ImageGenerationMode.TextToImage:
        return !hasPrompt || status.isGenerating || !user?.uuid;
      case ImageGenerationMode.ImageToImage:
      case ImageGenerationMode.BatchGeneration:
      case ImageGenerationMode.ImageFusion:
        return !hasPrompt || !hasImages || status.isGenerating || !user?.uuid;
      default:
        return true;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* ---------------------------- */}
      {/* <div>
        <h2 className="mb-2 text-2xl font-bold tracking-tight">{t('title')}</h2>
        <p className="text-muted-foreground">{t('description')}</p>
      </div> */}
      {/* ---------------------------- */}

      <Tabs
        defaultValue={mode}
        onValueChange={(value) => handleModeChange(value as ImageGenerationMode)}
        className="w-full"
      >
        <TabsList className="mb-4 grid w-full grid-cols-3">
          <TabsTrigger value={ImageGenerationMode.TextToImage}>
            {t('tabs.textToImage')}
          </TabsTrigger>
          <TabsTrigger value={ImageGenerationMode.ImageToImage}>
            {t('tabs.imageToImage')}
          </TabsTrigger>
          <TabsTrigger value={ImageGenerationMode.ImageFusion}>
            {t('tabs.imageFusion')}
          </TabsTrigger>
        </TabsList>

        {/* Text to Image */}
        <TabsContent value={ImageGenerationMode.TextToImage} className="space-y-4">
          <PromptInput
            value={prompt}
            onChange={handlePromptChange}
            placeholder={t('promptPlaceholder')}
            disabled={status.isGenerating}
            maxLength={MAX_PROMPT_LENGTH}
            style={{
              height: `${currentSizeConfig.inputHeight}px`,
              minHeight: `${currentSizeConfig.minInputHeight}px`,
              maxHeight: `${currentSizeConfig.maxInputHeight}px`
            }}
            textareaStyle={{
              height: '100%',
            }}
          />
          
          <div className="flex flex-wrap items-center justify-between gap-3">
            <StyleSelector
              onStyleChange={setImageStyle}
              onAspectRatioChange={setAspectRatio}
              onColorOptionChange={setColorOption}
              onCompositionChange={setCompositionOption}
              className="flex-grow"
              // style={{ height: currentSizeConfig.inputHeight }}
            />
            
            <div className="flex items-center gap-2">
              <span className="text-sm">{t('outputCountLabel')}</span>
              <Select
                value={outputCount.toString()}
                onValueChange={(value) => setOutputCount(Number(value))}
              >
                <SelectTrigger className="w-16">
                  <SelectValue placeholder="Images" />
                </SelectTrigger>
                <SelectContent className="min-w-[4rem] max-w-[4rem]" style={{ width: "4rem" }}>
                  {[1, 2, 3, 4].map((count) => (
                    <SelectItem key={count} value={count.toString()}>
                      {count}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="flex items-center justify-end gap-3">
            <Button variant="outline" onClick={handleClear} disabled={status.isGenerating}>
              {t('clearButton')}
            </Button>
            <Button onClick={handleGenerate} disabled={status.isGenerating || !prompt.trim()}>
              {status.isGenerating ? (
                <span className="flex items-center gap-2">
                  <svg
                    className="h-4 w-4 animate-spin"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  {t('generatingStatus')}
                </span>
              ) : (
                t('generateButton')
              )}
            </Button>
          </div>
        </TabsContent>

        {/* Image to Image */}
        <TabsContent value={ImageGenerationMode.ImageToImage} className="space-y-4">
          {/* Grid layout container with fixed square uploader */}
          <div 
            className="grid lg:grid-cols-[220px_1fr] md:grid-cols-[200px_1fr] grid-cols-1 gap-4 rounded-lg border border-border p-4"
            style={{ minHeight: `${currentSizeConfig.inputHeight}px` }}
          >
            {/* Image uploader area - exact 220px square */}
            <div 
              className="flex-shrink-0 w-[220px] h-[220px] lg:block hidden"
              style={{ 
                width: '220px',
                height: '220px'
              }}
            >
              <ImageUploader 
                onFileUpload={handleFileUpload}
                uploadedFiles={uploadedFiles}
                onFileRemove={handleFileRemove}
                maxImages={1}
                className="h-full w-full"
                parentHeight={220}
                userUuid={user?.uuid}
              />
            </div>
            
            {/* Image uploader area for small screens */}
            <div className="w-full h-40 sm:h-48 lg:hidden">
              <ImageUploader 
                onFileUpload={handleFileUpload}
                uploadedFiles={uploadedFiles}
                onFileRemove={handleFileRemove}
                maxImages={1}
                className="h-full w-full"
                parentHeight={192}
                userUuid={user?.uuid}
              />
            </div>
            
            {/* Text input area - flexible width */}
            <div className="min-w-0">
              <PromptInput
                value={prompt}
                onChange={handlePromptChange}
                placeholder={t('promptPlaceholderImg2Img')}
                disabled={status.isGenerating}
                maxLength={MAX_PROMPT_LENGTH}
                className="h-full"
                style={{
                  height: `${currentSizeConfig.inputHeight}px`,
                  minHeight: `${currentSizeConfig.minInputHeight}px`,
                  maxHeight: `${currentSizeConfig.maxInputHeight}px`
                }}
                textareaStyle={{
                  height: '100%',
                  resize: 'none'
                }}
              />
            </div>
          </div>
          
          {/* Style selection options in a row beneath the input */}
          <div className="flex flex-wrap items-center justify-between gap-3">
            <StyleSelector
              onStyleChange={setImageStyle}
              onAspectRatioChange={setAspectRatio}
              onColorOptionChange={setColorOption}
              onCompositionChange={setCompositionOption}
              className="flex-grow"
            />
            
            <div className="flex items-center gap-2">
              <span className="text-sm">{t('outputCountLabel')}</span>
              <Select
                value={outputCount.toString()}
                onValueChange={(value) => setOutputCount(Number(value))}
              >
                <SelectTrigger className="w-16">
                  <SelectValue placeholder="Images" />
                </SelectTrigger>
                <SelectContent className="min-w-[4rem] max-w-[4rem]" style={{ width: "4rem" }}>
                  {[1, 2, 3, 4].map((count) => (
                    <SelectItem key={count} value={count.toString()}>
                      {count}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          {/* Action buttons */}
          <div className="flex items-center justify-end gap-3">
            <Button variant="outline" onClick={handleClear} disabled={status.isGenerating}>
              {t('clearButton')}
            </Button>
            <Button
              onClick={handleGenerate}
              disabled={status.isGenerating || !prompt.trim() || uploadedFiles.length === 0}
            >
              {status.isGenerating ? (
                <span className="flex items-center gap-2">
                  <svg
                    className="h-4 w-4 animate-spin"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  {t('generatingStatus')}
                </span>
              ) : (
                t('generateButton')
              )}
            </Button>
          </div>
        </TabsContent>

        {/* Image Fusion */}
        <TabsContent value={ImageGenerationMode.ImageFusion} className="space-y-4">
          {/* Dynamic layout container */}
          <div 
            className="flex lg:flex-row flex-col gap-4 rounded-lg border border-border p-4"
            style={{ minHeight: `${currentSizeConfig.inputHeight}px` }}
          >
            {/* Image uploader area - dynamic width based on content */}
            <div 
              className="flex-shrink-0 lg:block hidden"
              style={{ 
                width: `${uploaderWidth}px`,
                height: '220px'
              }}
            >
              <ImageUploader 
                onFileUpload={handleFileUpload}
                uploadedFiles={uploadedFiles}
                onFileRemove={handleFileRemove}
                maxImages={5}
                className="h-full w-full"
                parentHeight={220}
                userUuid={user?.uuid}
                onContainerWidthChange={handleUploaderWidthChange}
              />
            </div>
            
            {/* Image uploader area for small screens */}
            <div className="w-full h-48 sm:h-56 lg:hidden">
              <ImageUploader 
                onFileUpload={handleFileUpload}
                uploadedFiles={uploadedFiles}
                onFileRemove={handleFileRemove}
                maxImages={5}
                className="h-full w-full"
                parentHeight={224}
                userUuid={user?.uuid}
                onContainerWidthChange={handleUploaderWidthChange}
              />
            </div>
            
            {/* Text input area - flexible width */}
            <div className="flex-1 min-w-0">
              <PromptInput
                value={prompt}
                onChange={handlePromptChange}
                placeholder={t('promptPlaceholderImg2Img')}
                disabled={status.isGenerating}
                maxLength={MAX_PROMPT_LENGTH}
                className="h-full"
                style={{
                  height: `${currentSizeConfig.inputHeight}px`,
                  minHeight: `${currentSizeConfig.minInputHeight}px`,
                  maxHeight: `${currentSizeConfig.maxInputHeight}px`
                }}
                textareaStyle={{
                  height: '100%',
                  resize: 'none'
                }}
              />
            </div>
          </div>
          
          {/* Style selection options in a row beneath the input */}
          <div className="flex flex-wrap items-center justify-between gap-3">
            <StyleSelector
              onStyleChange={setImageStyle}
              onAspectRatioChange={setAspectRatio}
              onColorOptionChange={setColorOption}
              onCompositionChange={setCompositionOption}
              className="flex-grow"
            />
            
            <div className="flex items-center gap-2">
              <span className="text-sm">{t('outputCountLabel')}</span>
              <Select
                value={outputCount.toString()}
                onValueChange={(value) => setOutputCount(Number(value))}
              >
                <SelectTrigger className="w-16">
                  <SelectValue placeholder="Images" />
                </SelectTrigger>
                <SelectContent className="min-w-[4rem] max-w-[4rem]" style={{ width: "4rem" }}>
                  {[1, 2, 3, 4].map((count) => (
                    <SelectItem key={count} value={count.toString()}>
                      {count}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          {/* Action buttons */}
          <div className="flex items-center justify-end gap-3">
            <Button variant="outline" onClick={handleClear} disabled={status.isGenerating}>
              {t('clearButton')}
            </Button>
            <Button
              onClick={handleGenerate}
              disabled={status.isGenerating || !prompt.trim() || uploadedFiles.length === 0}
            >
              {status.isGenerating ? (
                <span className="flex items-center gap-2">
                  <svg
                    className="h-4 w-4 animate-spin"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  {t('generatingStatus')}
                </span>
              ) : (
                t('generateButton')
              )}
            </Button>
          </div>
        </TabsContent>
      </Tabs>

      {/* Results Section */}
      <div ref={setResultsRef}>
        <GenerationResults 
          status={status} 
          className="mt-8" 
        />
      </div>
    </div>
  );
}
