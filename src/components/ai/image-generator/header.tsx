"use client";

import { useTranslations } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import Icon from "@/components/icon";

interface ImageGeneratorHeaderProps {
  showSubtitle?: boolean;
}

export default function ImageGeneratorHeader({
  showSubtitle = true,
}: ImageGeneratorHeaderProps) {
  const t = useTranslations('components.image_generator.block');
  
  // Define highlight text for title (leave empty if no highlight needed)
  // This text must be part of the title in the translation file
  const titleHighlight = "AI";
  
  // Get title from translation
  const title = t('title');
  let texts = null;
  if (titleHighlight && title.includes(titleHighlight)) {
    texts = title.split(titleHighlight, 2);
  }

  // ------------------标题高亮、按钮文本和按钮链接没有实现国际化------------------

  // Control buttons visibility - set to false to hide
  const showPrimaryButton = true;   // Set to false to hide primary button
  const showSecondaryButton = true; // Set to false to hide secondary button
  
  // Configure buttons
  // Primary button (filled style)
  const primaryButton = {
    title: "Start Now",
    url: "/get-started",
    target: "", // Optional: "_blank" for opening in new tab
    icon: "lightning" // Optional: icon name from your icon component
  };
  
  // Secondary button (outline style)
  const secondaryButton = {
    title: "加入 Discord",
    url: "https://discord.com/invite/yourlink",
    target: "_blank",
    icon: "" // Leave empty if no icon needed
  };

  return (
    <div className="text-center">
      {/* Title with optional highlighted text */}
      {texts && texts.length > 1 ? (
        <h2 className="mx-auto mb-8 max-w-3xl text-balance text-3xl font-bold lg:text-5xl">
          {texts[0]}
          <span className="bg-gradient-to-r from-primary via-primary to-primary bg-clip-text text-transparent">
            {titleHighlight}
          </span>
          {texts[1]}
        </h2>
      ) : (
        <h2 className="mx-auto mb-8 max-w-3xl text-balance text-3xl font-bold lg:text-5xl">
          {title}
        </h2>
      )}
      
      {/* Main description */}
      <p className="mx-auto mb-12 max-w-3xl text-center text-muted-foreground lg:text-xl">
        {t('description')}
      </p>
      
      {/* Optional subtitle */}
      {showSubtitle && t.has('subtitle') && (
        <p className="mx-auto mb-8 max-w-2xl text-center text-lg text-muted-foreground">
          {t('subtitle')}
        </p>
      )}

      {/* Buttons section - controlled by showPrimaryButton and showSecondaryButton variables */}
      {((showPrimaryButton && primaryButton) || (showSecondaryButton && secondaryButton)) && (
        <div className="mt-8 flex flex-col justify-center gap-4 sm:flex-row">
          {showPrimaryButton && primaryButton && (
            <Link
              href={primaryButton.url || ""}
              target={primaryButton.target || ""}
              className="flex items-center"
            >
              <Button
                className="w-full"
                size="lg"
                variant="default"
              >
                {primaryButton.title}
                {primaryButton.icon && (
                  <Icon name={primaryButton.icon} className="ml-1" />
                )}
              </Button>
            </Link>
          )}
          
          {showSecondaryButton && secondaryButton && (
            <Link
              href={secondaryButton.url || ""}
              target={secondaryButton.target || ""}
              className="flex items-center"
            >
              <Button
                className="w-full"
                size="lg"
                variant="outline"
              >
                {secondaryButton.title}
                {secondaryButton.icon && (
                  <Icon name={secondaryButton.icon} className="ml-1" />
                )}
              </Button>
            </Link>
          )}
        </div>
      )}
    </div>
  );
}
