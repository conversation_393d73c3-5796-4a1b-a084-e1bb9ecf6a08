import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { Textarea } from '@/components/ui/textarea';

interface PromptInputProps {
  /**
   * Current prompt value
   */
  value: string;
  
  /**
   * Callback when prompt changes
   */
  onChange: (value: string) => void;
  
  /**
   * Placeholder text
   */
  placeholder?: string;
  
  /**
   * Whether the input is disabled
   */
  disabled?: boolean;
  
  /**
   * Maximum length of prompt (0 = no limit)
   */
  maxLength?: number;
  
  /**
   * Additional CSS classes for the container
   */
  className?: string;
  
  /**
   * Additional CSS classes for the textarea element
   */
  textareaClassName?: string;

  /**
   * Custom inline styles for the container
   */
  style?: React.CSSProperties;

  /**
   * Custom inline styles for the textarea element
   */
  textareaStyle?: React.CSSProperties;
}

/**
 * A reusable prompt input component for AI image generation
 * Includes character count and styling
 * Height increased by 50% from original size to provide more space for writing
 * Supports custom styling through className props
 */
export default function PromptInput({
  value,
  onChange,
  placeholder,
  disabled = false,
  maxLength = 0,
  className = '',
  textareaClassName = '',
  style,
  textareaStyle,
}: PromptInputProps) {
  const t = useTranslations('components.image_generator.core');
  
  // Track character count
  const [charCount, setCharCount] = useState(0);
  
  // Update character count when value changes
  useEffect(() => {
    setCharCount(value.length);
  }, [value]);
  
  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    
    // Apply character limit if specified
    if (maxLength > 0 && newValue.length > maxLength) {
      return;
    }
    
    onChange(newValue);
  };
  
  // Show character warning if approaching limit
  const isApproachingLimit = maxLength > 0 && charCount > maxLength * 0.8;
  const isAtLimit = maxLength > 0 && charCount >= maxLength;
  
  return (
    <div className={`w-full space-y-1 ${className}`} style={style}>
      <Textarea
        value={value}
        onChange={handleChange}
        placeholder={placeholder || t('promptPlaceholder')}
        disabled={disabled}
        className={`min-h-48 resize-none transition-colors ${textareaClassName} ${
          isAtLimit ? 'border-destructive' : 
          isApproachingLimit ? 'border-warning' : ''
        }`}
        style={textareaStyle}
      />
      
      {/* Character counter */}
      {/* {maxLength > 0 && (
        <div className="flex justify-end">
          <span 
            className={`text-xs ${
              isAtLimit ? 'text-destructive' : 
              isApproachingLimit ? 'text-warning' : 'text-muted-foreground'
            }`}
          >
            {charCount}/{maxLength}
          </span>
        </div>
      )} */}
    </div>
  );
}
