"use client";

import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { AspectRatio, ColorOption, CompositionOption, ImageStyle } from '@/types/ai/image-gen-types';

interface StyleOption<T> {
  value: T;
  label: string;
}

interface StyleSelectorProps {
  onStyleChange: (style: ImageStyle | null) => void;
  onAspectRatioChange: (ratio: AspectRatio | null) => void;
  onColorOptionChange: (color: ColorOption | null) => void;
  onCompositionChange: (composition: CompositionOption | null) => void;
  className?: string;
  /**
   * Custom inline styles for the container
   */
  style?: React.CSSProperties;
}

/**
 * Component for selecting image generation style options
 * Provides dropdown selectors for style, aspect ratio, color, and composition
 * Supports flexible layout and can be centered for different UI layouts
 */
export default function StyleSelector({
  onStyleChange,
  onAspectRatioChange,
  onColorOptionChange,
  onCompositionChange,
  className,
  style,
}: StyleSelectorProps) {
  const t = useTranslations('components.image_generator.core');
  const [isStyleDropdownOpen, setIsStyleDropdownOpen] = useState(false);
  const [isAspectRatioDropdownOpen, setIsAspectRatioDropdownOpen] = useState(false);
  const [isColorDropdownOpen, setIsColorDropdownOpen] = useState(false);
  const [isCompositionDropdownOpen, setIsCompositionDropdownOpen] = useState(false);
  
  const [selectedStyle, setSelectedStyle] = useState<ImageStyle | null>(null);
  const [selectedAspectRatio, setSelectedAspectRatio] = useState<AspectRatio | null>(null);
  const [selectedColor, setSelectedColor] = useState<ColorOption | null>(null);
  const [selectedComposition, setSelectedComposition] = useState<CompositionOption | null>(null);

  // 获取枚举值对应的枚举名称
  const getEnumKeyByValue = <T extends Record<string, string>>(enumObj: T, value: string): keyof T | undefined => {
    return Object.keys(enumObj).find(key => enumObj[key as keyof T] === value) as keyof T | undefined;
  };

  // Generate style options from enums and translations
  const styleOptions: StyleOption<ImageStyle>[] = Object.values(ImageStyle).map(
    (style) => {
      const enumKey = getEnumKeyByValue(ImageStyle, style);
      return {
        value: style,
        label: enumKey ? t(`styleOptions.${enumKey}`) : style,
      };
    }
  );

  const aspectRatioOptions: StyleOption<AspectRatio>[] = Object.values(AspectRatio).map(
    (ratio) => {
      const enumKey = getEnumKeyByValue(AspectRatio, ratio);
      return {
        value: ratio,
        label: enumKey ? t(`aspectRatio.${enumKey}`) : ratio,
      };
    }
  );

  const colorOptions: StyleOption<ColorOption>[] = Object.values(ColorOption).map(
    (color) => {
      const enumKey = getEnumKeyByValue(ColorOption, color);
      return {
        value: color,
        label: enumKey ? t(`colorOptions.${enumKey}`) : color,
      };
    }
  );

  const compositionOptions: StyleOption<CompositionOption>[] = Object.values(CompositionOption).map(
    (composition) => {
      const enumKey = getEnumKeyByValue(CompositionOption, composition);
      return {
        value: composition,
        label: enumKey ? t(`compositionOptions.${enumKey}`) : composition,
      };
    }
  );

  // Handle style selection
  const handleStyleSelect = (style: ImageStyle | null) => {
    setSelectedStyle(style);
    onStyleChange(style);
    setIsStyleDropdownOpen(false);
  };

  // Handle aspect ratio selection
  const handleAspectRatioSelect = (ratio: AspectRatio | null) => {
    setSelectedAspectRatio(ratio);
    onAspectRatioChange(ratio);
    setIsAspectRatioDropdownOpen(false);
  };

  // Handle color selection
  const handleColorSelect = (color: ColorOption | null) => {
    setSelectedColor(color);
    onColorOptionChange(color);
    setIsColorDropdownOpen(false);
  };

  // Handle composition selection
  const handleCompositionSelect = (composition: CompositionOption | null) => {
    setSelectedComposition(composition);
    onCompositionChange(composition);
    setIsCompositionDropdownOpen(false);
  };

  return (
    <div className={`flex flex-wrap items-center gap-3 ${className}`} style={style}>
      {/* Style Selector */}
      <div className="relative">
        <button
          className="flex items-center gap-1.5 rounded-md border px-3 py-1.5 text-sm transition-colors hover:bg-accent"
          onClick={() => setIsStyleDropdownOpen(!isStyleDropdownOpen)}
        >
          <span className="flex h-5 w-5 items-center justify-center">
            {selectedStyle ? '🎨' : '⚪️'}
          </span>
          <span>
            {selectedStyle
              ? t(`styleOptions.${getEnumKeyByValue(ImageStyle, selectedStyle) || ''}`)
              : t(`styleOptions.NoStyle`)}
          </span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d={isStyleDropdownOpen ? 'M5 15l7-7 7 7' : 'M19 9l-7 7-7-7'}
            />
          </svg>
        </button>

        {isStyleDropdownOpen && (
          <div className="absolute left-0 top-full z-10 mt-1 w-48 rounded-md border bg-popover p-1 shadow-md">
            {styleOptions.map((option) => (
              <button
                key={option.value}
                className={`flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-left text-sm hover:bg-accent ${
                  selectedStyle === option.value ? 'bg-accent/50' : ''
                }`}
                onClick={() => handleStyleSelect(option.value)}
              >
                {option.label}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Aspect Ratio Selector */}
      <div className="relative">
        <button
          className="flex items-center gap-1.5 rounded-md border px-3 py-1.5 text-sm transition-colors hover:bg-accent"
          onClick={() => setIsAspectRatioDropdownOpen(!isAspectRatioDropdownOpen)}
        >
          <span className="flex h-5 w-5 items-center justify-center">
            {selectedAspectRatio ? '📐' : '⚪️'}
          </span>
          <span>
            {selectedAspectRatio
              ? t(`aspectRatio.${getEnumKeyByValue(AspectRatio, selectedAspectRatio) || ''}`)
              : t(`aspectRatio.Square`)}
          </span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d={isAspectRatioDropdownOpen ? 'M5 15l7-7 7 7' : 'M19 9l-7 7-7-7'}
            />
          </svg>
        </button>

        {isAspectRatioDropdownOpen && (
          <div className="absolute left-0 top-full z-10 mt-1 w-40 rounded-md border bg-popover p-1 shadow-md">
            {aspectRatioOptions.map((option) => (
              <button
                key={option.value}
                className={`flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-left text-sm hover:bg-accent ${
                  selectedAspectRatio === option.value ? 'bg-accent/50' : ''
                }`}
                onClick={() => handleAspectRatioSelect(option.value)}
              >
                {option.label}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Color Selector */}
      <div className="relative">
        <button
          className="flex items-center gap-1.5 rounded-md border px-3 py-1.5 text-sm transition-colors hover:bg-accent"
          onClick={() => setIsColorDropdownOpen(!isColorDropdownOpen)}
        >
          <span className="flex h-5 w-5 items-center justify-center">
            {selectedColor ? '🎨' : '⚪️'}
          </span>
          <span>
            {selectedColor
              ? t(`colorOptions.${getEnumKeyByValue(ColorOption, selectedColor) || ''}`)
              : t(`colorOptions.NoColor`)}
          </span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d={isColorDropdownOpen ? 'M5 15l7-7 7 7' : 'M19 9l-7 7-7-7'}
            />
          </svg>
        </button>

        {isColorDropdownOpen && (
          <div className="absolute left-0 top-full z-10 mt-1 w-40 rounded-md border bg-popover p-1 shadow-md">
            {colorOptions.map((option) => (
              <button
                key={option.value}
                className={`flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-left text-sm hover:bg-accent ${
                  selectedColor === option.value ? 'bg-accent/50' : ''
                }`}
                onClick={() => handleColorSelect(option.value)}
              >
                {option.label}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Composition Selector */}
      <div className="relative">
        <button
          className="flex items-center gap-1.5 rounded-md border px-3 py-1.5 text-sm transition-colors hover:bg-accent"
          onClick={() => setIsCompositionDropdownOpen(!isCompositionDropdownOpen)}
        >
          <span className="flex h-5 w-5 items-center justify-center">
            {selectedComposition ? '🖼️' : '⚪️'}
          </span>
          <span>
            {selectedComposition
              ? t(`compositionOptions.${getEnumKeyByValue(CompositionOption, selectedComposition) || ''}`)
              : t(`compositionOptions.NoComposition`)}
          </span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d={isCompositionDropdownOpen ? 'M5 15l7-7 7 7' : 'M19 9l-7 7-7-7'}
            />
          </svg>
        </button>

        {isCompositionDropdownOpen && (
          <div className="absolute left-0 top-full z-10 mt-1 w-40 rounded-md border bg-popover p-1 shadow-md">
            {compositionOptions.map((option) => (
              <button
                key={option.value}
                className={`flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-left text-sm hover:bg-accent ${
                  selectedComposition === option.value ? 'bg-accent/50' : ''
                }`}
                onClick={() => handleCompositionSelect(option.value)}
              >
                {option.label}
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
} 