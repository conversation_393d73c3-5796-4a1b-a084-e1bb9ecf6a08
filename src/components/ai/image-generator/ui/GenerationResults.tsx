"use client";

import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { GenerationStatus } from '@/types/ai/common';
import { FileInfo } from '@/types/file';
import Image from 'next/image';
import { Loader2, Download, ExternalLink } from 'lucide-react';
import { useImageDownload } from '@/hooks/useDownload';

interface GenerationResultsProps {
  status: GenerationStatus;
  className?: string;
}

/**
 * Component for displaying image generation results.
 * It renders a progress indicator during generation and an image grid with action buttons after completion.
 */
export default function GenerationResults({ 
  status, 
  className 
}: GenerationResultsProps) {
  const t = useTranslations('components.image_generator.core');
  const [imageLoadStatus, setImageLoadStatus] = useState<Record<number, 'loading' | 'success' | 'error'>>({});
  
  const { 
    downloadImage, 
    isLoading: isDownloading, 
    error: downloadError, 
  } = useImageDownload();

  const handleDownloadImage = async (file: FileInfo | undefined) => {
    if (!file) return;
    try {
      await downloadImage(file.fileId, file.fileName);
    } catch (error) {
      console.error('Image download failed:', error);
    }
  };

  const handleViewOriginalImage = (imageUrl: string) => {
    window.open(imageUrl, '_blank');
  };

  useEffect(() => {
    if (status.files.length > 0) {
      const initialLoadStatus: Record<number, 'loading' | 'success' | 'error'> = {};
      status.files.forEach((_, index) => {
        initialLoadStatus[index] = 'loading';
      });
      setImageLoadStatus(initialLoadStatus);
    }
  }, [status.files]);
  
  if (!status.isGenerating && status.files.length === 0) {
    return null;
  }

  const handleImageLoad = (index: number) => {
    setImageLoadStatus(prev => ({ ...prev, [index]: 'success' }));
  };

  const handleImageError = (index: number) => {
    setImageLoadStatus(prev => ({ ...prev, [index]: 'error' }));
  };

  if (status.isGenerating) {
    return (
      <div className={`rounded-md border p-4 ${className}`}>
        <div className="flex flex-col gap-4">
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>{t('generating')}</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
            <div 
              className="bg-blue-600 h-2.5 rounded-full dark:bg-blue-500 transition-all duration-300" 
              style={{ width: `${status.progress}%` }}
            ></div>
          </div>
          <p className="text-sm text-muted-foreground">
            {status.progress}% {t('complete')}
          </p>
        </div>
      </div>
    );
  }
  
  if (status.error) {
    return (
      <div className={`rounded-md border border-red-200 bg-red-50 p-4 ${className}`}>
        <div className="flex items-center gap-2 text-red-600">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
          <span>{t('generationFailed')}</span>
        </div>
        <p className="mt-2 text-sm text-red-600">{status.error}</p>
      </div>
    );
  }
  
  if (status.files && status.files.length > 0) {
    const getGridColumns = () => {
      const count = status.files.length;
      if (count === 1) return "w-full";
      if (count >= 2 && count <= 4) return "grid grid-cols-1 sm:grid-cols-2";
      return "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3";
    };

    return (
      <div className={`space-y-6 ${className}`}>
        <h3 className="text-lg font-medium">{t('generatedImages')}</h3>
        <div className={`gap-4 ${getGridColumns()}`}>
          {status.files.map((file, index) => (
            <div 
              key={file.fileId || index} 
              className={`relative overflow-hidden rounded-lg border ${
                status.files.length === 1 ? "w-full max-w-xl mx-auto" : ""
              }`}
            >
              <div className={`relative ${status.files.length === 1 ? "aspect-auto h-auto" : "aspect-square"}`}>
                <Image
                  src={file.url}
                  alt={`Generated image ${index + 1}`}
                  fill={status.files.length !== 1}
                  width={status.files.length === 1 ? 800 : undefined}
                  height={status.files.length === 1 ? 600 : undefined}
                  className={`${status.files.length === 1 ? "w-full h-auto" : "object-cover"}`}
                  unoptimized={true}
                  onLoad={() => handleImageLoad(index)}
                  onError={() => handleImageError(index)}
                />
              </div>
              
              {downloadError && (
                <div className="absolute top-2 left-2 right-2">
                  <div className="bg-red-500 bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                    {downloadError}
                  </div>
                </div>
              )}
              
              <div className="bg-background/80 backdrop-blur-sm absolute bottom-0 left-0 right-0 p-2 flex justify-between">
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleDownloadImage(file)}
                  title={t('downloadImage')}
                  disabled={isDownloading}
                >
                  {isDownloading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Download className="h-4 w-4" />
                  )}
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleViewOriginalImage(file.url)}
                  title={t('viewOriginal')}
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }
  
  return null;
}
