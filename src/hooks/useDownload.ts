import { useState, useCallback } from 'react';
import { clientDownloadService, type DownloadResult } from '@/lib/client-download-service';

interface DownloadState {
  isLoading: boolean;
  error: string | null;
  progress: number;
}

/**
 * Enhanced download hook using extracted client download service
 * Reuses the proven download logic from DownloadService with blob strategy
 * Ensures files are downloaded instead of opened in browser
 */
export function useDownload() {
  const [state, setState] = useState<DownloadState>({
    isLoading: false,
    error: null,
    progress: 0,
  });

  /**
   * Download a single file using the client download service
   * Uses blob strategy to ensure proper download behavior
   */
  const downloadFile = useCallback(async (file_id: string, custom_filename?: string) => {
    setState({ isLoading: true, error: null, progress: 0 });

    try {
      // Fetch the presigned download URL from our new secure endpoint
      const response = await fetch(`/api/files/${file_id}/download`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Error ${response.status}`);
      }
      
      const { downloadUrl } = await response.json();

      if (!downloadUrl) {
        throw new Error('Failed to retrieve a valid download URL.');
      }

      // Use client download service with the presigned URL
      const result: DownloadResult = await clientDownloadService.downloadFile({
        url: downloadUrl,
        customFilename: custom_filename || `${file_id}.zip`, // Provide a fallback filename
        onProgress: (progress) => {
          setState(prev => ({ ...prev, progress }));
        },
        strategy: 'blob' // Force blob strategy for better download experience
      });

      if (!result.success) {
        throw new Error(result.error || 'Download failed in client service');
      }

      setState({ isLoading: false, error: null, progress: 100 });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      setState({ isLoading: false, error: errorMessage, progress: 0 });
      console.error('Download failed:', error);
    }
  }, []);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    downloadFile,
    isLoading: state.isLoading,
    error: state.error,
    progress: state.progress,
    clearError,
  };
}

/**
 * Specialized hook for image downloads
 */
export function useImageDownload() {
  const { downloadFile, ...rest } = useDownload();
  
  const downloadImage = useCallback((file_id: string, custom_filename?: string) => {
    return downloadFile(file_id, custom_filename);
  }, [downloadFile]);

  return {
    ...rest,
    downloadImage,
  };
}

/**
 * Specialized hook for video downloads
 */
export function useVideoDownload() {
  const { downloadFile, ...rest } = useDownload();
  
  const downloadVideo = useCallback((file_id: string, custom_filename?: string) => {
    return downloadFile(file_id, custom_filename);
  }, [downloadFile]);

  return {
    ...rest,
    downloadVideo,
  };
}
