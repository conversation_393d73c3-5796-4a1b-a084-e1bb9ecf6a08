import { useCallback } from 'react';

/**
 * Custom hook for managing file cleanup operations
 * Provides a unified interface for cleaning up files from both database and cloud storage
 * Used when users remove files or clear the entire form
 */
export function useFileCleanup(userUuid?: string) {
  /**
   * Clean up specified files from both database and cloud storage
   * @param fileIds Array of file IDs to clean up
   * @param reason Optional reason for cleanup (used for logging)
   * @returns Promise<boolean> indicating success/failure
   */
  const cleanupFiles = useCallback(async (
    fileIds: string[], 
    reason?: string
  ): Promise<boolean> => {
    // Early return if no files to clean up or no user authentication
    if (fileIds.length === 0 || !userUuid) {
      return true;
    }
    
    try {
      const response = await fetch('/api/files/upload/cleanup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-uuid': userUuid,
        },
        body: JSON.stringify({ fileIds }),
      });
      
      if (response.ok) {
        console.log(`Successfully cleaned up ${fileIds.length} files${reason ? ` (${reason})` : ''}`);
        return true;
      } else {
        console.warn(`Failed to cleanup ${fileIds.length} files${reason ? ` (${reason})` : ''}`);
        return false;
      }
    } catch (error) {
      console.warn(`Error cleaning up files${reason ? ` (${reason})` : ''}:`, error);
      return false;
    }
  }, [userUuid]);

  return { cleanupFiles };
} 