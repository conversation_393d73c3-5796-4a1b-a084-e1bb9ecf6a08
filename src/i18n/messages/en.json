{"metadata": {"title": "Ship Any AI SaaS Startups in hours | ShipAny", "description": "ShipAny is a NextJS boilerplate for building AI SaaS startups, Ship Fast with a variety of templates and components.", "keywords": "ShipAny, AI SaaS Boilerplate, NextJS Boilerplate"}, "user": {"sign_in": "Sign In", "sign_out": "Sign Out", "credits": "Credits", "api_keys": "API Keys", "my_orders": "My Orders", "user_center": "User Center", "admin_system": "Admin System"}, "sign_modal": {"sign_in_title": "Sign In", "sign_in_description": "Sign in to your account", "sign_up_title": "Sign Up", "sign_up_description": "Create an account", "email_title": "Email", "email_placeholder": "Input your email here", "password_title": "Password", "password_placeholder": "Input your password here", "forgot_password": "Forgot password?", "or": "Or", "continue": "Continue", "no_account": "Don't have an account?", "email_sign_in": "Sign in with <PERSON><PERSON>", "google_sign_in": "Sign in with Google", "github_sign_in": "Sign in with GitHub", "close_title": "Close", "cancel_title": "Cancel"}, "my_orders": {"title": "My Orders", "description": "orders paid with ShipAny.", "no_orders": "No orders found", "tip": "", "activate_order": "Activate Order", "actived": "Activated", "join_discord": "Join <PERSON>", "read_docs": "Read Docs", "table": {"order_no": "Order No", "email": "Email", "product_name": "Product Name", "amount": "Amount", "paid_at": "<PERSON><PERSON>", "github_username": "GitHub Username", "status": "Status"}}, "my_credits": {"title": "My Credits", "left_tip": "left credits: {left_credits}", "no_credits": "No credits records", "recharge": "Recharge", "table": {"trans_no": "Trans No", "trans_type": "Trans Type", "credits": "Credits", "updated_at": "Updated At", "status": "Status"}}, "api_keys": {"title": "API Keys", "tip": "Please keep your apikey safe to avoid leaks", "no_api_keys": "No API Keys", "create_api_key": "Create API Key", "table": {"name": "Name", "key": "Key", "created_at": "Created At"}, "form": {"name": "Name", "name_placeholder": "API Key Name", "submit": "Submit"}}, "blogs": {"title": "Blogs", "description": "News, resources, and updates about ShipAny", "read_more_text": "Read More"}, "products": {"title": "Products", "description": "Explore our products", "no_products": "No products found."}, "case_studies": {"title": "Case Studies", "description": "Explore our case studies", "no_case_studies": "No case studies found."}, "my_invites": {"title": "My Invites", "description": "View your invite records", "no_invites": "No invite records found", "my_invite_link": "My Invite Link", "edit_invite_link": "Edit Invite <PERSON>", "copy_invite_link": "Copy Invite Link", "invite_code": "Invite Code", "invite_tip": "Invite 1 friend to buy ShipAny, reward $50.", "invite_balance": "In<PERSON>te <PERSON>", "total_invite_count": "Total Invite Count", "total_paid_count": "Total Paid Count", "total_award_amount": "Total Award Amount", "update_invite_code": "Set Invite Code", "update_invite_code_tip": "Input your custom invite code", "update_invite_button": "Save", "no_orders": "You can't invite others before you bought ShipAny", "no_affiliates": "You're not allowed to invite others, please contact us to apply for permission.", "table": {"invite_time": "Invite Time", "invite_user": "Invite User", "status": "Status", "reward_percent": "<PERSON><PERSON> Percent", "reward_amount": "<PERSON><PERSON> Amount", "pending": "Pending", "completed": "Completed"}}, "feedback": {"title": "<PERSON><PERSON><PERSON>", "description": "We'd love to hear what went well or how we can improve the product experience.", "submit": "Submit", "loading": "Submitting...", "contact_tip": "Other ways to contact us", "rating_tip": "How do you feel about <PERSON><PERSON><PERSON>?", "placeholder": "Leave your words here..."}, "components": {"image_generator": {"block": {"title": "AI Image Generator", "description": "Create stunning AI-generated images with our powerful tool", "subtitle": "Transform your ideas into visual art with advanced AI technology"}, "core": {"title": "AI Image Generator", "description": "Generate images with AI using text prompts or modify existing images", "tabs": {"textToImage": "Text to Image", "imageToImage": "Image to Image", "batchGeneration": "Batch Generation", "imageFusion": "Image Fusion"}, "promptPlaceholder": "Describe the image you want", "promptPlaceholderImg2Img": "Describe how to modify this image", "uploadButton": "Upload", "generateButton": "Generate", "clearButton": "Clear", "outputCountLabel": "Count:", "images": "images", "generatedImages": "Generated Images", "downloadImage": "Download Image", "viewOriginal": "View Original", "promptInput": {"characterLimit": "Character limit reached", "charactersRemaining": "characters remaining", "suggestions": "Suggestions", "history": "History"}, "styleOptions": {"NoStyle": "No Style", "SnoopyStyle": "Snoopy Style", "IrasutoyaIllustrations": "Irasutoya Illustrations", "ChibiEmojiStickers": "<PERSON>bi Em<PERSON>ji Stickers", "FourGridComics": "4-Grid Comics", "GhibliStyle": "Ghibli Style", "AnimeStyle": "Anime Style", "PixelArt": "Pixel Art", "DisneyStyle": "Disney Style", "PixarStyle": "Pixar Style", "RealisticStyle": "Realistic Style"}, "aspectRatio": {"Square": "Square", "Landscape": "Landscape", "Portrait": "Portrait"}, "colorOptions": {"NoColor": "No Color", "WarmColors": "Warm Colors", "ColdColors": "Cold Colors", "SoftColors": "Soft Colors", "VibrantColors": "Vibrant Colors", "PastelColors": "Pastel Colors", "BlackAndWhite": "Black And White"}, "compositionOptions": {"NoComposition": "No Composition", "BlurryBackground": "Blurry Background", "CloseUp": "Close-up", "WideAngle": "Wide Angle", "DepthOfField": "Depth of Field", "LowAngle": "Low Angle", "HighAngle": "High Angle", "MacroPhotography": "Macro Photography"}, "generatingStatus": "Generating...", "generating": "Generating...", "generationResults": "Generation Results", "progress": "Progress", "complete": "Complete", "completeMessage": "Generation complete!", "waitTimeMessage": "Image generation takes about 2-3 minutes. You can wait or check the results later in the 'My Works' page", "imageActionButtons": {"download": "Download", "viewHDOriginal": "View HD Original", "continueAdjusting": "Continue Adjusting Image"}, "outputOptions": {"outputCount": "Number of images", "outputCountDescription": "Generate 1-9 images at once"}, "actions": {"remove": "Remove"}, "errors": {"uploadFailed": "Upload failed", "generationFailed": "Image generation failed", "invalidPrompt": "Please enter a valid prompt", "invalidImage": "Please upload a valid image", "unsupportedFormat": "Unsupported file format, please upload PNG, JPG or WEBP images only", "pollingFailed": "Failed to get generation status"}}}, "video_generator": {"block": {"title": "AI Video Generator", "description": "Create stunning AI-generated videos with our powerful tool", "subtitle": "Transform your ideas into videos with advanced AI technology"}, "core": {"title": "AI Video Generator", "description": "Generate videos with AI using text prompts or transform existing images into videos", "tabs": {"textToVideo": "Text to Video", "imageToVideo": "Image to Video"}, "prompt_label": "What would you like to create?", "prompt_placeholder": "Describe the video you want to generate", "prompt_placeholder_img2video": "Describe how to animate this image", "upload_button": "Upload Image", "generate_button": "Generate Video", "clear_button": "Clear", "prompt_input": {"character_limit": "Character limit reached", "characters_remaining": "characters remaining", "suggestions": "Suggestions", "history": "History"}, "video_style": {"label": "Video Style", "NoStyle": "No Style", "Cinematic": "Cinematic", "Anime": "Anime", "3D Animation": "3D Animation", "Documentary": "Documentary"}, "duration": {"seconds5": {"label": "Short (5s)", "description": "Standard length, suitable for most scenes"}, "seconds10": {"label": "<PERSON> (10s)", "description": "Extended duration for complex scenes or transitions"}}, "tips": {"img2video": "Upload an image to animate it as a short video", "text2video": "Enter a detailed description for better results"}, "errors": {"prompt_required": "Please enter a prompt", "image_required": "Please upload an image", "unknown": "An unknown error occurred", "upload_failed": "Upload failed", "generation_failed": "Video generation failed", "invalid_prompt": "Please enter a valid prompt", "invalid_image": "Please upload a valid image", "unsupported_format": "Unsupported file format, please upload PNG, JPG or WEBP images only", "invalid_response": "Invalid server response"}, "prompt": {"placeholder": "Describe the video you want to generate..."}}, "generation_results": {"generating": "Generating Video", "please_wait": "Please wait, this may take up to a minute...", "error_title": "Generation Failed", "retry": "Try Again", "title": "Generated Videos", "regenerate": "Generate Again", "video_number": "Video {number}", "download": "Download Video", "view_original": "View in New Tab", "complete": "Generation complete!"}, "buttons": {"generating": "Generating...", "generate": "Generate Video", "clear": "Clear"}, "duration": {"seconds5": {"label": "Short (5s)", "description": "Standard length, suitable for most scenes"}, "seconds10": {"label": "<PERSON> (10s)", "description": "Extended duration for complex scenes or transitions"}}, "aspect_ratio": {"title": "Aspect Ratio", "description": "Select the aspect ratio for your video", "placeholder": "Select aspect ratio", "square": "Square (1:1)", "landscape": "Landscape (16:9)", "portrait": "Portrait (9:16)"}, "image_to_video": {"uploader_title": "Reference Image", "uploader_description": "Upload an image to animate into a video", "prompt_title": "Motion Description", "prompt_description": "Describe how you want the image to animate", "prompt_placeholder": "e.g., \"zoom in slowly\", \"character walking\"..."}, "modes": {"text_to_video": "Text to Video", "image_to_video": "Image to Video"}, "style_selector": {"title": "Select Video Style", "description": "Choose a style to apply to your generated video"}, "styles": {"title": "Video Style", "no_style": {"label": "No Style", "description": "Generate video without any specific style"}, "realistic": {"label": "Realistic", "description": "Photorealistic video with natural elements"}, "cinematic": {"label": "Cinematic", "description": "Professional movie-like appearance with film grain and dramatic lighting"}, "anime": {"label": "Anime", "description": "Japanese animation style with colorful characters"}, "cartoon": {"label": "Cartoon", "description": "Classic cartoon animation with exaggerated features"}, "pixar": {"label": "Pixar", "description": "3D animation style similar to Pixar films"}, "ghibli": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "Inspired by <PERSON> Ghibli's distinctive artistic style"}, "pixel_art": {"label": "Pixel Art", "description": "Retro video game inspired pixel aesthetics"}}, "duration_selector": {"title": "Video Duration", "description": "Select the duration of your generated video"}}}, "common": {"file_uploader": {"drop_active": "Drop files to start upload", "uploading": "Uploading...", "click_or_drag": "Click or drag {fileType} here to upload", "supported_formats": "Supports {formats}", "max_files_single": "single file", "max_files_multiple": "up to {count} files", "file_types": {"image": "images", "video": "videos", "audio": "audio", "file": "files"}, "upload_complete": "Upload complete", "upload_failed": "Upload failed", "uploading_files": "Uploading {count} files...", "uploaded_successfully": "Successfully uploaded {count} files", "progress": "Progress"}}}