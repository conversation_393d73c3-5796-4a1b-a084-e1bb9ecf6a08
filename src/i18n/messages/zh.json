{"metadata": {"title": "几小时内构建任何 AI SaaS 创业项目 | ShipAny", "description": "ShipAny 是一个用于构建 AI SaaS 创业项目的 NextJS 模板，提供各种模板和组件，帮助您快速启动。", "keywords": "Ship<PERSON>ny, AI SaaS 模板, NextJS 模板"}, "user": {"sign_in": "登录", "sign_out": "退出登录", "credits": "额度", "api_keys": "API 密钥", "my_orders": "我的订单", "user_center": "用户中心", "admin_system": "管理后台"}, "sign_modal": {"sign_in_title": "登录", "sign_in_description": "登录您的账户", "sign_up_title": "注册", "sign_up_description": "创建新账户", "email_title": "邮箱", "email_placeholder": "请输入您的邮箱", "password_title": "密码", "password_placeholder": "请输入您的密码", "forgot_password": "忘记密码？", "or": "或", "continue": "继续", "no_account": "还没有账户？", "email_sign_in": "使用邮箱登录", "google_sign_in": "使用 Google 登录", "github_sign_in": "使用 GitHub 登录", "close_title": "关闭", "cancel_title": "取消"}, "my_orders": {"title": "我的订单", "description": "在 ShipAny 上购买的订单。", "no_orders": "未找到订单", "tip": "", "activate_order": "激活订单", "actived": "已激活", "join_discord": "加入 Discord", "read_docs": "阅读文档", "table": {"order_no": "订单号", "email": "邮箱", "product_name": "产品名称", "amount": "金额", "paid_at": "支付时间", "github_username": "GitHub 用户名", "status": "状态"}}, "my_credits": {"title": "我的积分", "left_tip": "剩余积分: {left_credits}", "no_credits": "没有积分记录", "recharge": "充值", "table": {"trans_no": "交易号", "trans_type": "交易类型", "credits": "积分", "updated_at": "更新时间", "status": "状态"}}, "api_keys": {"title": "API 密钥", "tip": "请妥善保管您的 API 密钥，避免泄露", "no_api_keys": "没有 API 密钥", "create_api_key": "创建 API 密钥", "table": {"name": "名称", "key": "密钥", "created_at": "创建时间"}, "form": {"name": "名称", "name_placeholder": "API 密钥名称", "submit": "提交"}}, "blogs": {"title": "博客", "description": "关于 ShipAny 的新闻、资源和更新", "read_more_text": "阅读更多"}, "products": {"title": "产品", "description": "探索我们的产品", "no_products": "未找到产品。"}, "case_studies": {"title": "案例研究", "description": "探索我们的案例研究", "no_case_studies": "未找到案例研究。"}, "my_invites": {"title": "我的邀请", "description": "查看您的邀请记录", "no_invites": "未找到邀请记录", "my_invite_link": "我的邀请链接", "edit_invite_link": "编辑邀请链接", "copy_invite_link": "复制邀请链接", "invite_code": "邀请码", "invite_tip": "每邀请 1 位朋友购买 ShipAny，奖励 $50。", "invite_balance": "邀请奖励余额", "total_invite_count": "总邀请人数", "total_paid_count": "已充值人数", "total_award_amount": "总奖励金额", "update_invite_code": "设置邀请码", "update_invite_code_tip": "输入你的自定义邀请码", "update_invite_button": "保存", "no_orders": "你需要先购买过 ShipAny 才能邀请朋友", "no_affiliates": "你暂无邀请朋友的权限，请联系我们申请开通。", "table": {"invite_time": "邀请时间", "invite_user": "邀请用户", "status": "状态", "reward_percent": "奖励比例", "reward_amount": "奖励金额", "pending": "已注册，未支付", "completed": "已支付"}}, "feedback": {"title": "反馈", "description": "我们很乐意听取您对产品的看法或如何改进产品体验。", "submit": "提交", "loading": "提交中...", "contact_tip": "其他联系方式", "rating_tip": "您对 ShipAny 的看法如何？", "placeholder": "在这里留下您的反馈..."}, "components": {"image_generator": {"block": {"title": "AI 图像生成器", "description": "使用我们强大的工具创建令人惊叹的AI生成图像", "subtitle": "借助先进的AI技术将您的创意转化为视觉艺术"}, "core": {"title": "AI 图像生成器", "description": "使用文本提示生成图像或修改现有图像", "tabs": {"textToImage": "文生图", "imageToImage": "图生图", "batchGeneration": "批量生成", "imageFusion": "图像融合"}, "promptPlaceholder": "描述你想要的图像", "promptPlaceholderImg2Img": "描述如何修改这张图片", "uploadButton": "上传", "generateButton": "生成", "clearButton": "清除", "outputCountLabel": "数量：", "images": "图片", "generatedImages": "生成的图像", "downloadImage": "下载图片", "viewOriginal": "查看原图", "promptInput": {"characterLimit": "已达字符限制", "charactersRemaining": "剩余字符", "suggestions": "建议", "history": "历史记录"}, "styleOptions": {"NoStyle": "无风格", "SnoopyStyle": "史努比风格", "IrasutoyaIllustrations": "Irasutoya插图风格", "ChibiEmojiStickers": "Q版表情贴纸", "FourGridComics": "四格漫画", "GhibliStyle": "吉卜力风格", "AnimeStyle": "动漫风格", "PixelArt": "像素艺术", "DisneyStyle": "迪士尼风格", "PixarStyle": "皮克斯风格", "RealisticStyle": "写实风格"}, "aspectRatio": {"Square": "方形", "Landscape": "横版", "Portrait": "竖版"}, "colorOptions": {"NoColor": "无色彩偏向", "WarmColors": "暖色调", "ColdColors": "冷色调", "SoftColors": "柔和色调", "VibrantColors": "鲜艳色调", "PastelColors": "粉彩色调", "BlackAndWhite": "黑白"}, "compositionOptions": {"NoComposition": "无构图偏向", "BlurryBackground": "背景模糊", "CloseUp": "特写", "WideAngle": "广角", "DepthOfField": "景深", "LowAngle": "低角度", "HighAngle": "高角度", "MacroPhotography": "微距"}, "generatingStatus": "生成中...", "generationResults": "生成结果", "progress": "进度", "completeMessage": "生成完成！", "waitTimeMessage": "图像生成大约需要2-3分钟。您可以等待或稍后在\"我的作品\"页面查看结果", "imageActionButtons": {"download": "下载", "viewHDOriginal": "查看高清原图", "continueAdjusting": "继续调整图像"}, "outputOptions": {"outputCount": "图像数量", "outputCountDescription": "一次生成1-9张图片"}, "actions": {"remove": "移除"}, "errors": {"uploadFailed": "上传失败", "generationFailed": "图像生成失败", "invalidPrompt": "请输入有效的提示词", "invalidImage": "请上传有效的图片", "unsupportedFormat": "不支持的文件格式，请上传PNG、JPG或WEBP格式的图片", "pollingFailed": "获取生成状态失败"}}}, "video_generator": {"block": {"title": "AI 视频生成器", "description": "使用我们强大的工具创建令人惊叹的AI生成视频", "subtitle": "借助先进的AI技术将您的创意转化为视频"}, "core": {"title": "AI 视频生成器", "description": "使用文本提示生成视频或将现有图像转换为视频", "tabs": {"textToVideo": "文生视频", "imageToVideo": "图生视频"}, "prompt_label": "您想创建什么？", "prompt_placeholder": "描述您想要生成的视频", "prompt_placeholder_img2video": "描述如何让这张图片动起来", "upload_button": "上传图片", "generate_button": "生成视频", "clear_button": "清除", "prompt_input": {"character_limit": "已达字符限制", "characters_remaining": "剩余字符", "suggestions": "建议", "history": "历史记录"}, "video_style": {"label": "视频风格", "NoStyle": "无风格", "Cinematic": "电影风格", "Anime": "动漫风格", "3D Animation": "3D动画", "Documentary": "纪录片风格"}, "duration": {"label": "时长", "seconds": "秒"}, "tips": {"img2video": "上传图片将其制作成短视频", "text2video": "输入详细描述以获得更好的结果"}, "errors": {"prompt_required": "请输入提示词", "image_required": "请上传图片", "unknown": "发生未知错误", "upload_failed": "上传失败", "generation_failed": "视频生成失败", "invalid_prompt": "请输入有效的提示词", "invalid_image": "请上传有效的图片", "unsupported_format": "不支持的文件格式，请上传PNG、JPG或WEBP格式的图片", "invalid_response": "服务器响应无效"}, "prompt": {"placeholder": "描述您想要生成的视频..."}}, "generation_results": {"generating": "正在生成视频", "please_wait": "请稍等，这可能需要一分钟左右...", "error_title": "生成失败", "retry": "重试", "title": "生成的视频", "regenerate": "重新生成", "video_number": "视频 {number}", "download": "下载视频", "view_original": "在新标签页中查看", "complete": "生成完成！"}, "buttons": {"generating": "生成中...", "generate": "生成视频", "clear": "清除"}, "duration": {"seconds5": {"label": "短 (5秒)", "description": "标准长度，适合大多数场景"}, "seconds10": {"label": "长 (10秒)", "description": "延长时间，适合复杂场景或过渡"}}, "aspect_ratio": {"title": "宽高比", "description": "选择您的视频宽高比", "placeholder": "选择宽高比", "square": "正方形 (1:1)", "landscape": "横屏 (16:9)", "portrait": "竖屏 (9:16)"}, "image_to_video": {"uploader_title": "参考图片", "uploader_description": "上传图片制作成动画视频", "prompt_title": "动作描述", "prompt_description": "描述您希望图像如何动起来", "prompt_placeholder": "例如，\"慢慢放大\"，\"角色行走\"..."}, "modes": {"text_to_video": "文生视频", "image_to_video": "图生视频"}, "style_selector": {"title": "选择视频风格", "description": "为您生成的视频选择一种风格"}, "styles": {"title": "视频风格", "no_style": {"label": "无风格", "description": "生成没有特定风格的视频"}, "realistic": {"label": "写实风格", "description": "具有自然元素的照片级真实视频"}, "cinematic": {"label": "电影风格", "description": "带有胶片颗粒和戏剧性光影的专业电影外观"}, "anime": {"label": "动漫风格", "description": "日本动画风格，角色色彩丰富"}, "cartoon": {"label": "卡通风格", "description": "经典卡通动画，特征夸张"}, "pixar": {"label": "皮克斯风格", "description": "类似皮克斯电影的3D动画风格"}, "ghibli": {"label": "吉卜力风格", "description": "受吉卜力工作室独特艺术风格启发"}, "pixel_art": {"label": "像素艺术", "description": "受复古电子游戏启发的像素美学"}}, "duration_selector": {"title": "视频时长", "description": "选择您生成的视频的时长"}}}, "common": {"file_uploader": {"drop_active": "释放文件开始上传", "uploading": "正在上传...", "click_or_drag": "点击或拖拽{fileType}到此处上传", "supported_formats": "支持 {formats}", "max_files_single": "单个文件", "max_files_multiple": "最多 {count} 个文件", "file_types": {"image": "图片", "video": "视频", "audio": "音频", "file": "文件"}, "upload_complete": "上传完成", "upload_failed": "上传失败", "uploading_files": "正在上传 {count} 个文件...", "uploaded_successfully": "成功上传 {count} 个文件", "progress": "进度"}}}