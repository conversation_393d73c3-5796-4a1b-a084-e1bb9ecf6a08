// Available video generation modes
export enum VideoGenerationMode {
  TextToVideo = 'textToVideo',
  ImageToVideo = 'imageToVideo',
}

// Style options for video generation
export enum VideoStyle {
  NoStyle = 'No Style',
  AnimeStyle = 'Anime Style',
  RealisticStyle = 'Realistic Style',
  CinematicStyle = 'Cinematic Style',
  CartoonStyle = 'Cartoon Style',
  PixarStyle = 'Pixar Style',
  GhibliStyle = 'Ghibli Style',
  PixelArtStyle = 'Pixel Art Style',
}

// Duration options for video generation (in seconds)
export enum VideoDuration {
  Seconds5 = 5,
  Seconds10 = 10,
}

// Aspect ratio options
export enum VideoAspectRatio {
  Square = 'Square',     // 1:1
  Landscape = 'Landscape', // 16:9
  Portrait = 'Portrait',   // 9:16
}

/**
 * Frontend request format directly matching the form UI
 */
export interface VideoGenerationRequest {
  mode: VideoGenerationMode;
  prompt: string;

  // 反向提示词
  negativePrompt?: string;

  videoStyle?: VideoStyle;
  aspectRatio?: VideoAspectRatio;
  duration?: VideoDuration;
  // outputFormat: DefaultVideoExtension;
  
  /**
   * File ID from presigned URL upload for image-to-video generation
   * Used when the image has been uploaded via the new upload system
   */
  fileId?: string;
}

