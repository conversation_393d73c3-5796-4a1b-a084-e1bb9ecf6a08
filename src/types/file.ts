/**
 * File Management Type Definitions
 *
 * @description
 * This file contains comprehensive type definitions for file management within the application.
 * It defines the core `FileRecord` interface, which represents a file's complete record in the database,
 * as well as associated types for metadata, status, queries, and API results.
 *
 * @see types/file-core.ts for the most essential file information interfaces.
 * @see types/file-upload.ts for types related to the client upload process.
 */

// ==================== Core Enums & Constants ====================

export const FileTypeGroup = {
  // 媒体文件
  IMAGE: 'image',
  VIDEO: 'video', 
  AUDIO: 'audio',
  
  // 文档文件
  PDF: 'pdf',
  WORD: 'word',           // .doc, .docx
  EXCEL: 'excel',         // .xls, .xlsx
  PPT: 'ppt',            // .ppt, .pptx
  
  // 文本和数据
  TEXT: 'text',          // .txt, .md, .rtf
  CSV: 'csv',            // .csv
  
  // 压缩和归档
  ARCHIVE: 'archive',    // .zip, .rar, .7z, .tar.gz
  
  // 兜底分类
  OTHER: 'other',
} as const;

export type FileTypeGroup = typeof FileTypeGroup[keyof typeof FileTypeGroup];


/**
 * Defines the business-logic category for a file. This is used for generating
 * storage keys and applying specific business rules.
 * This is intentionally a flexible string type in the database, but controlled
 * by a constant object in the code for type safety.
 */
export const FileCategory = {
  USER_PROFILE: 'user-profile',
  USER_UPLOADS: 'user-uploads',
  TASK_OUTPUT: 'task-output',
  BLOGS: 'blogs',
  TEMP: 'temp',
} as const;
export type FileCategory = typeof FileCategory[keyof typeof FileCategory];

/**
 * Utility type for task-related file category queries.
 * Automatically stays in sync with FileCategory changes.
 * Restricts task file queries to only relevant categories plus 'all' option.
 */
export type TaskFileQueryCategory = 'all' | typeof FileCategory.USER_UPLOADS | typeof FileCategory.TASK_OUTPUT;

/**
 * Defines the access level for a file, determining how it can be accessed.
 */
export const AccessLevel = {
  PUBLIC: 'public',       // Accessible to anyone via the direct URL.
  PRIVATE: 'private',     // Accessible only to the owner, requires specific permissions.
  RESTRICTED: 'restricted', // Accessible to logged-in users or specific roles.
} as const;
export type AccessLevel = typeof AccessLevel[keyof typeof AccessLevel];

/**
 * Defines the possible lifecycle statuses of a file.
 */
export const FileStatus = {
  PENDING: 'pending',       // The file record is created, but the upload is not yet complete or confirmed.
  PROCESSING: 'processing', // The file is being processed (e.g., transcoding, analysis).
  ACTIVE: 'active',         // The file is uploaded and available for use.
  DELETED: 'deleted',       // The file has been marked for deletion.
  EXPIRED: 'expired',       // The file has passed its expiration date.
  FAILED: 'failed',         // The file processing or upload failed.
} as const;
export type FileStatus = typeof FileStatus[keyof typeof FileStatus];


// ==================== Core Interfaces ====================

/**
 * Basic file information, often used in API responses.
 */
export interface FileInfo {
  fileId: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
  category: FileCategory;
  url: string; // The direct, public-facing URL
}

/**
 * Represents a file record as it exists in the `files` database table.
 */
export interface FileRecord {
  /** Database auto-increment primary key */
  id: number;
  /** Unique file identifier (e.g., 'file_...') */
  file_id: string;
  /** UUID of the user who owns this file */
  user_uuid: string;
  /** Optional task ID that generated or is associated with this file */
  task_id?: string | null;
  /** Cloud storage key/path (e.g., 'user-uploads/2024/07/user_uuid/file_id.png') */
  storage_key: string;
  /** User-friendly filename for display purposes */
  file_name: string;
  /** The specific MIME type of the file (e.g., 'image/png') */
  mime_type: string;
  /** File size in bytes */
  file_size: number;
  /** The business-logic category for the file */
  file_category: FileCategory;
  /** The access control level for the file */
  access_level: AccessLevel;
  /** The current lifecycle status of the file */
  status: FileStatus;
  /** ISO 8601 timestamp of when the record was created */
  created_at: string;
  /** Optional ISO 8601 timestamp for when the file should be auto-cleaned up */
  expires_at?: string | null;
  /** Flexible JSONB field for any additional, unstructured metadata */
  metadata?: FileMetadata | null;
}

/**
 * A flexible structure for storing additional, type-specific metadata.
 */
export interface FileMetadata {
  /** The source of the file (e.g., 'user_upload', 'fal.ai', 'dall-e-3') */
  source?: string;
  /** If downloaded from another location, this stores the original URL */
  original_url?: string;
  /** For AI-generated content, stores the generation parameters */
  generation_params?: Record<string, unknown>;
  /** The original filename from the client upload */
  original_filename?: string;
  /** Any other custom metadata */
  [key: string]: unknown;
}


// ==================== Database Operation Interfaces ====================

/**
 * Data structure required to create a new file record in the database.
 */
export interface FileCreationData {
  file_id: string;
  user_uuid: string;
  task_id?: string;
  storage_key: string;
  file_name: string;
  mime_type: string;
  file_size: number;
  file_category: FileCategory;
  access_level: AccessLevel;
  expires_at?: string;
  metadata?: FileMetadata;
}

// ==================== Service Layer Interfaces ====================

/**
 * Return structure for server-side file creation operations.
 */
export interface FileCreationResult {
  file_record: FileRecord;
  url: string; // The direct, public-facing URL
}

/**
 * Structure for a user's storage usage statistics.
 */
export interface StorageStats {
  total_files: number;
  total_size: number;
  by_category: Record<FileCategory, { 
    count: number; 
    size: number; 
  }>;
}

/**
 * Simple interface for handling file uploads from base64 data.
 */
export interface FileDataEntry {
  base64Data: string;
  mimeType: string;
}
