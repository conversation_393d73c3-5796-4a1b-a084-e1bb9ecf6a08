import { NextRequest, NextResponse } from 'next/server';
import { getFileAccessUrl, getFileRecord } from '@/services/file';
import { createPresignedPreviewUrl } from '@/lib/cloud-storage';
import logger from '@/lib/logger';

/**
 * GET /api/files/{fileId}/preview - Generate presigned preview URL for private files
 * Allows viewing files in browser with inline disposition
 * Used for displaying private files (images, videos) directly in the browser
 * 
 * Path parameters:
 * - fileId: string - Unique file identifier
 * 
 * Query parameters:
 * - expires_in: number - URL expiration time in seconds (default: 3600)
 * - redirect: boolean - Whether to redirect to presigned URL (default: true)
 * 
 * Response (if redirect=false):
 * {
 *   code: 0,
 *   message: "Preview URL generated successfully",
 *   data: {
 *     previewUrl: string,
 *     expiresAt: string,
 *     fileInfo: {
 *       fileId: string,
 *       fileName: string,
 *       fileType: string,
 *       mimeType: string
 *     }
 *   }
 * }
 * 
 * Response (if redirect=true): HTTP 302 redirect to presigned URL
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ fileId: string }> }
) {
  try {
    // Extract fileId from params
    const { fileId } = await params;
    if (!fileId) {
      return NextResponse.json({
        code: 400,
        message: 'File ID is required'
      }, { status: 400 });
    }

    // Extract query parameters
    const searchParams = request.nextUrl.searchParams;
    const shouldRedirect = searchParams.get('redirect') !== 'false'; // Default to true

    logger.info('Preview URL request received', 
      { fileId, shouldRedirect }, 
      { filePath: "app/api/files/[fileId]/preview/route.ts", functionName: 'GET' });

    // Get file record from database
    const fileRecord = await getFileRecord(fileId);
    if (!fileRecord) {
      return NextResponse.json({
        code: 404,
        message: 'File not found'
      }, { status: 404 });
    }

    // Generate presigned preview URL with inline disposition (for viewing in browser)
    // const previewUrl = await createPresignedPreviewUrl({
    //   storageKey: fileRecord.storage_key,
    //   expiresIn: 3600, // 1 hour default
    // });
    const previewUrl = await getFileAccessUrl(fileId);
    
    logger.info('Preview URL generated', 
      { fileId, previewUrl }, 
      { filePath: "app/api/files/[fileId]/preview/route.ts", functionName: 'GET' });

    // Redirect to the presigned URL for direct viewing (default behavior)
    if (shouldRedirect) {
      if (!previewUrl) {
        return NextResponse.json({
          code: 404,
          message: 'File access URL not available'
        }, { status: 404 });
      }
      return NextResponse.redirect(previewUrl);
    }

    // Return JSON response with URL (when redirect=false)
    if (!previewUrl) {
      return NextResponse.json({
        code: 404,
        message: 'File access URL not available'
      }, { status: 404 });
    }
    return NextResponse.json({
      code: 0,
      message: 'Preview URL generated successfully',
      data: {
        previewUrl,
        fileInfo: {
          fileId: fileRecord.file_id,
          fileName: fileRecord.file_name,
          mimeType: fileRecord.mime_type
        }
      }
    });

  } catch (error) {
    logger.error('Preview URL generation failed', error, 
      { fileId: (await params).fileId },
      { filePath: "app/api/files/[fileId]/preview/route.ts", functionName: 'GET' });
    
    return NextResponse.json({
      code: 500,
      message: error instanceof Error ? error.message : 'Failed to generate preview URL'
    }, { status: 500 });
  }
}
