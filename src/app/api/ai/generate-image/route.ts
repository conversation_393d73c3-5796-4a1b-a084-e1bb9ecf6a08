import { respData, respErr } from "@/lib/resp";
import {
  ImageGenerationMode,
  ImageGenerationRequest,
} from "@/types/ai/image-gen-types";
import { 
  buildKieParams, 
} from "@/aisdk/adapters/image-params-adapter";
import <PERSON><PERSON> from "@/aisdk/model-providers/kie";
import { createTask, setProviderTaskId } from "@/services/task";
import { TaskTypes } from "@/types/task";
import logger from "@/lib/logger";
import { NgrokWebHook } from "@/lib/ngrok-webhook";
import { getFileRecord, updateFileByTaskId, getFileAccessUrl } from "@/services/file";

/**
 * API route handler for image generation
 * Now creates an asynchronous task and returns the task ID immediately
 * The actual generation is performed in the background
 */
export async function POST(req: Request) {
  try {
    // Get user UUID from request headers or use 'anonymous'
    const userUuid = req.headers.get('x-user-uuid');
    if (!userUuid) {
      return respErr("User Login is required");
    }
    logger.info("User UUID", { userUuid }, 
      { filePath: "app/api/ai/generate-image/route.ts", functionName: 'POST' });

    // Parse and validate the request body from frontend
    const imgGenRequest = (await req.json()) as ImageGenerationRequest;
    // Log request details for debugging
    console.log(
      "Image generation request:",
      JSON.stringify({
        ...imgGenRequest,
        // Don't log the full file data for clarity and security
        fileIds: imgGenRequest.fileIds ? `[${imgGenRequest.fileIds.length} FILES]` : undefined
      }, null, 2)
    );

    // Validate request
    if (!imgGenRequest.prompt || imgGenRequest.prompt.trim() === "") {
      return respErr("Prompt is required");
    }

    // Check for required images in image processing modes
    if (imgGenRequest.mode === ImageGenerationMode.ImageFusion || 
        imgGenRequest.mode === ImageGenerationMode.ImageToImage) {
      // Verify images are provided in non-text-to-image modes
      const hasImages = imgGenRequest.fileIds && imgGenRequest.fileIds.length > 0;
      
      if (!hasImages) {
        return respErr("Image files are required for image processing modes (provide fileIds)");
      }
    }

    // Limit output count to a reasonable range (1-9)
    if (imgGenRequest.outputCount < 1 || imgGenRequest.outputCount > 9) {
      return respErr("Output count wrong");
    }
    
    let sourceFileRecords: any[] = [];
    let imageUrls: string[] = [];
    
    // Handle source images for image processing modes
    if (imgGenRequest.mode === ImageGenerationMode.ImageFusion || 
        imgGenRequest.mode === ImageGenerationMode.ImageToImage) {
      
      // Use fileIds from presigned URL uploads
      if (imgGenRequest.fileIds && imgGenRequest.fileIds.length > 0) {
        console.log("Using pre-uploaded files for AI generation", { 
          fileIds: imgGenRequest.fileIds
        });
        
        // Retrieve file records and validate they exist and belong to user
        try {
          sourceFileRecords = [];
          imageUrls = [];
          
          for (const fileId of imgGenRequest.fileIds) {
            const fileRecord = await getFileRecord(fileId);
            
            if (!fileRecord) {
              return respErr(`File not found: ${fileId}`);
            }
            
            if (fileRecord.user_uuid !== userUuid) {
              return respErr(`File access denied: ${fileId}`);
            }
            
            sourceFileRecords.push(fileRecord);
            
            // Get access URL for the file (handles both public and private files)
            const fileUrl = await getFileAccessUrl(fileId);
            
            if (fileUrl) {
              imageUrls.push(fileUrl);
            }
          }
          
          console.log("Source files validated successfully", {
            fileCount: sourceFileRecords.length,
            fileIds: sourceFileRecords.map(f => f.file_id)
          });
          
        } catch (error) {
          logger.error("Failed to retrieve source files", error, { fileIds: imgGenRequest.fileIds });
          return respErr("Failed to retrieve source files");
        }
      }
    }

    const currAiModel = Kie.ImageModels.gpt4o;

    // Create a new task for this image generation request
    const taskId = await createTask({
      userUuid,
      taskType: TaskTypes.IMAGE,
      params: imgGenRequest,
      modelId: currAiModel.id,
      provider: currAiModel.provider // Currently only supporting KIE
    });

    // Update source files with task ID if any were uploaded
    if (sourceFileRecords.length > 0) {
      try {
        // Update the task_id for source files to associate them with the AI task
        for (const fileRecord of sourceFileRecords) {
          await updateFileByTaskId(fileRecord.file_id, taskId);
        }
        console.log("Updated source files with task ID", { taskId, fileCount: sourceFileRecords.length });
      } catch (error) {
        logger.error("Failed to update source files with task ID", error, 
          { taskId, fileIds: sourceFileRecords.map(f => f.file_id) },
          { filePath: "app/api/ai/generate-image/route.ts", functionName: 'POST' });
        // Don't throw error as files are already uploaded successfully, just association failed
      }
    }

    // const webhookUrl = await getKieWebhookUrl(taskId);
    const webhookPath = `/api/ai/webhooks/kie/image-generated?taskId=${taskId}`;
    const webhookUrl = await NgrokWebHook.getWebhookUrl(webhookPath);
    console.log("Generated webhook URL", { taskId, webhookUrl });

    const kieParams = buildKieParams(imgGenRequest, webhookUrl, imageUrls);
    logger.info("KIE params", { taskId, kieParams }, 
      { filePath: "app/api/ai/generate-image/route.ts", functionName: 'POST' });

    const generator = new Kie.ImageGenerator();
    const modelResponse = await generator.submitImageGenerationTask(
      currAiModel.id,
      kieParams
    );
    console.log("KIE response:", { taskId, response: modelResponse });

    await setProviderTaskId(taskId, modelResponse.taskId);
    logger.info("Task submitted successfully", { 
      taskId, 
      kieTaskId: modelResponse.taskId,
      webhookUrl 
    }, 
    { filePath: "app/api/ai/generate-image/route.ts", functionName: 'POST' });

    // Return the task ID immediately
    return respData({ taskId });
  } catch (error) {
    // Handle general request errors
    logger.error("Error creating image generation task", { error }, 
      { filePath: "app/api/ai/generate-image/route.ts", functionName: 'POST' });
    return respErr("Failed to create image generation task");
  }
}
