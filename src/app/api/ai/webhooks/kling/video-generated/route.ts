import { NextRequest, NextResponse } from 'next/server';
import { getTask, getTaskByProviderTaskId, setTaskError, completeTaskWithResults, updateTaskStatus } from '@/services/task';
import logger from '@/lib/logger';
import { Task, TaskResults, TaskStatus, TaskStatusType } from '@/types/task';

/**
 * POST handler for Kling video generation webhook callbacks
 * 
 * This endpoint receives webhook notifications when a video generation task
 * is completed on the Kling platform. It downloads generated videos, uploads them
 * to our storage, creates file records, and completes the task.
 * 
 * The webhook payload contains:
 * - Task status (succeed/failed/processing/submitted)
 * - Generated video URLs if successful
 * - Error information if failed
 * - Task ID for correlation
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Get taskId from query parameter or external_task_id in payload
    const searchParams = req.nextUrl.searchParams;
    let taskId: string | null = searchParams.get('taskId');

    // Parse webhook payload
    const payload = await req.json();
    logger.info('Received Kling video generation webhook', { payload }, 
      { filePath: "app/api/ai/webhooks/kling/video-generated/route.ts", functionName: 'POST' });
    
    // If taskId is not in URL, check if it's in the payload as external_task_id
    if (!taskId && payload.task_info && payload.task_info.external_task_id) {
      taskId = payload.task_info.external_task_id;
    } else if (!taskId && payload.external_task_id) {
      taskId = payload.external_task_id;
    }
    
    // If we still don't have a taskId, try to find by provider's task ID
    let task: Task | null = null;
    if (!taskId) {
      const providerTaskId = payload.task_id;
      if (providerTaskId) {
        task = await getTaskByProviderTaskId(providerTaskId);
        taskId = task?.task_id || null;
      }
    } else {
      task = await getTask(taskId);
    }

    if (!taskId || !task) {
      logger.error('Task not found for Kling webhook', 
        { taskId, payload }, 
        { filePath: "app/api/ai/webhooks/kling/video-generated/route.ts", functionName: 'POST' });
      return NextResponse.json(
        { success: false, error: "Task not found" },
        { status: 404 }
      );
    }

    // Process webhook based on task status
    const taskStatus = payload.task_status;
    const { status, progress } = mapKlingStatusToTaskStatus(taskStatus);
    
    // Handle status based on our mapped internal status
    if (status === TaskStatus.COMPLETED) {
      // Task completed successfully - extract video URLs
      const videoUrls = extractVideoUrls(payload);
      
      if (videoUrls.length > 0) {
        try {
          // Use new file management system to process generated videos
          const taskResult = await completeTaskWithResults(
            taskId,
            task.user_uuid,
            videoUrls,
            task.provider + "_" + task.model_id,
            {
              processing_time_seconds: undefined, // Kling doesn't provide this
              generation_params: task.params,
              provider_response: {
                provider_task_id: payload.task_id || 'unknown',
                raw_response: payload
              }
            }
          );

          logger.info('Successfully completed Kling video generation task', 
            { taskId, filesCount: taskResult.files.length }, 
            { filePath: "app/api/ai/webhooks/kling/video-generated/route.ts", functionName: 'POST' });

        } catch (fileProcessingError) {
          logger.error('Failed to process generated videos', fileProcessingError, 
            { taskId, videoUrls }, 
            { filePath: "app/api/ai/webhooks/kling/video-generated/route.ts", functionName: 'POST' });
          
          await setTaskError(taskId, 
            `Failed to process generated videos: ${fileProcessingError instanceof Error ? fileProcessingError.message : 'Unknown error'}`);
        }
      } else {
        // Successfully completed but no videos found
        await setTaskError(taskId, 'No videos found in successful response');
        logger.error('Missing video URLs in success response', 
          { taskId, payload }, 
          { filePath: "app/api/ai/webhooks/kling/video-generated/route.ts", functionName: 'POST' });
      }
    } 
    else if (status === TaskStatus.FAILED) {
      // Task failed
      const errorMessage = payload.task_status_msg || 'Video generation failed';
      await setTaskError(taskId, errorMessage);
      
      logger.error('Kling video generation failed', 
        { taskId, errorMessage, payload }, 
        { filePath: "app/api/ai/webhooks/kling/video-generated/route.ts", functionName: 'POST' });
    } 
    else if (status === TaskStatus.PROCESSING) {
      // Task still in progress - update progress if available
      await updateTaskStatus(taskId, status, progress);
      
      logger.info('Kling video generation progress update', 
        { taskId, status, progress }, 
        { filePath: "app/api/ai/webhooks/kling/video-generated/route.ts", functionName: 'POST' });
    }
    
    // Return success response to acknowledge receipt of the webhook
    return NextResponse.json({ success: true });
  } catch (error) {
    // Log the error but still return 200 to prevent webhook retries
    logger.error('Error processing Kling video webhook', error, {}, 
      { filePath: "app/api/ai/webhooks/kling/video-generated/route.ts", functionName: 'POST' });
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 200 }
    );
  }
}

/**
 * Map Kling task status to our internal TaskStatus enum
 * 
 * @param klingStatus Status from Kling webhook
 * @returns Object with mapped status and progress
 */
function mapKlingStatusToTaskStatus(klingStatus: string): {
  status: TaskStatusType;
  progress: number;
} {
  switch (klingStatus) {
    case 'succeed':
      return { status: TaskStatus.COMPLETED, progress: 100 };
    case 'failed':
      return { status: TaskStatus.FAILED, progress: 0 };
    case 'processing':
      return { status: TaskStatus.PROCESSING, progress: 50 };
    case 'submitted':
    case 'pending':
      return { status: TaskStatus.PROCESSING, progress: 10 };
    default:
      logger.warn('Unknown Kling status', { klingStatus }, 
        { filePath: "app/api/ai/webhooks/kling/video-generated/route.ts", functionName: 'mapKlingStatusToTaskStatus' });
      return { status: TaskStatus.PROCESSING, progress: 0 };
  }
}

/**
 * Extract video URLs from Kling webhook payload
 * 
 * @param payload The webhook payload from Kling
 * @returns Array of video URLs
 */
function extractVideoUrls(payload: any): string[] {
  const videoUrls: string[] = [];
  
  // Extract videos from task_result if available
  if (payload.task_result && payload.task_result.videos) {
    // Iterate through video results
    payload.task_result.videos.forEach((video: any) => {
      if (video.url) {
        videoUrls.push(video.url);
      }
    });
  }
  
  return videoUrls;
}
