import { NextRequest, NextResponse } from 'next/server';
import { getTask, setTaskError, completeTaskWithResults } from '@/services/task';
import logger from '@/lib/logger';
import { validateUrls } from '@/lib/url';


/**
 * POST handler for KIE image generation webhook callbacks
 * 
 * This endpoint receives webhook notifications when an image generation task
 * is completed on the KIE platform. It downloads generated images, uploads them
 * to our storage, creates file records, and completes the task.
 * 
 * The webhook payload contains:
 * - Status code (200=success, 400=content violation, 451=download failed)
 * - Generated image URLs if successful
 * - Error information if failed
 * - Task correlation data
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Verify webhook authenticity if WEBHOOK_SECRET is configured
    // This is optional as KIE doesn't have robust webhook verification
    if (process.env.WEBHOOK_SECRET) {
      // Placeholder for future verification logic
      // const signature = req.headers.get('x-webhook-signature');
      // etc.
    }

    // Extract task ID from query parameters
    const searchParams = req.nextUrl.searchParams;
    const taskId = searchParams.get('taskId');

    // Parse webhook payload
    const payload = await req.json();
    logger.info('Received KIE image generation webhook', 
      { taskId, payload }, 
      { filePath: "app/api/ai/webhooks/kie/image-generated/route.ts", functionName: 'POST' });

    if (!taskId) {
      logger.error('Missing taskId in KIE webhook request', 
        { payload }, 
        { filePath: "app/api/ai/webhooks/kie/image-generated/route.ts", functionName: 'POST' });
      return NextResponse.json(
        { success: false, error: "Missing taskId parameter" },
        { status: 400 }
      );
    }

    // Retrieve task details to get user information
    const task = await getTask(taskId);
    if (!task) {
      logger.error('Task not found for KIE webhook', 
        { taskId }, 
        { filePath: "app/api/ai/webhooks/kie/image-generated/route.ts", functionName: 'POST' });
      return NextResponse.json(
        { success: false, error: "Task not found" },
        { status: 404 }
      );
    }

    // Handle different status codes from KIE
    switch (payload.code) {
      case 200: // Success - Image generation completed successfully
        const result_urls = payload.data?.info?.result_urls;
        if (!result_urls || result_urls.length === 0) {
          await setTaskError(taskId, 'No image URLs found in successful response');
          logger.error('Missing image URLs in success response', 
            { taskId, payload }, 
            { filePath: "app/api/ai/webhooks/kie/image-generated/route.ts", functionName: 'POST' });
          return NextResponse.json(
            { success: false, error: "Missing image URLs" },
            { status: 200 }
          );
        }

        // Validate URLs from KIE response
        const result_urls_validation = validateUrls(result_urls);
        if (!result_urls_validation.isAllValid) {
          logger.warn('Some invalid URLs found in KIE response', 
            { taskId, invalidUrls: result_urls_validation.invalidUrls }, 
            { filePath: "app/api/ai/webhooks/kie/image-generated/route.ts", functionName: 'POST' });
        }

        if (result_urls_validation.validUrls.length === 0) {
          await setTaskError(taskId, 'No valid image URLs found in response');
          logger.error('No valid image URLs found in KIE response', 
            { taskId, payload }, 
            { filePath: "app/api/ai/webhooks/kie/image-generated/route.ts", functionName: 'POST' });
          return NextResponse.json(
            { success: false, error: "No valid image URLs" },
            { status: 200 }
          );
        }

        try {
          // Use new file management system to process generated images
          const taskResult = await completeTaskWithResults(
            taskId,
            task.user_uuid,
            result_urls_validation.validUrls,
            task.provider + "_" + task.model_id,
            {
              processing_time_seconds: undefined, // KIE doesn't provide this
              generation_params: task.params,
              provider_response: {
                provider_task_id: payload.data?.taskId || 'unknown',
                raw_response: payload
              }
            }
          );

          logger.info('Successfully completed KIE image generation task', 
            { taskId, filesCount: taskResult.files.length }, 
            { filePath: "app/api/ai/webhooks/kie/image-generated/route.ts", functionName: 'POST' });

        } catch (fileProcessingError) {
          logger.error('Failed to process generated images', fileProcessingError, 
            { taskId, validUrls: result_urls_validation.validUrls }, 
            { filePath: "app/api/ai/webhooks/kie/image-generated/route.ts", functionName: 'POST' });
          
          await setTaskError(taskId, 
            `Failed to process generated images: ${fileProcessingError instanceof Error ? fileProcessingError.message : 'Unknown error'}`);
        }
        break;
        
      case 400: // Content Violation - The image content violates content policy
        await setTaskError(taskId, 'Content policy violation: The generated image violates content policies');
        logger.warn('KIE content policy violation', 
          { taskId }, 
          { filePath: "app/api/ai/webhooks/kie/image-generated/route.ts", functionName: 'POST' });
        break;
        
      case 451: // Download Failed - Unable to download image from provided URLs
        await setTaskError(taskId, 'Failed to download source images from the provided URLs');
        logger.error('KIE download failure', 
          { taskId }, 
          { filePath: "app/api/ai/webhooks/kie/image-generated/route.ts", functionName: 'POST' });
        break;
        
      default:
        const errorMsg = `Unknown error (code: ${payload.code || 'not provided'})`;
        await setTaskError(taskId, errorMsg);
        logger.error('Unknown KIE error code', 
          { taskId, code: payload.code, payload }, 
          { filePath: "app/api/ai/webhooks/kie/image-generated/route.ts", functionName: 'POST' });
    }
    
    // Return success response to acknowledge receipt of the webhook
    return NextResponse.json({ success: true });
  } catch (error) {
    // Log the error but still return 200 to prevent webhook retries
    logger.error('Error processing KIE webhook', error, {}, 
      { filePath: "app/api/ai/webhooks/kie/image-generated/route.ts", functionName: 'POST' });
    return NextResponse.json(
      { success: false, error: 'Internal server error processing webhook' },
      { status: 200 } // Still return 200 to acknowledge receipt
    );
  }
}
