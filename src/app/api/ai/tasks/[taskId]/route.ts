/**
 * API endpoint for checking task status with associated files
 * Used by frontend to poll for task completion, results, and generated files
 */
import { NextRequest, NextResponse } from "next/server";
import { nextRespData, nextRespJson, respData, respErr } from "@/lib/resp";
import { getTask } from "@/services/task";
import { getTaskFiles } from "@/services/file";
import { FileCategory } from "@/types/file";
import logger from "@/lib/logger";

/**
 * GET handler for task status and files
 *
 * Returns the current status, progress, results, and associated files for a task
 * This replaces the old approach of storing file URLs in task.results JSON
 * Now file information comes from the dedicated files table
 *
 * @param req The incoming request
 * @param params Route parameters containing taskId
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ taskId: string }> }
): Promise<NextResponse> {
  let taskId: string | undefined;
  try {
    // Extract taskId from params
    const resolvedParams = await params;
    taskId = resolvedParams.taskId;

    // Log the request
    logger.info("Task status request with files:", { taskId }, 
      { filePath: "app/api/ai/tasks/[taskId]/route.ts", functionName: 'GET' });

    // Get the task data
    const task = await getTask(taskId);

    if (!task) {
      return NextResponse.json(respErr("Task not found"));
    }

    // If the request includes an authorization header with a user UUID,
    // verify it matches the task owner for simple authorization
    const authHeader = req.headers.get("authorization");
    if (authHeader && authHeader.startsWith("Bearer ")) {
      const userUuid = authHeader.substring(7);
      if (task.user_uuid !== userUuid) {
        logger.warn("Unauthorized task access attempt", 
          { taskId, userUuid }, 
          { filePath: "app/api/ai/tasks/[taskId]/route.ts", functionName: 'GET' });
        return NextResponse.json(respErr("Unauthorized access to task"));
      }
    }

    // Fetch only task output files (generated files), not user uploads
    const files = await getTaskFiles(taskId, FileCategory.TASK_OUTPUT);

    // Format task data for client with integrated file information
    const taskData = {
      taskId: task.task_id,
      taskType: task.task_type,
      status: task.status,
      createdAt: task.created_at,
      completedAt: task.completed_at,
      progress: task.progress || 0,
      error: task.error,
      
      // Generated output files from the new file management system
      files: files,
      filesCount: files.length,
      
      // Task metadata (excludes file URLs which are managed separately)
      // Contains generation parameters and processing information
      results: task.results ? {
        generated_count: files.length, // Count based on actual generated files
        processing_time_seconds: task.results.processing_time_seconds,
        generation_params: task.results.generation_params,
        provider_response: task.results.provider_response
      } : undefined
    };
    
    logger.info("Task data with output files retrieved", { 
      taskId, 
      outputFilesCount: files.length,
      status: task.status 
    }, { filePath: "app/api/ai/tasks/[taskId]/route.ts", functionName: 'GET' });
    
    return nextRespData(taskData);
  } catch (error) {
    logger.error("Error fetching task status with files", 
      { error, taskId }, 
      { filePath: "app/api/ai/tasks/[taskId]/route.ts", functionName: 'GET' });
    return NextResponse.json(respErr("Failed to get task status"));
  }
}
