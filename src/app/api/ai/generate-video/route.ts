import { respData, respErr } from "@/lib/resp";
import {
  VideoGenerationMode,
  VideoGenerationRequest,
} from "@/types/ai/video-gen-types";
import { createTask } from "@/services/task";
import { TaskTypes } from "@/types/task";
import logger from "@/lib/logger";
import { NgrokWebHook } from "@/lib/ngrok-webhook";
import { getFileRecord, updateFileByTaskId, getFileAccessUrl } from "@/services/file";
import Kling from "@/aisdk/model-providers/kling";

/**
 * API route handler for video generation
 * 
 * Creates an asynchronous task for video generation and returns the task ID immediately.
 * Uses Kling AI as the video generation provider.
 * The actual generation is performed in the background, and results are delivered via webhook.
 * 
 * Features:
 * - Input validation
 * - Task creation and tracking
 * - Image upload for image-to-video mode
 * - Webhook configuration for async completion notification
 */
export async function POST(req: Request) {
  try {
    // Get user UUID from request headers or use 'anonymous'
    const userUuid = req.headers.get('x-user-uuid');
    if (!userUuid) {
      return respErr("User Login is required");
    }
    
    // Parse and validate the request body from frontend
    const videoGenRequest = (await req.json()) as VideoGenerationRequest;

    // Log request details for debugging (excluding sensitive data)
    logger.info("Video generation request:", {
      ...videoGenRequest,
      // Don't log the file ID for security
      fileId: videoGenRequest.fileId ? "[FILE_ID]" : undefined
    }, 
    { filePath: "app/api/ai/generate-video/route.ts", functionName: 'POST' });

    // Prompt is always required
    if (!videoGenRequest.prompt || videoGenRequest.prompt.trim() === "") {
      return respErr("Prompt is required");
    }

    // For image-to-video mode, an image source is required
    if (videoGenRequest.mode === VideoGenerationMode.ImageToVideo) {
      const fileId = videoGenRequest.fileId;
      if (!fileId) {
        return respErr("Image is required for image-to-video generation (provide fileId)");
      }
    }
    
    try {
      // Handle image source for image-to-video mode
      let imageUrl: string | undefined;
      
      if (videoGenRequest.mode === VideoGenerationMode.ImageToVideo) {
        // Use fileId from presigned URL upload
        if (videoGenRequest.fileId) {
          console.log("Using pre-uploaded file for video generation", { 
            fileId: videoGenRequest.fileId
          });
          
          try {
            const fileRecord = await getFileRecord(videoGenRequest.fileId);
            
            if (!fileRecord) {
              return respErr(`File not found: ${videoGenRequest.fileId}`);
            }
            
            if (fileRecord.user_uuid !== userUuid) {
              return respErr(`File access denied: ${videoGenRequest.fileId}`);
            }
            
            // Get access URL for the file (handles both public and private files)
            imageUrl = await getFileAccessUrl(videoGenRequest.fileId) || undefined;
            
            logger.info("Source file validated for video generation", {
              fileId: videoGenRequest.fileId,
              fileUrl: imageUrl
            }, { filePath: "app/api/ai/generate-video/route.ts", functionName: 'POST' });
            
          } catch (error) {
            logger.error("Failed to retrieve source file", error, { fileId: videoGenRequest.fileId });
            return respErr("Failed to retrieve source file");
          }
        }
      }
      
      // Select the model based on parameters
      // Currently we always use "kling-v1-6" as it's the primary available model
      const modelId = "kling-v1-6";
      const modelInfo = Kling.Models[modelId];
      
      // Create a new task for this video generation request
      const taskId = await createTask({
        userUuid,
        taskType: TaskTypes.VIDEO,
        params: {
          ...videoGenRequest,
          imageUrl
        },
        modelId: modelInfo.id,
        provider: modelInfo.provider
      });
      
      // Generate webhook URL for async completion notification
      const webhookPath = `/api/ai/webhooks/kling/video-generated?taskId=${taskId}`;
      const webhookUrl = await NgrokWebHook.getWebhookUrl(webhookPath);
      logger.info("Generated webhook URL", 
        { taskId, webhookUrl }, 
        { filePath: "app/api/ai/generate-video/route.ts", functionName: 'POST' });
      
      /**
       * TODO: 暂时移除了实际的生成逻辑，直接返回taskId
       * kling的服务不好用，以后需要实现视频生成时，再考虑选择和实现。
       */
      console.log("=========================================================");
      logger.warn("Video generation is not implemented yet, returning taskId", { taskId }, 
      { filePath: "app/api/ai/generate-video/route.ts", functionName: 'POST' });
      console.log("=========================================================");

      // Return the task ID immediately
      return respData({ taskId });
    } catch (error) {
      logger.error("Error generating video:", { error }, 
        { filePath: "app/api/ai/generate-video/route.ts", functionName: 'POST' });
      return respErr("Failed to generate video");
    }
  } catch (error) {
    // Handle general request parsing errors
    logger.error("Error processing request:", { error }, 
      { filePath: "app/api/ai/generate-video/route.ts", functionName: 'POST' });
    return respErr("Generate video failed");
  }
}
