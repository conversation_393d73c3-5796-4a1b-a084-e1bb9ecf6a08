import { notFound } from 'next/navigation'
import { setRequestLocale } from 'next-intl/server'
import { Mdx } from '@/components/mdx'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import Image from 'next/image'
import { Metadata } from 'next'
import ContentLanguageIndicator from '@/components/locale/content-language-indicator'
import { getAllContentSlugs, getContent, type ProductContent, type SupportedLocale } from '@/services/content'

export async function generateStaticParams() {
  const slugs = await getAllContentSlugs('product')
  return slugs.map(({ locale, slug }) => ({
    locale,
    slug
  }))
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string; slug: string }>
}): Promise<Metadata> {
  const { locale, slug } = await params
  
  const product = await getContent<ProductContent>('product', slug, locale as SupportedLocale)

  if (!product) {
    return {
      title: 'Product Not Found',
    }
  }

  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/products/${slug}`

  if (locale !== 'en') {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/products/${slug}`
  }

  return {
    title: product.title,
    description: product.description,
    alternates: {
      canonical: canonicalUrl,
    },
  }
}

export default async function ProductPage({
  params,
}: {
  params: Promise<{ locale: string; slug: string }>
}) {
  const { locale, slug } = await params
  setRequestLocale(locale)

  const product = await getContent<ProductContent>('product', slug, locale as SupportedLocale)

  if (!product) {
    notFound()
  }

  return (
    <section className="py-16">
      <div className="container">
        <div className="mx-auto max-w-4xl">
          {/* Header */}
          <div className="mb-8">
            <div className="mb-4 flex items-center justify-between">
              <div className="flex items-center gap-2">
                {product.featured && (
                  <Badge variant="secondary">Featured</Badge>
                )}
                {product.tags && product.tags.map((tag) => (
                  <Badge key={tag} variant="outline">
                    {tag}
                  </Badge>
                ))}
              </div>
              <ContentLanguageIndicator variant="compact" />
            </div>
            
            <h1 className="mb-4 text-4xl font-bold tracking-tight">
              {product.title}
            </h1>
            
            {product.description && (
              <p className="text-xl text-muted-foreground">
                {product.description}
              </p>
            )}

            {(product.author || product.publishedAt) && (
              <div className="mt-6 flex items-center gap-4 text-sm text-muted-foreground">
                {product.author && (
                  <div className="flex items-center gap-2">
                    {product.authorImage && (
                      <Image
                        src={product.authorImage}
                        alt={product.author}
                        width={24}
                        height={24}
                        className="rounded-full"
                      />
                    )}
                    <span>By {product.author}</span>
                  </div>
                )}
                {product.publishedAt && (
                  <time dateTime={product.publishedAt}>
                    {new Date(product.publishedAt).toLocaleDateString(locale, {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                    })}
                  </time>
                )}
              </div>
            )}
          </div>

          {/* Cover Image */}
          {product.coverImage && (
            <div className="mb-8">
              <Image
                src={product.coverImage}
                alt={product.title}
                width={800}
                height={400}
                className="w-full rounded-lg border object-cover"
              />
            </div>
          )}

          {/* Content */}
          <Card>
            <CardContent className="p-8">
              <Mdx content={product} />
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
