/**
 * File Model
 * 
 * @description
 * This model provides a type-safe API for all direct database interactions 
 * with the `files` table. All functions handle their own error logging.
 *
 * @see services/file-service.ts - The service layer that consumes this model.
 */
import { db } from "@/db";
import { files } from "@/db/schema";
import { eq, and, lt, not, isNull, desc, asc, inArray } from "drizzle-orm";
import logger from "@/lib/logger";
import { 
  FileRecord, 
  FileCreationData,
  FileStatus,
  StorageStats,
  AccessLevel,
  FileCategory,
  TaskFileQueryCategory,
} from "@/types/file";

/**
 * Creates a new file record in the database with explicit timestamp setting
 * 
 * @param {FileCreationData} fileData - The data for the new file record
 * @returns {Promise<FileRecord>} The created file record with normalized fields
 * @throws {Error} If the database operation fails
 * 
 * @description
 * Creates a new file record with ACTIVE status and explicit created_at timestamp.
 * Uses unified timezone management to ensure consistency with database defaults.
 * Normalizes bigint file_size to number and formats timestamps as ISO strings.
 * 
 * @example
 * const fileRecord = await createFile({
 *   file_id: 'file_123',
 *   user_uuid: 'user_456',
 *   storage_key: 'uploads/2024/01/file.jpg',
 *   file_name: 'image.jpg',
 *   mime_type: 'image/jpeg',
 *   file_size: 1024000,
 *   file_category: FileCategory.USER_UPLOADS,
 *   access_level: AccessLevel.PRIVATE
 * });
 * 
 * @note
 * - Always sets status to ACTIVE
 * - Uses unified timezone for created_at consistency
 * - Converts bigint file_size to number for client compatibility
 */
export async function createFile(fileData: FileCreationData): Promise<FileRecord> {
  try {
    const database = db();
    
    const { expires_at, ...restFileData } = fileData;
    
    const insertData = {
      ...restFileData,
      status: FileStatus.ACTIVE,
      created_at: new Date(),
      ...(expires_at && { expires_at: new Date(expires_at) }),
    };
    
    const result = await database
      .insert(files)
      .values(insertData)
      .returning();
    
    if (result.length === 0) {
      throw new Error('Failed to create file record');
    }
    
    const fileRecord = result[0];
    logger.info('Successfully created file record', { fileId: fileRecord.file_id });
    
    // Normalize response format for client compatibility
    return {
      ...fileRecord,
      file_size: Number(fileRecord.file_size), // Convert bigint to number
      created_at: fileRecord.created_at.toISOString(),
      expires_at: fileRecord.expires_at?.toISOString() || null,
    } as FileRecord;

  } catch (error) {
    logger.error('Failed to create file record in database', error, { fileId: fileData.file_id });
    throw error;
  }
}

/**
 * Retrieves a single, active file record by its unique file ID.
 * @param fileId The unique file identifier.
 * @returns The file record or null if not found or inactive.
 */
export async function getFileById(fileId: string): Promise<FileRecord | null> {
  try {
    const database = db();
    const result = await database
      .select()
      .from(files)
      .where(and(
        eq(files.file_id, fileId),
        eq(files.status, FileStatus.ACTIVE)
      ))
      .limit(1);
    
    if (result.length === 0) return null;
    
    const fileRecord = result[0];
    return {
      ...fileRecord,
      file_size: Number(fileRecord.file_size),
      created_at: fileRecord.created_at.toISOString(),
      expires_at: fileRecord.expires_at?.toISOString() || null,
    } as FileRecord;

  } catch (error) {
    logger.error('Failed to retrieve file from database', error, { fileId });
    return null;
  }
}

/**
 * Retrieves a single file record by its ID and a specific status.
 * @param fileId The unique file identifier.
 * @param status The lifecycle status to filter by.
 * @returns The file record or null if not found.
 */
export async function getFileByIdAndStatus(fileId: string, status: FileStatus): Promise<FileRecord | null> {
  try {
    const database = db();
    const result = await database
      .select()
      .from(files)
      .where(and(
        eq(files.file_id, fileId),
        eq(files.status, status)
      ))
      .limit(1);
    
    if (result.length === 0) return null;
    
    const fileRecord = result[0];
    return {
      ...fileRecord,
      file_size: Number(fileRecord.file_size),
      created_at: fileRecord.created_at.toISOString(),
      expires_at: fileRecord.expires_at?.toISOString() || null,
    } as FileRecord;

  } catch (error) {
    logger.error('Failed to retrieve file by ID and status', error, { fileId, status });
    return null;
  }
}

/**
 * Retrieves active files associated with a specific task, optionally filtered by category.
 * @param taskId The task ID to filter by.
 * @param category Category filter. Only accepts 'all', FileCategory.USER_UPLOADS, or FileCategory.TASK_OUTPUT.
 * @returns An array of active file records for the task.
 * @throws An error if the database query fails.
 */
export async function getActiveTaskFiles(
  taskId: string, 
  category: TaskFileQueryCategory = 'all'
): Promise<FileRecord[]> {
  try {
    const database = db();
    let conditions = [
      eq(files.task_id, taskId),
      eq(files.status, FileStatus.ACTIVE)
    ];
    
    // Add category filter if not 'all'
    if (category !== 'all') {
      conditions.push(eq(files.file_category, category));
    }
    
    const result = await database
      .select()
      .from(files)
      .where(and(...conditions))
      .orderBy(asc(files.created_at));
    
    return result.map(fileRecord => ({
      ...fileRecord,
      file_size: Number(fileRecord.file_size),
      created_at: fileRecord.created_at.toISOString(),
      expires_at: fileRecord.expires_at?.toISOString() || null,
    })) as FileRecord[];

  } catch (error) {
    logger.error('Failed to retrieve active task files', error, { taskId, category });
    throw error;
  }
}

/**
 * Retrieves a paginated list of a user's active files, optionally filtered by category.
 * @param userUuid The user's UUID.
 * @param category Optional category to filter by.
 * @param limit Number of files per page.
 * @param offset Number of files to skip.
 * @returns An array of the user's file records.
 * @throws An error if the database query fails.
 */
export async function getActiveUserFilesPaginated(
  userUuid: string, 
  category?: FileCategory,
  limit: number = 20,
  offset: number = 0
): Promise<FileRecord[]> {
  try {
    const database = db();
    let conditions = [
      eq(files.user_uuid, userUuid),
      eq(files.status, FileStatus.ACTIVE)
    ];
    
    if (category) {
      conditions.push(eq(files.file_category, category));
    }
    
    const result = await database
      .select()
      .from(files)
      .where(and(...conditions))
      .orderBy(desc(files.created_at))
      .limit(limit)
      .offset(offset);
    
    return result.map(fileRecord => ({
      ...fileRecord,
      file_size: Number(fileRecord.file_size),
      created_at: fileRecord.created_at.toISOString(),
      expires_at: fileRecord.expires_at?.toISOString() || null,
    })) as FileRecord[];

  } catch (error) {
    logger.error('Failed to retrieve paginated user files', error, { userUuid, category, limit, offset });
    throw error;
  }
}

/**
 * Retrieves all active files for a specific user (used for deletion operations).
 * @param userUuid The user's UUID.
 * @returns An array of the user's active file records.
 * @throws An error if the database query fails.
 */
export async function getActiveUserFilesByUuid(userUuid: string): Promise<FileRecord[]> {
  try {
    const database = db();
    const result = await database
      .select()
      .from(files)
      .where(and(
        eq(files.user_uuid, userUuid),
        eq(files.status, FileStatus.ACTIVE)
      ));
    
    return result.map(fileRecord => ({
      ...fileRecord,
      file_size: Number(fileRecord.file_size),
      created_at: fileRecord.created_at.toISOString(),
      expires_at: fileRecord.expires_at?.toISOString() || null,
    })) as FileRecord[];

  } catch (error) {
    logger.error('Failed to retrieve active user files by UUID', error, { userUuid });
    throw error;
  }
}

/**
 * Retrieves pending files that are older than a specified cutoff time (used for cleanup operations).
 * @param cutoffTime ISO 8601 timestamp - files created before this time will be returned.
 * @returns An array of pending file records older than the cutoff time.
 * @throws An error if the database query fails.
 */
export async function getPendingFilesOlderThan(cutoffTime: string): Promise<FileRecord[]> {
  try {
    const database = db();
    const result = await database
      .select()
      .from(files)
      .where(and(
        eq(files.status, FileStatus.PENDING),
        lt(files.created_at, new Date(cutoffTime))
      ));
    
    return result.map(fileRecord => ({
      ...fileRecord,
      file_size: Number(fileRecord.file_size),
      created_at: fileRecord.created_at.toISOString(),
      expires_at: fileRecord.expires_at?.toISOString() || null,
    })) as FileRecord[];

  } catch (error) {
    logger.error('Failed to retrieve pending files older than cutoff', error, { cutoffTime });
    throw error;
  }
}

/**
 * Updates a file's status (for soft deletion, expiration, etc.).
 * @param fileId The unique ID of the file to update.
 * @param status The new status.
 * @returns The updated file record.
 * @throws An error if the file is not found or if the database update fails.
 */
export async function updateFileStatus(
  fileId: string, 
  status: FileStatus
): Promise<FileRecord> {
  try {
    const database = db();
    const result = await database
      .update(files)
      .set({ status })
      .where(eq(files.file_id, fileId))
      .returning();
    
    if (result.length === 0) {
      throw new Error('File not found');
    }
    
    const fileRecord = result[0];
    logger.info('Successfully updated file status', { fileId, status });
    return {
      ...fileRecord,
      file_size: Number(fileRecord.file_size),
      created_at: fileRecord.created_at.toISOString(),
      expires_at: fileRecord.expires_at?.toISOString() || null,
    } as FileRecord;

  } catch (error) {
    logger.error('Failed to update file status', error, { fileId, status });
    throw error;
  }
}

/**
 * Soft deletes a file by changing its status to DELETED.
 * @param fileId The unique ID of the file to delete.
 * @returns True if successful, false otherwise.
 */
export async function softDeleteFile(fileId: string): Promise<boolean> {
  try {
    await updateFileStatus(fileId, FileStatus.DELETED);
    return true;
  } catch (error) {
    logger.error('Failed to soft delete file', error, { fileId });
    return false;
  }
}

/**
 * Retrieves files that have passed their expiration time.
 * @returns An array of expired file records.
 * @throws An error if the database query fails.
 */
/**
 * Retrieves files that have passed their expiration time
 * 
 * @returns {Promise<FileRecord[]>} Array of expired file records
 * @throws {Error} If the database query fails
 * 
 * @description
 * Finds all ACTIVE files where expires_at is not null and is before current time.
 * Used by cleanup services to identify files for removal.
 * Uses unified timezone for consistent time comparison.
 * 
 * @example
 * const expiredFiles = await getExpiredFiles();
 * for (const file of expiredFiles) {
 *   await cleanupExpiredFile(file.file_id);
 * }
 * 
 * @note
 * - Only returns ACTIVE files (not already deleted)
 * - Compares expires_at against current unified timestamp
 * - Returns normalized file records with ISO timestamp strings
 */
export async function getExpiredFiles(): Promise<FileRecord[]> {
  try {
    const database = db();
    
    const result = await database
      .select()
      .from(files)
      .where(and(
        eq(files.status, FileStatus.ACTIVE),
        not(isNull(files.expires_at)),
        lt(files.expires_at, new Date())
      ));
    
    // Normalize response format for client compatibility
    return result.map(fileRecord => ({
      ...fileRecord,
      file_size: Number(fileRecord.file_size),
      created_at: fileRecord.created_at.toISOString(),
      expires_at: fileRecord.expires_at?.toISOString() || null,
    })) as FileRecord[];

  } catch (error) {
    logger.error('Failed to retrieve expired files', error);
    throw error;
  }
}

/**
 * Calculates storage statistics for a user.
 * @param userUuid The user's UUID.
 * @returns An object with total usage and breakdown by category.
 * @throws An error if the database query fails.
 */
export async function getUserStorageStats(userUuid: string): Promise<StorageStats> {
  try {
    const database = db();
    const result = await database
      .select({
        file_category: files.file_category,
        file_size: files.file_size
      })
      .from(files)
      .where(and(
        eq(files.user_uuid, userUuid),
        eq(files.status, FileStatus.ACTIVE)
      ));
    
    const stats: StorageStats = {
      total_files: result.length,
      total_size: 0,
      by_category: {} as Record<FileCategory, { count: number; size: number }>
    };
    
    result.forEach(file => {
      const size = Number(file.file_size);
      stats.total_size += size;
      
      if (file.file_category) {
        const category = file.file_category as FileCategory;
        if (!stats.by_category[category]) {
          stats.by_category[category] = { count: 0, size: 0 };
        }
        stats.by_category[category].count++;
        stats.by_category[category].size += size;
      }
    });
    
    return stats;
  } catch (error) {
    logger.error('Failed to calculate user storage stats', error, { userUuid });
    throw error;
  }
}

/**
 * Associates an existing file record with a task ID.
 * @param fileId The unique ID of the file to update.
 * @param taskId The task ID to associate.
 * @returns The updated file record.
 * @throws An error if the file is not found or if the database update fails.
 */
export async function updateFileTaskId(
  fileId: string, 
  taskId: string
): Promise<FileRecord> {
  try {
    const database = db();
    const result = await database
      .update(files)
      .set({ task_id: taskId })
      .where(eq(files.file_id, fileId))
      .returning();
    
    if (result.length === 0) {
      throw new Error('File not found');
    }
    
    const fileRecord = result[0];
    logger.info('Successfully updated file task ID', { fileId, taskId });
    return {
      ...fileRecord,
      file_size: Number(fileRecord.file_size),
      created_at: fileRecord.created_at.toISOString(),
      expires_at: fileRecord.expires_at?.toISOString() || null,
    } as FileRecord;

  } catch (error) {
    logger.error('Failed to update file task ID', error, { fileId, taskId });
    throw error;
  }
}

/**
 * Creates a 'pending' file record for the presigned upload flow.
 * @param fileData The data for the pending file.
 * @returns The created file record with 'pending' status.
 * @throws An error if the database operation fails.
 */
/**
 * Creates a pending file record for the presigned upload flow
 * 
 * @param {Object} fileData - The data for the pending file record
 * @param {string} fileData.file_id - Unique file identifier
 * @param {string} fileData.user_uuid - Owner user UUID
 * @param {string} fileData.storage_key - Cloud storage object key
 * @param {string} fileData.file_name - Original filename
 * @param {string} fileData.mime_type - File MIME type
 * @param {number} fileData.file_size - File size in bytes
 * @param {FileCategory} fileData.file_category - File category classification
 * @param {AccessLevel} fileData.access_level - Access control level
 * @param {Record<string, any>} [fileData.metadata] - Optional metadata
 * @returns {Promise<FileRecord>} The created pending file record
 * @throws {Error} If the database operation fails
 * 
 * @description
 * Creates a file record with PENDING status for presigned upload workflow.
 * The file will be activated after successful upload confirmation.
 * Uses unified timezone management for timestamp consistency.
 * 
 * @example
 * const pendingFile = await createPendingFile({
 *   file_id: 'file_123',
 *   user_uuid: 'user_456',
 *   storage_key: 'temp/uploads/file.jpg',
 *   file_name: 'photo.jpg',
 *   mime_type: 'image/jpeg',
 *   file_size: 2048000,
 *   file_category: FileCategory.USER_UPLOADS,
 *   access_level: AccessLevel.PRIVATE
 * });
 * 
 * @note
 * - Sets status to PENDING (not ACTIVE)
 * - Used in presigned upload flow
 * - Should be activated after successful upload
 */
export async function createPendingFile(fileData: {
  file_id: string;
  user_uuid: string;
  storage_key: string;
  file_name: string;
  mime_type: string;
  file_size: number;
  file_category: FileCategory;
  access_level: AccessLevel;
  metadata?: Record<string, any>;
}): Promise<FileRecord> {
  try {
    const database = db();
    const insertData = {
      ...fileData,
      status: FileStatus.PENDING,
      created_at: new Date(),
    };
    
    const result = await database
      .insert(files)
      .values(insertData)
      .returning();
    
    if (result.length === 0) {
      throw new Error('Failed to create pending file record');
    }
    
    const fileRecord = result[0];
    logger.info('Successfully created pending file record', { fileId: fileData.file_id });
    
    // Normalize response format for client compatibility
    return {
      ...fileRecord,
      file_size: Number(fileRecord.file_size),
      created_at: fileRecord.created_at.toISOString(),
      expires_at: fileRecord.expires_at?.toISOString() || null,
    } as FileRecord;

  } catch (error) {
    logger.error('Failed to create pending file record', error, { fileId: fileData.file_id });
    throw error;
  }
}

/**
 * Updates the access level of a file.
 * @param fileId The unique ID of the file to update.
 * @param accessLevel The new access level.
 * @returns The updated file record.
 * @throws An error if the file is not found or if the database update fails.
 */
export async function updateFileAccessLevel(
  fileId: string,
  accessLevel: AccessLevel
): Promise<FileRecord> {
  try {
    const database = db();
    const result = await database
      .update(files)
      .set({ access_level: accessLevel })
      .where(eq(files.file_id, fileId))
      .returning();
      
    if (result.length === 0) {
      throw new Error('File not found');
    }
    
    const fileRecord = result[0];
    logger.info('Successfully updated file access level', { fileId, accessLevel });
    return {
      ...fileRecord,
      file_size: Number(fileRecord.file_size),
      created_at: fileRecord.created_at.toISOString(),
      expires_at: fileRecord.expires_at?.toISOString() || null,
    } as FileRecord;

  } catch (error) {
    logger.error('Failed to update file access level', error, { fileId });
    throw error;
  }
}

/**
 * Permanently deletes a file record from the database by its primary key (id).
 * This is an irreversible operation. It's designed for internal, high-performance
 * operations and should be called by a service layer that has already authorized
 * the action.
 *
 * @param id The numeric primary key of the file record.
 * @returns True if deletion was successful, false otherwise.
 */
export async function hardDeleteFileById(id: number): Promise<boolean> {
  try {
    const database = db();
    await database
      .delete(files)
      .where(eq(files.id, id));
      
    logger.info('Successfully hard-deleted file record', { id });
    return true;

  } catch (error) {
    logger.error('Failed to hard-delete file record by ID', error, { id });
    return false;
  }
}

/**
 * Retrieves a minimal set of file record details needed for a hard delete operation.
 * This function serves as the bridge between business-facing `file_id`s and
 * internal database `id`s, allowing the service layer to fetch necessary data
 * before committing to a destructive operation.
 *
 * @param fileIds An array of unique file_id strings.
 * @returns An array of objects containing id and storage_key, or null on error.
 */
export async function getMinimalFileRecords(fileIds: string[]): Promise<{ id: number; storage_key: string }[] | null> {
  try {
    const database = db();
    const result = await database
      .select({
        id: files.id,
        storage_key: files.storage_key
      })
      .from(files)
      .where(inArray(files.file_id, fileIds));

    return result;

  } catch (error) {
    logger.error('Failed to fetch minimal records for hard deletion', error, { fileIdsCount: fileIds.length });
    return null;
  }
}

/**
 * Permanently deletes multiple file records from the database by their primary keys.
 * This is an irreversible, high-performance batch operation. It uses the primary key (`id`)
 * directly for maximum efficiency, as it avoids a secondary index lookup that would
 * occur if using `file_id`.
 *
 * @param ids An array of numeric primary keys.
 * @returns True if the operation was successful, false otherwise.
 */
export async function hardDeleteFilesByIds(ids: number[]): Promise<boolean> {
  try {
    const database = db();
    
    await database
      .delete(files)
      .where(inArray(files.id, ids));
    
    logger.info(`Successfully hard-deleted ${ids.length} file records.`);
    return true;

  } catch (error) {
    logger.error('Failed to hard-delete file records from database by IDs', error, { idsCount: ids.length });
    return false;
  }
}
