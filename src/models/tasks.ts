/**
 * Tasks model
 * Handles database operations for task records
 */
import { db } from "@/db";
import { tasks } from "@/db/schema";
import { eq, desc } from "drizzle-orm";
import logger from "@/lib/logger";
import { 
  TaskStatus, 
  TaskStatusType, 
  TaskType, 
  TaskTypes,
  Task, 
  TaskCreationData, 
  TaskResults,
  TextItem 
} from "@/types/task";

/**
 * Create a new task record in the database
 * 
 * @param {TaskCreationData} taskData - The task data to create
 * @returns {Promise<string>} The created task ID
 * @throws {Error} If the database operation fails
 * 
 * @description
 * Creates a new task with explicit timestamp setting to ensure timezone consistency.
 * Both created_at and updated_at are set using unified timezone management.
 * 
 * @example
 * const taskId = await createTask({
 *   task_id: 'task_123',
 *   user_uuid: 'user_456',
 *   task_type: 'image',
 *   status: 'pending',
 *   params: { prompt: 'test' },
 *   model_id: 'gpt4o',
 *   provider: 'openai'
 * });
 */
export async function createTask(taskData: TaskCreationData): Promise<string> {
  try {
    const database = db();
    
    // Use unified timestamp to ensure timezone consistency with database defaults
    const now = new Date();
    
    const result = await database
      .insert(tasks)
      .values({
        task_id: taskData.task_id,
        user_uuid: taskData.user_uuid,
        task_type: taskData.task_type,
        status: taskData.status,
        params: taskData.params,
        model_id: taskData.model_id,
        provider: taskData.provider,
        provider_task_id: taskData.provider_task_id || null,
        created_at: now,  // Explicit timestamp setting for timezone consistency
        updated_at: now   // Explicit timestamp setting for timezone consistency
      })
      .returning({ task_id: tasks.task_id });
    
    if (result.length === 0) {
      throw new Error('Failed to create task record');
    }
    
    logger.info('Successfully created task', 
      { taskId: taskData.task_id, userUuid: taskData.user_uuid }, 
      { filePath: "models/tasks.ts", functionName: 'createTask' });
    return result[0].task_id;
  } catch (error) {
    logger.error('Failed to create task', error, 
      { taskId: taskData.task_id }, 
      { filePath: "models/tasks.ts", functionName: 'createTask' });
    throw error;
  }
}

/**
 * Get a task by its ID
 * @param taskId The task ID to look up
 * @returns The task data or null if not found
 */
export async function getTask(taskId: string): Promise<Task | null> {
  try {
    const database = db();
    const result = await database
      .select()
      .from(tasks)
      .where(eq(tasks.task_id, taskId))
      .limit(1);
    
    if (result.length === 0) {
      return null;
    }
    
    const task = result[0];
    return {
      task_id: task.task_id,
      user_uuid: task.user_uuid,
      task_type: task.task_type,
      status: task.status,
      params: task.params,
      model_id: task.model_id,
      provider: task.provider,
      provider_task_id: task.provider_task_id,
      created_at: task.created_at.toISOString(),
      updated_at: task.updated_at.toISOString(),
      completed_at: task.completed_at?.toISOString() || null,
      progress: task.progress || 0,
      results: task.results,
      error: task.error
    } as Task;
  } catch (error) {
    logger.error('Failed to get task', error, { taskId }, 
      { filePath: "models/tasks.ts", functionName: 'getTask' });
    throw error;
  }
}

/**
 * Update the provider's task ID for an existing task
 * @param taskId The task ID to update
 * @param providerTaskId The provider's task ID
 */
export async function updateProviderTaskId(
  taskId: string, 
  providerTaskId: string
): Promise<void> {
  try {
    const database = db();
    await database
      .update(tasks)
      .set({
        provider_task_id: providerTaskId,
        updated_at: new Date()
      })
      .where(eq(tasks.task_id, taskId));
    
    logger.info('Updated provider task ID', 
      { taskId, providerTaskId }, 
      { filePath: "models/tasks.ts", functionName: 'updateProviderTaskId' });
  } catch (error) {
    logger.error('Failed to update provider task ID', error, 
      { taskId, providerTaskId }, 
      { filePath: "models/tasks.ts", functionName: 'updateProviderTaskId' });
    throw error;
  }
}

/**
 * Update task status and progress
 * 
 * @param {string} taskId - The task ID to update
 * @param {TaskStatusType} status - The new status value
 * @param {number} progress - The current progress (0-100), defaults to 0
 * @returns {Promise<void>}
 * @throws {Error} If the database operation fails
 * 
 * @description
 * Updates the task status and progress, automatically setting updated_at timestamp
 * using unified timezone management for consistency.
 * 
 * @example
 * await updateTaskStatus('task_123', TaskStatus.PROCESSING, 50);
 */
export async function updateTaskStatus(
  taskId: string, 
  status: TaskStatusType,
  progress: number = 0
): Promise<void> {
  try {
    const database = db();
    await database
      .update(tasks)
      .set({
        status: status,
        progress: progress,
        updated_at: new Date()
      })
      .where(eq(tasks.task_id, taskId));
    
    logger.info('Successfully updated task status', 
      { taskId, status, progress }, 
      { filePath: "models/tasks.ts", functionName: 'updateTaskStatus' });
  } catch (error) {
    logger.error('Failed to update task status', error, 
      { taskId, status }, 
      { filePath: "models/tasks.ts", functionName: 'updateTaskStatus' });
    throw error;
  }
}

/**
 * Update the completion timestamp for a finished task
 * 
 * @param {string} taskId - The task ID to update
 * @returns {Promise<void>}
 * @throws {Error} If the database operation fails
 * 
 * @description
 * Sets the completed_at timestamp and updates updated_at for task completion.
 * Uses unified timezone management to ensure consistency with other timestamps.
 * 
 * @example
 * await updateCompletedAt('task_123');
 * 
 * @note
 * This function should be called when a task transitions to completed status
 */
export async function updateCompletedAt(taskId: string): Promise<void> {
  try {
    const database = db();
    const now = new Date()
    
    await database
      .update(tasks)
      .set({
        completed_at: now,  // Mark completion time
        updated_at: now     // Update modification time
      })
      .where(eq(tasks.task_id, taskId));
    
    logger.info('Successfully updated task completion time', 
      { taskId }, 
      { filePath: "models/tasks.ts", functionName: 'updateCompletedAt' });
  } catch (error) {
    logger.error('Failed to update task completion time', error, 
      { taskId }, 
      { filePath: "models/tasks.ts", functionName: 'updateCompletedAt' });
    throw error;
  }
}

/**
 * Get tasks for a specific user
 * @param userUuid The user UUID
 * @param limit Maximum number of tasks to return
 * @param offset Pagination offset
 * @returns Array of tasks
 */
export async function getUserTasks(
  userUuid: string,
  limit: number = 20,
  offset: number = 0
): Promise<Task[]> {
  try {
    const database = db();
    const result = await database
      .select()
      .from(tasks)
      .where(eq(tasks.user_uuid, userUuid))
      .orderBy(desc(tasks.created_at))
      .limit(limit)
      .offset(offset);
    
    return result.map(task => ({
      task_id: task.task_id,
      user_uuid: task.user_uuid,
      task_type: task.task_type,
      status: task.status,
      params: task.params,
      model_id: task.model_id,
      provider: task.provider,
      provider_task_id: task.provider_task_id,
      created_at: task.created_at.toISOString(),
      updated_at: task.updated_at.toISOString(),
      completed_at: task.completed_at?.toISOString() || null,
      progress: task.progress || 0,
      results: task.results,
      error: task.error
    })) as Task[];
  } catch (error) {
    logger.error('Failed to get user tasks', error, { userUuid }, 
      { filePath: "models/tasks.ts", functionName: 'getUserTasks' });
    throw error;
  }
}

/**
 * 通过提供商任务ID获取任务
 * @param providerTaskId 提供商的任务ID
 * @returns 对应的任务数据，如果未找到则返回null
 */
export async function getTaskByProviderTaskId(providerTaskId: string): Promise<Task | null> {
  try {
    const database = db();
    const result = await database
      .select()
      .from(tasks)
      .where(eq(tasks.provider_task_id, providerTaskId))
      .limit(1);
    
    if (result.length === 0) {
      return null;
    }
    
    const task = result[0];
    return {
      task_id: task.task_id,
      user_uuid: task.user_uuid,
      task_type: task.task_type,
      status: task.status,
      params: task.params,
      model_id: task.model_id,
      provider: task.provider,
      provider_task_id: task.provider_task_id,
      created_at: task.created_at.toISOString(),
      updated_at: task.updated_at.toISOString(),
      completed_at: task.completed_at?.toISOString() || null,
      progress: task.progress || 0,
      results: task.results,
      error: task.error
    } as Task;
  } catch (error) {
    logger.error('Failed to get task by provider task ID', 
      error, { providerTaskId }, 
      { filePath: "models/tasks.ts", functionName: 'getTaskByProviderTaskId' });
    throw error;
  }
}

/**
 * Complete a task with results metadata
 * 
 * @param {string} taskId - The task ID to complete
 * @param {TaskResults} results - Task results metadata (no file URLs)
 * @returns {Promise<void>}
 * @throws {Error} If the database operation fails
 * 
 * @description
 * Marks a task as completed by setting status, progress, results, and timestamps.
 * This is a comprehensive completion that updates all relevant fields atomically.
 * File URLs should be managed through the files table, not in results.
 * 
 * @example
 * await completeTaskWithResults('task_123', {
 *   generated_count: 2,
 *   processing_time_seconds: 45.2,
 *   model_version: 'gpt-4',
 *   generation_params: { prompt: 'test', style: 'realistic' }
 * });
 * 
 * @note
 * - Sets status to COMPLETED and progress to 100
 * - Uses unified timezone for both completed_at and updated_at
 * - Results should contain metadata only, not file URLs
 */
export async function completeTaskWithResults(
  taskId: string, 
  results: TaskResults
): Promise<void> {
  try {
    const database = db();
    const now = new Date()
    
    await database
      .update(tasks)
      .set({
        status: TaskStatus.COMPLETED,
        progress: 100,
        results,
        completed_at: now,  // Mark completion time
        updated_at: now     // Update modification time
      })
      .where(eq(tasks.task_id, taskId));
    
    logger.info('Successfully completed task with results', 
      { taskId, generatedCount: results.generated_count }, 
      { filePath: "models/tasks.ts", functionName: 'completeTaskWithResults' });
  } catch (error) {
    logger.error('Failed to complete task with results', error, 
      { taskId }, 
      { filePath: "models/tasks.ts", functionName: 'completeTaskWithResults' });
    throw error;
  }
}

/**
 * Mark a task as failed with error information
 * 
 * @param {string} taskId - The task ID to mark as failed
 * @param {string} errorMsg - The error message describing the failure
 * @returns {Promise<void>}
 * @throws {Error} If the database operation fails
 * 
 * @description
 * Sets task status to FAILED and records the error message.
 * Updates the modified timestamp using unified timezone management.
 * 
 * @example
 * await failTaskWithError('task_123', 'API request timeout after 30 seconds');
 * 
 * @note
 * - Does not set completed_at (task didn't complete successfully)
 * - Error message should be descriptive for debugging purposes
 * - Uses unified timezone for updated_at timestamp
 */
export async function failTaskWithError(
  taskId: string, 
  errorMsg: string
): Promise<void> {
  try {
    const database = db();
    
    await database
      .update(tasks)
      .set({
        status: TaskStatus.FAILED,
        error: errorMsg,
        updated_at: new Date()
      })
      .where(eq(tasks.task_id, taskId));
    
    logger.info('Successfully marked task as failed', 
      { taskId, errorMsg }, 
      { filePath: "models/tasks.ts", functionName: 'failTaskWithError' });
  } catch (error) {
    logger.error('Failed to mark task as failed', error, 
      { taskId, errorMsg }, 
      { filePath: "models/tasks.ts", functionName: 'failTaskWithError' });
    throw error;
  }
}
