{"permissions": {"allow": ["Bash(rg:*)", "Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(touch:*)", "Bash(npm run dev:test:*)", "Bash(npx tsx:*)", "Bash(npx contentlayer2 build:*)", "Bash(ls:*)", "Bash(timeout 30 npm run dev)", "Bash(pnpm build)", "Bash(pnpm lint:*)", "WebFetch(domain:www.npmjs.com)", "<PERSON><PERSON>(curl:*)", "Bash(grep:*)", "Bash(CONTENT_PROVIDER=next-mdx-remote pnpm dev)", "Bash(pnpm build:content:*)", "Bash(pnpm add:*)", "<PERSON><PERSON>(cat:*)", "Bash(pnpm list:*)", "Bash(npm run lint:*)", "Bash(npm run build:*)", "Bash(npx eslint:*)", "WebFetch(domain:docs.orama.com)"], "deny": []}}