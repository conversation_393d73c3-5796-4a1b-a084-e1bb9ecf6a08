---
title: "Getting Started with ShipAny: Build Your AI SaaS in Hours"
slug: "getting-started-with-shipany"
description: "Learn how to quickly build and deploy your AI SaaS application using ShipAny's powerful template and components."
coverImage: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=400&fit=crop"
author: "ShipAny Team"
authorImage: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face"
publishedAt: "2025-01-17"
featured: true
tags:
  - tutorial
  - getting-started
  - ai-saas
---

# Getting Started with ShipAny

Welcome to ShipAny! This comprehensive guide will walk you through building your first AI SaaS application using our powerful template system.

## What is ShipAny?

ShipAny is a Next.js boilerplate designed specifically for building AI SaaS startups quickly and efficiently. With pre-built components, authentication, payment processing, and AI integrations, you can focus on your unique value proposition rather than boilerplate code.

## Key Features

- **🚀 Rapid Development**: Pre-built components and templates
- **🤖 AI Integration**: Ready-to-use AI SDK integrations
- **💳 Payment Processing**: Stripe integration out of the box
- **🌍 Internationalization**: Multi-language support
- **📱 Responsive Design**: Mobile-first approach
- **🔐 Authentication**: Secure user management

## Quick Start

### 1. Clone the Repository

```bash
git clone https://github.com/shipany/shipany-template
cd shipany-template
```

### 2. Install Dependencies

```bash
pnpm install
```

### 3. Set Up Environment Variables

Create a `.env.local` file with your configuration:

```env
NEXT_PUBLIC_WEB_URL=http://localhost:3000
DATABASE_URL=your_database_url
NEXTAUTH_SECRET=your_secret
STRIPE_SECRET_KEY=your_stripe_key
```

### 4. Run the Development Server

```bash
pnpm dev
```

## Building Your First Feature

Let's create a simple AI-powered text generator:

### 1. Create the API Route

```typescript
// app/api/generate/route.ts
import { openai } from '@ai-sdk/openai'
import { generateText } from 'ai'

export async function POST(request: Request) {
  const { prompt } = await request.json()
  
  const { text } = await generateText({
    model: openai('gpt-3.5-turbo'),
    prompt: `Generate creative content based on: ${prompt}`,
  })
  
  return Response.json({ text })
}
```

### 2. Create the Frontend Component

```tsx
'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'

export function TextGenerator() {
  const [prompt, setPrompt] = useState('')
  const [result, setResult] = useState('')
  const [loading, setLoading] = useState(false)

  const handleGenerate = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt }),
      })
      const data = await response.json()
      setResult(data.text)
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-4">
      <Textarea
        placeholder="Enter your prompt..."
        value={prompt}
        onChange={(e) => setPrompt(e.target.value)}
      />
      <Button onClick={handleGenerate} disabled={loading}>
        {loading ? 'Generating...' : 'Generate'}
      </Button>
      {result && (
        <div className="p-4 bg-muted rounded-lg">
          {result}
        </div>
      )}
    </div>
  )
}
```

## Next Steps

Now that you have the basics set up, you can:

1. **Customize the UI**: Modify components to match your brand
2. **Add More AI Features**: Integrate additional AI models
3. **Set Up Payments**: Configure Stripe for subscriptions
4. **Deploy**: Deploy to Vercel or your preferred platform

## Conclusion

ShipAny provides everything you need to build and launch your AI SaaS quickly. With its comprehensive feature set and developer-friendly architecture, you can focus on what matters most: building great products for your users.

Ready to ship your next AI SaaS? Get started with ShipAny today!
