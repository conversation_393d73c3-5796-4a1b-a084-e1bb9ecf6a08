# 快速参考指南

## 核心命令速查

### 开发命令

```bash
pnpm dev                    # 启动开发服务器（推荐）
pnpm dev --turbo           # 使用 Turbopack（默认已启用）
```

### 内容处理命令

```bash
pnpm contentlayer build    # 处理 MDX 内容
pnpm generate:content      # 生成 SEO 文件（sitemap + rss）
pnpm generate:sitemap      # 仅生成 sitemap.xml
pnpm generate:rss          # 仅生成 RSS 订阅源
```

### 构建部署命令

```bash
pnpm build                 # 完整生产构建（推荐）
pnpm start                 # 启动生产服务器
pnpm analyze               # 分析构建包大小
```

### 数据库命令

```bash
pnpm db:generate           # 生成数据库迁移
pnpm db:migrate            # 执行数据库迁移
pnpm db:studio             # 启动数据库管理界面
pnpm db:push               # 推送数据库变更
```

### 代码质量命令

```bash
pnpm lint                  # ESLint 检查
pnpm test                  # 运行测试
```

## 工作流程速查

### 🚀 开发工作流

```bash
# 1. 启动开发环境
pnpm dev

# 2. 编辑内容（自动处理）
# - 修改 MDX 文件
# - 自动重新生成内容
# - 浏览器自动刷新

# 3. 可选：手动更新 SEO
pnpm generate:content
```

### 📝 内容更新工作流

```bash
# 1. 创建/编辑 MDX 文件
# content/blogs/en/new-post.mdx
# content/blogs/zh/xin-wen-zhang.mdx

# 2. 处理内容（开发环境自动）
pnpm contentlayer build

# 3. 更新 SEO 文件
pnpm generate:content

# 4. 构建部署
pnpm build
```

### 🚢 部署工作流

```bash
# 一键部署（推荐）
pnpm build

# 分步部署
pnpm contentlayer build # 处理内容
pnpm generate:content   # 生成 SEO 文件
next build              # 构建应用
pnpm start              # 启动服务器
```

## 文件结构速查

### 内容目录结构

```text
content/
├── blogs/
│   ├── en/              # 英文博客
│   └── zh/              # 中文博客
├── products/
│   ├── en/              # 英文产品
│   └── zh/              # 中文产品
└── case-studies/
    ├── en/              # 英文案例
    └── zh/              # 中文案例
```

### 生成文件位置

```text
.contentlayer/generated/     # Contentlayer 生成的内容索引
public/sitemap.xml          # 网站地图
public/rss.xml              # 英文 RSS 订阅
public/rss-zh.xml           # 中文 RSS 订阅
.next/                      # Next.js 构建输出
```

## MDX 文件模板

### 基础模板

```mdx
---
title: "文章标题"
slug: "url-slug"
description: "SEO 描述（150-160字符）"
coverImage: "/images/cover.jpg"
author: "作者名称"
authorImage: "/images/author.jpg"
publishedAt: "2024-01-15"
featured: false
tags: ["标签1", "标签2"]
---

# 文章标题

文章内容使用 Markdown 语法...

## 二级标题

- 列表项 1
- 列表项 2

```代码块```

![图片描述](/images/example.jpg)
```

### 必需字段

- `title`: 文章标题
- `slug`: URL 标识符

### 推荐字段

- `description`: SEO 描述
- `publishedAt`: 发布日期（YYYY-MM-DD）
- `tags`: 标签数组

## 路由规则速查

### MDX 内容路由

```text
/blogs                      # 英文博客列表
/blogs/[slug]              # 英文博客详情
/zh/blogs                  # 中文博客列表
/zh/blogs/[slug]           # 中文博客详情

/products                  # 英文产品列表
/products/[slug]           # 英文产品详情
/zh/products               # 中文产品列表
/zh/products/[slug]        # 中文产品详情

/case-studies              # 英文案例列表
/case-studies/[slug]       # 英文案例详情
/zh/case-studies           # 中文案例列表
/zh/case-studies/[slug]    # 中文案例详情
```

### 数据库内容路由（现有）

```text
/posts                     # 英文文章列表
/posts/[slug]              # 英文文章详情
/zh/posts                  # 中文文章列表
/zh/posts/[slug]           # 中文文章详情
```

## 测试速查

### 本地测试

```bash
# 启动开发服务器
pnpm dev

# 访问测试页面
http://localhost:3000/blogs
http://localhost:3000/products
http://localhost:3000/case-studies
http://localhost:3000/zh/blogs

# 检查 SEO 文件
http://localhost:3000/sitemap.xml
http://localhost:3000/rss.xml
http://localhost:3000/rss-zh.xml
```

### 构建测试

```bash
# 构建测试
pnpm build

# 启动生产服务器测试
pnpm start

# 性能分析
pnpm analyze
```

### 内容验证清单

- [ ] Frontmatter 格式正确
- [ ] 必需字段完整（title, slug）
- [ ] 图片路径有效
- [ ] 多语言版本一致
- [ ] SEO 描述长度适中（150-160字符）
- [ ] 发布日期格式正确（YYYY-MM-DD）

## 故障排除速查

### 常见问题解决

```bash
# Contentlayer 构建失败
rm -rf .contentlayer
pnpm contentlayer build

# SEO 文件未更新
pnpm generate:content

# 开发服务器问题
rm -rf .next
pnpm dev

# 依赖问题
rm -rf node_modules pnpm-lock.yaml
pnpm install

# 类型错误
pnpm contentlayer build
```

### 调试技巧

```bash
# 查看生成的内容索引
cat .contentlayer/generated/index.mjs

# 检查构建详情
pnpm build --debug

# 验证环境变量
echo $NEXT_PUBLIC_WEB_URL

# 检查文件权限
ls -la content/blogs/en/
```

## 最佳实践速查

### 开发最佳实践

1. ✅ 使用 `pnpm dev` 进行开发
2. ✅ 依赖自动内容处理
3. ✅ 定期运行 `pnpm generate:content`
4. ✅ 使用 TypeScript 类型检查

### 内容最佳实践

1. ✅ 文件名和 slug 保持一致
2. ✅ 多语言版本同步更新
3. ✅ 完善 frontmatter 元数据
4. ✅ 优化图片格式和大小

### 部署最佳实践

1. ✅ 使用 `pnpm build` 完整构建
2. ✅ 设置正确的环境变量
3. ✅ 本地测试生产构建
4. ✅ 配置适当的缓存策略

### SEO 最佳实践

1. ✅ 标题长度 50-60 字符
2. ✅ 描述长度 150-160 字符
3. ✅ 使用语义化的 slug
4. ✅ 添加相关标签和分类

## 环境变量速查

### 必需环境变量

```bash
NEXT_PUBLIC_WEB_URL=https://your-domain.com    # 网站 URL（用于 sitemap 和 RSS）
```

### 可选环境变量

```bash
NODE_ENV=production                            # 环境标识
ANALYZE=true                                   # 启用构建分析
```

## 依赖关系速查

### 核心依赖

- `contentlayer` - MDX 内容处理
- `next-contentlayer` - Next.js 集成
- `next-intl` - 国际化支持
- `@mdx-js/react` - MDX 组件支持

### 开发依赖

- `tsx` - TypeScript 执行器
- `@types/node` - Node.js 类型定义
- `cross-env` - 跨平台环境变量

## 语言切换组件速查

### ContentLanguageIndicator - 页头语言指示器

```tsx
import ContentLanguageIndicator from '@/components/locale/content-language-indicator'

// 紧凑模式（页头使用）
<ContentLanguageIndicator variant="compact" />

// 完整模式（详细信息）
<ContentLanguageIndicator variant="full" />
```

**使用场景**: 内容详情页头部、紧凑空间

### LanguageVersions - 详细语言版本组件

```tsx
import LanguageVersions from '@/components/content/language-versions'

// 完整卡片模式（侧边栏）
<LanguageVersions compact={false} />

// 紧凑模式（内容区域）
<LanguageVersions compact={true} />
```

**使用场景**: 侧边栏、内容管理界面、详细语言信息

## 内容服务速查

### 服务导入

```tsx
// 统一导入（推荐）
import {
  detectContentPage,
  handleContentLanguageSwitch,
  getAvailableLanguageVersions
} from '@/services/content'

// 默认导入
import ContentService from '@/services/content'
```

### 核心服务功能

| 服务模块 | 主要功能 | 核心函数 |
|---------|---------|----------|
| content-detection | URL 解析 | `detectContentPage()` |
| content-queries | 内容查询 | `contentExistsInLocale()` |
| language-switching | 语言切换 | `handleContentLanguageSwitch()` |
| url-generation | URL 生成 | `generateContentUrl()` |

### 组件选择指南

| 场景 | 组件 | 原因 |
|------|------|------|
| 页头 | ContentLanguageIndicator | 紧凑、状态图标 |
| 侧边栏 | LanguageVersions | 详细、显示标题 |
| 移动端 | ContentLanguageIndicator | 节省空间 |

## Headless CMS 扩展性速查

### 架构优势

✅ **抽象层分离** - 内容查询集中在 `content-language-utils.ts`
✅ **接口一致性** - 统一的 TypeScript 接口
✅ **单一入口** - Contentlayer 导入集中管理

### 支持的 CMS

| CMS | 特点 | 适用场景 |
|-----|------|----------|
| Strapi | 开源、自托管 | 完全控制 |
| Sanity | 实时协作 | 团队协作 |
| Contentful | 企业级 | 全球分发 |

### 迁移策略

1. 创建适配器接口
2. 实现 CMS 特定适配器
3. 配置环境变量切换
4. 渐进式迁移

> 💡 当前 MDX + Contentlayer 方案已足够强大，除非有明确需求否则无需迁移

## 性能指标速查

### 关键指标

- **FCP** (First Contentful Paint) < 1.8s
- **LCP** (Largest Contentful Paint) < 2.5s
- **CLS** (Cumulative Layout Shift) < 0.1
- **FID** (First Input Delay) < 100ms

### 优化建议

- 使用 Next.js Image 组件
- 启用静态生成
- 配置适当的缓存头
- 压缩图片和资源

---

💡 **提示**: 将此文档加入书签，作为日常开发的快速参考！
