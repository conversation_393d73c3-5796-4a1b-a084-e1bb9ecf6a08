# MDX 内容完整指南

本指南涵盖了在 ShipAny 中使用 MDX 内容的所有内容，包括内容创建、provider 选择和 provider 之间的迁移。

## 目录

- [概述](#概述)
- [内容 Provider](#内容-provider)
- [创建 MDX 内容](#创建-mdx-内容)
- [Provider 选择指南](#provider-选择指南)
- [迁移指南](#迁移指南)
- [故障排除](#故障排除)

## 概述

ShipAny 使用 MDX（Markdown + JSX）进行内容管理，支持多个 provider。系统设计为与 provider 无关，同时保留每个 provider 的独特优势。

### 主要特性
- **多语言支持**：完整的国际化
- **自定义组件**：嵌入视频、图像和交互元素
- **SEO 优化**：自动元数据生成
- **Provider 灵活性**：无需代码更改即可在 provider 之间切换

## 内容 Provider

### 1. Contentlayer2（默认）

**最适合：** 标准 Next.js 部署（Vercel、自托管）

**特点：**
- 构建时 MDX 编译
- 最佳运行时性能
- TypeScript 类型生成
- 开发中的热重载

**安装：**
```bash
# 默认已安装
CONTENT_PROVIDER=contentlayer2  # 或留空
```

### 2. Next.js MDX Remote

**最适合：** Cloudflare Workers、边缘部署

**特点：**
- 运行时 MDX 解析
- 无文件系统依赖
- 较小的构建大小
- 动态内容支持

**安装：**
```bash
CONTENT_PROVIDER=next-mdx-remote
pnpm build:content
```

### Provider 比较

| 特性 | Contentlayer2 | NextMDXRemote |
|------|--------------|---------------|
| 编译 | 构建时 | 运行时 |
| 性能 | 运行时更快 | 构建更快 |
| 边缘支持 | 有限 | 完全 |
| 类型安全 | 生成类型 | 运行时验证 |
| 包大小 | 较大 | 较小 |
| 热重载 | ✅ | ✅ |
| Workers 优化 | ❌ | ✅ 分块加载 |

## 创建 MDX 内容

### 文件结构

```
content/
├── blogs/
│   ├── en/
│   │   ├── getting-started.mdx
│   │   └── advanced-features.mdx
│   └── zh/
│       └── getting-started.mdx
├── products/
│   ├── en/
│   │   └── ai-assistant.mdx
│   └── zh/
│       └── ai-assistant.mdx
└── case-studies/
    ├── en/
    │   └── success-story.mdx
    └── zh/
        └── success-story.mdx
```

### Frontmatter 结构

```yaml
---
title: "文章标题"
slug: "url-friendly-slug"  # 可选，默认为文件名
description: "SEO 的简短描述"
publishedAt: "2024-01-15"
author: "作者名称"
authorImage: "https://example.com/author.jpg"
coverImage: "https://example.com/cover.jpg"
featured: true
tags:
  - 教程
  - 入门
category: "教程"  # 可选
---
```

#### ⚠️ 重要：gray-matter 自动类型转换

gray-matter 库会自动转换某些 YAML 值类型。为避免意外的类型转换，请遵循以下规则：

**需要使用引号的情况：**
```yaml
# 时间格式 - 不加引号会转换为秒数
videoDuration: "5:30"     # 保持为字符串 "5:30"
videoDuration: 5:30       # ❌ 会转换为 330（秒）

# 版本号 - 不加引号会转换为数字
version: "1.0"           # 保持为字符串 "1.0"
version: 1.0             # ❌ 会转换为数字 1

# 时间 - 不加引号会转换为秒数
time: "09:00"            # 保持为字符串 "09:00"
time: 09:00              # ❌ 会转换为秒数

# 带前导零的代码
code: "001"              # 保持前导零
code: 001                # ❌ 会转换为数字 1
```

**自动转换规则：**
- 时间格式（如 `5:30`）→ 转换为秒数（330）
- 日期格式 → 转换为 Date 对象
- 布尔值（`true`/`false`）→ 转换为 boolean
- 数字 → 转换为 number

**最佳实践：**
- 对所有需要保持字符串格式的值使用引号
- 特别注意时间、版本号、代码等字段
- 在不确定时，使用引号总是更安全的选择

### 编写内容

#### 基本 Markdown

```markdown
# 一级标题
## 二级标题
### 三级标题

**粗体文本** 和 *斜体文本*

- 项目列表
- 另一个项目

1. 编号列表
2. 另一个项目

> 引用

`内联代码` 和代码块：

```javascript
const hello = "world"
```
```

#### 使用自定义组件

**图片：**
```markdown
![替代文本](https://example.com/image.jpg)
```

**视频：**
```markdown
<Video
  src="https://example.com/video.mp4"
  poster="https://example.com/poster.jpg"
  controls
/>

<YouTube
  videoId="dQw4w9WgXcQ"
  title="视频标题"
/>

<Bilibili
  bvid="BV1xx411c7mD"
  title="视频标题"
/>
```

**视频画廊：**
```markdown
<VideoGallery
  columns={2}
  videos={[
    {
      type: "video",
      src: "https://example.com/video1.mp4",
      poster: "https://example.com/poster1.jpg",
      title: "视频 1"
    },
    {
      type: "youtube",
      videoId: "dQw4w9WgXcQ",
      title: "视频 2"
    }
  ]}
/>
```

### 多语言内容

#### 文件命名
- 英文：`content/blogs/en/my-post.mdx`
- 中文：`content/blogs/zh/my-post.mdx`

#### Slug 一致性
为翻译内容使用相同的 frontmatter slug：

**英文版本：**
```yaml
---
title: "Getting Started"
slug: "getting-started"
---
```

**中文版本：**
```yaml
---
title: "快速入门"
slug: "getting-started"  # 相同的 slug！
---
```

## Provider 选择指南

### 何时使用 Contentlayer2

选择 Contentlayer2 当：
- ✅ 部署到 Vercel 或传统托管
- ✅ 你想要最佳运行时性能
- ✅ 你需要内容的 TypeScript 类型
- ✅ 内容不经常更改
- ✅ 构建时间不是问题

### 何时使用 NextMDXRemote

选择 NextMDXRemote 当：
- ✅ 部署到 Cloudflare Workers
- ✅ 你需要快速构建时间
- ✅ 内容经常更改
- ✅ 你想要较小的包大小
- ✅ 使用动态内容源

## 迁移指南

### 从 Contentlayer2 切换到 NextMDXRemote

1. **更新环境变量：**
   ```bash
   # .env 或 .env.local
   CONTENT_PROVIDER=next-mdx-remote
   ```

2. **构建内容：**
   ```bash
   pnpm build:content
   ```

3. **启动开发：**
   ```bash
   pnpm dev
   ```

就是这样！无需代码更改。

### 从 NextMDXRemote 切换到 Contentlayer2

1. **更新环境变量：**
   ```bash
   # .env 或 .env.local
   CONTENT_PROVIDER=contentlayer2
   # 或者只是删除该行（contentlayer2 是默认值）
   ```

2. **构建内容：**
   ```bash
   pnpm contentlayer build
   ```

3. **启动开发：**
   ```bash
   pnpm dev
   ```

### 迁移清单

- [ ] 更新 CONTENT_PROVIDER 环境变量
- [ ] 运行适当的构建命令
- [ ] 测试内容渲染
- [ ] 验证语言切换工作
- [ ] 检查 SEO 元数据生成
- [ ] 在生产环境中测试

## 故障排除

### 常见问题

#### 内容未找到（404）

**症状：** 博客/产品/案例研究页面返回 404

**解决方案：**
1. 检查文件命名：`content/[type]/[locale]/[slug].mdx`
2. 验证 frontmatter 中的 slug 与 URL 匹配
3. 运行构建命令：`pnpm build:content`
4. 检查控制台中的构建错误

#### 语言切换不工作

**症状：** 无法在语言之间切换

**解决方案：**
1. 确保两个语言版本都存在
2. 在所有版本的 frontmatter 中使用相同的 slug
3. 检查语言文件夹名称（en、zh）

#### 构建错误

**Contentlayer2 错误：**
```bash
# 清除缓存并重建
rm -rf .contentlayer
pnpm contentlayer build
```

**NextMDXRemote 错误：**
```bash
# 清除缓存并重建
rm -rf .mdx-compiled
pnpm build:content
```

#### 样式问题

**症状：** 内容看起来没有样式

**解决方案：** 确保你使用的是 Mdx 组件：
```tsx
import { Mdx } from '@/components/mdx'

// 正确
<Mdx content={blog} />

// 错误
<div>{blog.body.raw}</div>
```

#### 视频组件不显示（NextMDXRemote）

**症状：** 视频组件显示为文本而不是实际视频

**解决方案：**
1. 确保 markdown-renderer.tsx 包含 MDX 组件处理
2. 安装必要的插件：`pnpm add rehype-raw`
3. 验证组件转换逻辑正确
4. 检查浏览器控制台错误

### 性能提示

#### 对于 Contentlayer2
- 保持 MDX 文件在 100KB 以下
- 在嵌入之前优化图像
- 对重型组件使用延迟加载

#### 对于 NextMDXRemote
- 考虑缓存解析的内容
- 最小化自定义组件
- 对媒体资产使用 CDN

### 调试模式

启用详细日志记录：
```bash
# 对于构建问题
pnpm build:content --verbose

# 检查生成的文件
# Contentlayer2: .contentlayer/generated/
# NextMDXRemote: .mdx-compiled/
```

## Cloudflare Workers 优化

### 分块加载架构

NextMDXRemote provider 针对 Cloudflare Workers 环境进行了特殊优化，采用分块加载策略来解决 Workers 的限制。

#### 为什么需要分块加载？

Cloudflare Workers 环境的限制：
- ❌ 无文件系统 API (`fs.readFile` 不存在)
- ❌ 无法访问 `public/` 目录
- ❌ 无法运行时读取外部文件
- ✅ 只能使用静态导入的模块

#### 分块策略

构建时，内容被分为多个 TypeScript 文件：

```
src/services/content/providers/next-mdx-remote/
├── static-content.ts           # 主索引文件
├── static-content-blogs.ts     # 博客内容块
├── static-content-products.ts  # 产品内容块
├── static-content-case-studies.ts # 案例研究内容块
└── static-content-docs.ts      # 文档内容块
```

#### 懒加载机制

主索引文件使用动态导入和缓存：

```typescript
export async function getStaticContent(): Promise<Record<string, ContentItem>> {
  // 返回缓存的内容（如果可用）
  if (contentCache) {
    return contentCache
  }

  // 动态导入所有内容块
  const [blogs, products, caseStudies, docs] = await Promise.all([
    import('./static-content-blogs').catch(() => ({ default: {} })),
    import('./static-content-products').catch(() => ({ default: {} })),
    import('./static-content-case-studies').catch(() => ({ default: {} })),
    import('./static-content-docs').catch(() => ({ default: {} }))
  ])

  // 合并所有块
  contentCache = {
    ...blogs.default,
    ...products.default,
    ...caseStudies.default,
    ...docs.default
  }

  return contentCache
}
```

#### 性能优势

1. **按需加载** - 只有在需要时才加载内容块
2. **内存缓存** - 加载后的内容被缓存，避免重复导入
3. **错误容错** - 单个块失败不会影响其他块
4. **代码分割** - 减少单个文件大小，提高加载速度

#### 构建过程

运行 `pnpm build:content` 时：

1. 扫描所有 MDX 文件
2. 按内容类型分组
3. 生成独立的内容块文件
4. 创建主索引文件
5. 输出构建统计信息

```bash
[MDX Build] Generated chunk: static-content-blogs.ts (3 items)
[MDX Build] Generated chunk: static-content-products.ts (4 items)
[MDX Build] Generated chunk: static-content-case-studies.ts (2 items)
[MDX Build] Generated main index: static-content.ts
```

## 最佳实践

1. **内容组织**
   - 每个内容一个 MDX 文件
   - 使用一致的命名约定
   - 保持翻译同步

2. **Frontmatter**
   - 始终包含必需字段
   - 使用 ISO 日期（YYYY-MM-DD）
   - 保持描述在 160 个字符以下

3. **媒体资产**
   - 对图像和视频使用 CDN
   - 提供适当的替代文本
   - 优化文件大小

4. **组件使用**
   - 不要过度使用自定义组件
   - 在两个 provider 中测试组件
   - 为缺失的 props 提供回退

5. **SEO**
   - 编写描述性标题
   - 包含元描述
   - 使用语义标题

## 总结

ShipAny 的 MDX 系统通过其多 provider 架构提供灵活性和性能。无论你优先考虑使用 Contentlayer2 的构建时优化还是使用 NextMDXRemote 的运行时灵活性，系统都能适应你的需求，同时保持一致的开发者体验。

有关架构详细信息，请参阅 [CMS 架构设计](./CMS架构设计.md)。
有关媒体管理，请参阅 [媒体嵌入指南](./媒体嵌入指南.md)。