# 媒体嵌入指南

本指南涵盖了如何在 MDX 内容中嵌入和管理图像、视频和其他媒体。

## 目录

- [图像](#图像)
- [视频](#视频)
- [最佳实践](#最佳实践)
- [性能优化](#性能优化)
- [故障排除](#故障排除)

## 图像

### 基本图像嵌入

MDX 内容自动使用 Next.js Image 组件进行优化：

```markdown
![替代文本](https://example.com/image.jpg)
```

这会自动转换为：
```jsx
<Image
  src="https://example.com/image.jpg"
  alt="替代文本"
  width={800}
  height={400}
  className="rounded-lg border"
/>
```

### 图像来源

#### 外部 URL
```markdown
![产品截图](https://images.unsplash.com/photo-123?w=800&h=400&fit=crop)
```

#### CDN 图像
```markdown
![功能图像](https://cdn.example.com/feature.jpg)
```

#### 本地图像（不推荐）
为了更好的性能和部署灵活性，请使用 CDN 或外部 URL。

### 图像最佳实践

1. **始终提供替代文本** 以提高可访问性
2. **使用描述性文件名** 以改善 SEO
3. **优化尺寸** - 推荐：内容图像 800x400
4. **使用 WebP 格式** 以获得更好的压缩
5. **延迟加载** 使用 Next.js Image 自动实现

### 封面图像

在 frontmatter 中设置封面图像：
```yaml
---
title: "我的文章"
coverImage: "https://images.unsplash.com/photo-123?w=800&h=400&fit=crop"
---
```

## 视频

### 视频组件

ShipAny 为不同平台提供了多个视频组件：

#### 本地/CDN 视频

```jsx
<Video
  src="https://cdn.example.com/demo.mp4"
  poster="https://cdn.example.com/poster.jpg"
  controls
  autoPlay={false}
  loop={false}
  muted={false}
/>
```

**属性：**
- `src`（必需）：视频 URL
- `poster`：海报图像 URL
- `controls`：显示视频控件（默认：true）
- `autoPlay`：自动播放视频（默认：false）
- `loop`：循环播放视频（默认：false）
- `muted`：静音视频（默认：false）

#### YouTube 视频

```jsx
<YouTube
  videoId="dQw4w9WgXcQ"
  title="Rick Astley - Never Gonna Give You Up"
/>
```

**属性：**
- `videoId`（必需）：YouTube 视频 ID
- `title`：用于可访问性的视频标题

#### Bilibili 视频

```jsx
<Bilibili
  bvid="BV1xx411c7mD"
  title="视频标题"
/>
```

**属性：**
- `bvid`（必需）：Bilibili 视频 BV ID
- `title`：用于可访问性的视频标题

### 视频画廊

在网格中显示多个视频：

```jsx
<VideoGallery
  columns={2}
  videos={[
    {
      type: "video",
      src: "https://cdn.example.com/video1.mp4",
      poster: "https://cdn.example.com/poster1.jpg",
      title: "产品演示"
    },
    {
      type: "youtube",
      videoId: "dQw4w9WgXcQ",
      title: "教程"
    },
    {
      type: "bilibili",
      bvid: "BV1xx411c7mD",
      title: "中文教程"
    }
  ]}
/>
```

**属性：**
- `columns`：列数（1-3，默认：2）
- `videos`：视频对象数组

### 视频格式和编解码器

**推荐格式：**
- **MP4**：H.264 编解码器，最佳兼容性
- **WebM**：VP9 编解码器，更好的压缩
- **HLS**：用于流媒体（.m3u8）

**多格式示例：**
```jsx
<Video
  src="https://cdn.example.com/video.mp4"
  sources={[
    { src: "https://cdn.example.com/video.webm", type: "video/webm" },
    { src: "https://cdn.example.com/video.mp4", type: "video/mp4" }
  ]}
  poster="https://cdn.example.com/poster.jpg"
/>
```

## 最佳实践

### 图像优化

1. **尺寸指南：**
   - 封面图像：1200x630（Open Graph）
   - 内容图像：800x400
   - 缩略图：400x225
   - 作者头像：100x100

2. **格式选择：**
   - 照片：JPEG/WebP
   - 图形/标志：PNG/SVG
   - 动画：GIF/WebP

3. **压缩：**
   - 使用 Squoosh 或 TinyPNG 等工具
   - 目标每张图像 <200KB
   - 平衡质量与文件大小

### 视频优化

1. **分辨率：**
   - 主要视频 1080p
   - 内容视频 720p
   - 移动优化 480p

2. **比特率指南：**
   - 1080p：5-8 Mbps
   - 720p：2.5-5 Mbps
   - 480p：1-2.5 Mbps

3. **时长：**
   - 保持视频在 2 分钟以内
   - 使用海报图像进行预览
   - 考虑长页面的延迟加载

### CDN 配置

**推荐的 CDN 设置：**
```javascript
// next.config.js
module.exports = {
  images: {
    domains: [
      'images.unsplash.com',
      'cdn.example.com',
      'your-cdn.com'
    ],
    formats: ['image/avif', 'image/webp']
  }
}
```

## 性能优化

### 延迟加载

所有图像和视频默认都是延迟加载的。对于关键图像：

```jsx
<Image
  src="https://example.com/hero.jpg"
  alt="主要图像"
  priority={true}  // 立即加载
  width={1200}
  height={600}
/>
```

### 响应式图像

Image 组件自动生成响应式图像：

```markdown
![主要图像](https://example.com/hero.jpg)
```

生成：
- 多种尺寸（640w、750w、828w、1080w、1200w）
- WebP 和原始格式
- 正确的 srcset 属性

### 视频加载策略

1. **延迟加载：**
   ```jsx
   <Video
     src="https://cdn.example.com/demo.mp4"
     loading="lazy"
   />
   ```

2. **预加载元数据：**
   ```jsx
   <Video
     src="https://cdn.example.com/demo.mp4"
     preload="metadata"  // 仅加载视频元数据
   />
   ```

3. **渐进增强：**
   ```jsx
   <Video
     src="https://cdn.example.com/demo-hd.mp4"
     poster="https://cdn.example.com/poster.jpg"
     sources={[
       { src: "https://cdn.example.com/demo-sd.mp4", media: "(max-width: 768px)" }
     ]}
   />
   ```

## 故障排除

### 常见问题

#### 图像无法加载

**症状：** 损坏的图像图标，404 错误

**解决方案：**
1. 检查图像 URL 是否可访问
2. 验证域名在 next.config.js 中
3. 检查 CORS 问题
4. 确保正确的图像格式

#### 视频无法播放

**症状：** 黑色视频播放器，播放错误

**解决方案：**
1. 验证视频格式兼容性
2. 检查 CDN 上的 CORS 标头
3. 直接测试视频 URL
4. 确保正确的 MIME 类型

#### 加载缓慢

**症状：** 页面加载时间长

**解决方案：**
1. 优化媒体文件大小
2. 使用适当的格式
3. 实施延迟加载
4. 使用具有边缘位置的 CDN

### 平台特定问题

#### YouTube 嵌入问题
- 检查视频隐私设置
- 验证视频 ID 是否正确
- 确保允许 YouTube cookies

#### Bilibili 嵌入问题
- 某些地区可能会阻止 Bilibili
- 检查 BV ID 格式
- 考虑备选方案

### 调试清单

- [ ] 检查浏览器控制台的错误
- [ ] 验证媒体 URL 是否可访问
- [ ] 在不同设备/浏览器上测试
- [ ] 检查网络选项卡的失败请求
- [ ] 验证 MDX 语法
- [ ] 确保组件已导入

## 高级用法

### 自定义视频播放器

创建具有高级功能的自定义视频播放器：

```jsx
<Video
  src="https://cdn.example.com/course.mp4"
  poster="https://cdn.example.com/course-poster.jpg"
  controls
  controlsList="nodownload"
  onPlay={() => trackEvent('video_play')}
  onEnded={() => trackEvent('video_complete')}
  playsInline
  crossOrigin="anonymous"
>
  <track
    kind="captions"
    src="https://cdn.example.com/captions-zh.vtt"
    srcLang="zh"
    label="中文"
    default
  />
</Video>
```

### 自适应流媒体

对于大型视频，考虑 HLS 流媒体：

```jsx
<Video
  src="https://cdn.example.com/stream/playlist.m3u8"
  type="application/x-mpegURL"
  poster="https://cdn.example.com/poster.jpg"
/>
```

### 图像画廊

虽然没有内置功能，但你可以创建图像画廊：

```jsx
<div className="grid grid-cols-2 gap-4">
  ![图像 1](https://example.com/1.jpg)
  ![图像 2](https://example.com/2.jpg)
  ![图像 3](https://example.com/3.jpg)
  ![图像 4](https://example.com/4.jpg)
</div>
```

## 总结

有效的媒体管理可以增强用户体验和参与度。遵循这些指南以确保在所有设备和平台上的最佳性能和兼容性。

有关内容创建，请参阅 [MDX 内容完整指南](./MDX内容完整指南.md)。
有关架构详细信息，请参阅 [CMS 架构设计](./CMS架构设计.md)。