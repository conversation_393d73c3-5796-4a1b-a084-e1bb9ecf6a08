# Build outputs
.next
.vercel
.wrangler
.open-next
.contentlayer

# Development files
.vscode
.cursorrules
data
debug
TEMP

# Dependencies (will be installed in container)
node_modules

# Git and version control
.git
.gitignore

# Environment files (handled separately)
.env.local
.env.production
.env.development

# Deployment configs (will be copied when needed)
vercel.json
wrangler.toml

# Docker files (not needed in container)
Dockerfile
Dockerfile.dev
docker-compose.yml
docker-compose.*.yml

# Documentation
docs
README.md
LICENSE

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# OS generated files
.DS_Store
Thumbs.db
