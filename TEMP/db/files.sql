-- File Management System - Simplified & Refactored
-- This schema is optimized for flexibility, R2's architecture, and real-world use cases.
CREATE TABLE files (
    -- Core Identity
    id SERIAL PRIMARY KEY,
    file_id VARCHAR(255) UNIQUE NOT NULL,

    -- Ownership & Context
    user_uuid VARCHAR(255) NOT NULL,
    task_id VARCHAR(255), -- Optional: for AI-generated files or other task associations

    -- Storage & Naming
    storage_key VARCHAR(500) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_size BIGINT NOT NULL,

    -- Classification & Access Control (NEW)
    file_category VARCHAR(50) NOT NULL,
    access_level VARCHAR(50) NOT NULL,

    -- Lifecycle & Timestamps
    status VARCHAR(50) NOT NULL DEFAULT 'active', -- pending, active, deleted, expired
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMPTZ, -- Optional: for auto-cleanup of temporary files

    -- Extensible Metadata
    metadata JSONB,

    -- Foreign Key Constraints
    FOREIGN KEY (user_uuid) REFERENCES users(uuid) ON DELETE CASCADE,
    FOREIGN KEY (task_id) REFERENCES ai_tasks(task_id) ON DELETE SET NULL
);

-- Indexes for optimal performance
CREATE INDEX idx_files_user_uuid ON files(user_uuid);
CREATE INDEX idx_files_task_id ON files(task_id);
CREATE INDEX idx_files_status ON files(status);
CREATE INDEX idx_files_file_category ON files(file_category);
CREATE INDEX idx_files_access_level ON files(access_level);
CREATE INDEX idx_files_created_at ON files(created_at);
CREATE INDEX idx_files_expires_at ON files(expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX idx_files_storage_key ON files(storage_key);

-- Composite index for common user file queries
CREATE INDEX idx_files_user_category_status ON files(user_uuid, file_category, status);

-- Comments for clarity
COMMENT ON TABLE files IS 'Manages file records with a flexible category and access control system, optimized for R2-like storage.';
COMMENT ON COLUMN files.storage_key IS 'The full object key in the cloud storage bucket (e.g., "user-uploads/2024/07/user_uuid/file_id.png").';
COMMENT ON COLUMN files.file_category IS 'Business-logic category for the file (e.g., "avatars", "task-output").';
COMMENT ON COLUMN files.access_level IS 'Controls access rules (e.g., "public", "private", "restricted").';
COMMENT ON COLUMN files.expires_at IS 'Timestamp for automatic cleanup, useful for temporary files.';

