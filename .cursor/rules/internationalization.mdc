---
description: Internationalization patterns and best practices
globs: src/i18n/**,src/app/[locale]/**
alwaysApply: false
---
# 国际化规范

## 技术栈
- 使用 **next-intl** 进行国际化管理
- 支持多语言路由：`src/app/[locale]/`
- 配置文件位于 `src/i18n/` 目录

## 实现原则
- **服务端渲染** - 利用 Next.js 15 的 Server Components 进行 SSR
- **动态路由** - 通过 `[locale]` 参数支持多语言
- **类型安全** - 为翻译键提供 TypeScript 类型支持
- **性能优化** - 按需加载语言包

## 使用规范
- **翻译键命名** - 使用点分隔的层级结构（如 `common.buttons.save`）
- **组件内使用** - 通过 `useTranslations()` Hook 获取翻译函数
- **服务端组件** - 使用 `getTranslations()` 获取翻译
- **默认语言** - 设置合理的 fallback 语言

## 文件组织
```
src/i18n/
├── locales/           # 语言包文件
│   ├── en.json       # 英文
│   ├── zh.json       # 中文
│   └── ...
├── config.ts         # 国际化配置
└── request.ts        # 服务端翻译配置
src/middleware.ts     # Next.js 路由中间件
```

## 最佳实践
- **一致性** - 保持翻译键的命名一致性
- **上下文** - 为翻译提供足够的上下文信息
- **复数形式** - 正确处理复数和变量插值
- **测试** - 确保所有语言版本的功能正常

参考配置：[next.config.mjs](mdc:next.config.mjs)、[src/middleware.ts](mdc:src/middleware.ts)、[src/i18n/config.ts](mdc:src/i18n/config.ts)

