---
description: State management patterns and React 19 best practices
globs: src/contexts/**,src/hooks/**,src/providers/**
alwaysApply: false
---
# 状态管理规范

## 状态管理策略
- **就近原则** - 状态尽可能靠近使用的组件
- **服务端优先** - 优先使用 Server Components，减少客户端状态
- **Context 适度使用** - 避免过度使用 Context，防止不必要的重渲染
- **状态分层** - 区分本地状态、共享状态和服务端状态

## React 19 状态特性
- **并发特性** - 使用 `startTransition` 处理非紧急状态更新
- **自动批处理** - 利用 React 19 的自动批处理优化性能
- **Suspense 集成** - 配合 Suspense 处理异步状态
- **错误边界** - 使用错误边界处理状态错误

## 状态类型管理
- **本地状态** - 使用 `useState` 处理组件内部状态
- **表单状态** - 使用 `react-hook-form` 管理表单状态
- **服务端状态** - 通过 Server Components 或 API 路由获取
- **全局状态** - 使用 Context 或状态管理库（如需要）

## 最佳实践
- **状态结构扁平化** - 避免深层嵌套的状态结构
- **不可变更新** - 使用不可变的方式更新状态
- **状态派生** - 使用 `useMemo` 计算派生状态
- **副作用管理** - 使用 `useEffect` 合理处理副作用

## 性能优化
- **避免过度渲染** - 使用 `useCallback` 和 `useMemo` 优化
- **状态分割** - 将大的状态对象分割为更小的部分
- **选择性订阅** - 只订阅需要的状态变化
- **懒初始化** - 对复杂的初始状态使用懒初始化

参考实现：[src/contexts/](mdc:src/contexts)、[src/hooks/](mdc:src/hooks)、[src/providers/](mdc:src/providers)


