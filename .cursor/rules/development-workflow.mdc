---
description: Development workflow and quality standards
globs: 
alwaysApply: true
---
# 开发流程规范

## 重构原则
- **分析优先** - 大型重构前先进行充分分析
- **规划讨论** - 列出重构方案和可选项，团队讨论确认
- **彻底重构** - 重构时不考虑向后兼容，追求代码质量
- **渐进实施** - 如果遇到大型重构，根据需要，询问是否分解为可管理的小步骤

## 代码质量
- **模块化设计** - 代码结构清晰，组件可复用
- **可维护性** - 便于后续维护和功能扩展
- **统一风格** - 与项目现有代码风格保持一致
- **充分测试** - 重要功能需要有相应的测试覆盖

## 开发实践
- **英文注释** - 代码注释和日志使用英文，内容详细
- **中文交流** - 问题讨论和方案说明使用中文
- **包管理** - 统一使用 pnpm 作为包管理器
- **依赖管理** - 及时更新依赖，移除未使用的包

## 技术决策
- **现有优先** - 优先使用项目已有的技术栈和工具
- **必要引入** - 只在确实需要时引入新的库和技术
- **兼容性优先** - 优先考虑跨平台兼容性设计
- **平台适配** - 当兼容性设计大幅增加难度或严重影响性能时，优先适配Cloudflare
- **Edge Runtime** - 选择库和实现方案时考虑Edge Runtime限制
- **灵活适应** - 根据项目发展需要灵活调整技术选型

## 协作规范
- **代码审查** - 重要变更需要代码审查
- **文档更新** - 及时更新相关文档和注释
- **知识分享** - 分享技术决策和解决方案
- **持续改进** - 定期回顾和优化开发流程

## 工具使用
- **开发环境** - 使用 Turbopack 提升开发体验
- **代码格式** - 使用 ESLint 和 Prettier 保持代码一致性
- **类型检查** - 充分利用 TypeScript 的类型检查能力
- **性能监控** - 定期进行性能分析和优化

