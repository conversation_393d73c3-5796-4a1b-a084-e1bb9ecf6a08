---
description: Performance optimization guidelines and best practices
globs: 
alwaysApply: false
---
# 性能优化规范

## Next.js 15 优化
- **App Router** - 充分利用 Server Components 减少客户端 JavaScript
- **Edge Runtime** - 优先使用Edge兼容的API和库
- **图片优化** - 使用 `next/image` 组件，配置 `remotePatterns`
- **Bundle 分析** - 定期使用 `ANALYZE=true pnpm build` 分析包大小

## React 19 特性
- **并发特性** - 合理使用 Suspense 和 startTransition
- **服务端组件** - 优先使用 Server Components，减少客户端渲染
- **流式渲染** - 利用 Streaming 提升首屏加载速度
- **自动批处理** - 利用 React 19 的自动批处理优化状态更新

## 代码优化
- **懒加载** - 使用 `React.lazy()` 和动态导入分割代码
- **缓存策略** - 合理使用 `useMemo`、`useCallback` 和 `React.memo()`
- **避免重渲染** - 优化依赖数组，避免不必要的组件重渲染
- **状态管理** - 将状态尽可能靠近使用的组件

## 资源优化
- **图片处理** - 使用 WebP 格式，配置适当的尺寸和质量
- **CSS 优化** - 利用 Tailwind CSS 的 purge 功能移除未使用的样式
- **JavaScript 优化** - 移除未使用的依赖，使用 Tree Shaking
- **CDN 配置** - 静态资源使用 CDN 加速

## 监控和测试
- **Core Web Vitals** - 关注 LCP、FID、CLS 等关键指标
- **性能监控** - 集成性能监控工具（如 Vercel Analytics）
- **定期审计** - 使用 Lighthouse 进行性能审计
- **压力测试** - 对关键功能进行性能压力测试

参考配置：[next.config.mjs](mdc:next.config.mjs)、[package.json](mdc:package.json)



