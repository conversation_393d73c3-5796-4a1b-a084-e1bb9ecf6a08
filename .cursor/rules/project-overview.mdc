---
description: Project overview and technology stack
globs: 
alwaysApply: true
---
# 项目概览

这是一个基于 Next.js 15 的现代化 TypeScript 应用，采用分层架构设计，主要技术栈包括：

## 核心技术

- **Next.js 15** - 使用 App Router 架构和 React 19
- **TypeScript** - 严格类型检查，全项目覆盖
- **pnpm** - 高效的包管理器
- **Tailwind CSS 4** - 现代化CSS框架
- **Shadcn UI** - 高质量组件库基础

## 数据库与ORM

- **Drizzle-ORM** - 类型安全的数据库ORM
- **PostgreSQL** - 主数据库
- **Drizzle-Kit** - 数据库迁移和管理工具
- 完整的数据库表设计：用户、订单、任务、文件、积分、API密钥等

## AI & SDK 集成

- **多AI提供商支持** - OpenAI、DeepSeek、Replicate、OpenRouter
- **AI SDK** - Vercel AI SDK 统一接口
- **任务系统** - 支持异步AI任务处理
- **文件管理** - 完整的文件上传、存储和管理系统

## 身份认证与支付

- **NextAuth 5.0 beta** - 现代化认证系统
- **Stripe** - 完整的支付和订阅管理
- **多登录方式** - 支持多种社交登录

## 国际化与UI

- **next-intl** - 完整的国际化支持
- **next-themes** - 深色/浅色主题切换
- **Framer Motion** - 流畅动画效果
- **Sonner** - 优雅的通知系统

## 内容与媒体

- **MDX支持** - 动态内容渲染
- **文件存储** - AWS S3 集成
- **图片处理** - 完整的媒体管理方案

## 项目架构特点

- **分层设计** - Models/Services/Components 三层架构
- **类型安全** - 端到端TypeScript类型保护
- **模块化** - 高度模块化，便于扩展和维护
- **现代化开发体验** - Turbopack开发，ESLint/Prettier代码规范

## 部署策略

- **双分支架构** - main分支(Vercel等)，cloudflare分支(Workers)
- **当前环境** - Cloudflare Workers / Edge Runtime
- **设计原则** - 优先考虑跨平台兼容性设计
- **适配原则** - 当兼容性设计大幅增加难度或严重影响性能时，优先适配Cloudflare
- **运行时考虑** - 代码实现需考虑Edge Runtime限制

参考核心文件：[package.json](mdc:package.json)、[src/db/schema.ts](mdc:src/db/schema.ts)
