/**
 * Type declarations for next-contentlayer2
 * 
 * This file provides TypeScript type definitions for the next-contentlayer2 library.
 * It must remain in the /types directory as per TypeScript conventions for module augmentation.
 * 
 * Purpose:
 * - Extends type definitions for next-contentlayer2 which is a community fork of Contentlayer
 * - Provides proper typing for withContentlayer and useMDXComponent functions
 * - Ensures type safety when using Contentlayer2 with Next.js
 * 
 * Related files:
 * - /src/services/content/providers/contentlayer2/ - Provider implementation
 * - /contentlayer.config.ts - Contentlayer configuration
 */

// Type declarations for next-contentlayer2
declare module 'next-contentlayer2' {
  import { NextConfig } from 'next'
  
  export function withContentlayer(config: NextConfig): NextConfig
}

declare module 'next-contentlayer2/hooks' {
  import { FC } from 'react'
  
  type MDXContentProps = {
    [props: string]: unknown
    components?: Record<string, React.ComponentType<any>>
  }
  
  export function useMDXComponent(code: string, globals?: Record<string, unknown>): FC<MDXContentProps>
}
